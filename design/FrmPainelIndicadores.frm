object FrmPainelIndicadores: TFForm
  Left = 44
  Top = 160
  ActiveControl = vboxPrincipal
  Caption = 'Indicadores'
  ClientHeight = 663
  ClientWidth = 1014
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '382028'
  ShortcutKeys = <>
  InterfaceRN = 'PainelIndicadoresRN'
  Access = False
  ChangedProp = 
    'FrmPainelIndicadores.Width;'#13#10'FrmPainelIndicadores.Height;'#13#10#13#10'Frm' +
    'PainelIndicadores.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 1014
    Height = 663
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object pgIndicadores: TFPageControl
      Left = 0
      Top = 0
      Width = 1009
      Height = 658
      ActivePage = tabIndicadores
      TabOrder = 0
      TabPosition = tpTop
      OnChange = pgIndicadoresChange
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabLista: TFTabsheet
        Caption = 'Painel'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FHBox11: TFHBox
          Left = 0
          Top = 0
          Width = 1001
          Height = 630
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vboxLista: TFVBox
            Left = 0
            Top = 0
            Width = 509
            Height = 530
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox2: TFHBox
              Left = 0
              Top = 0
              Width = 475
              Height = 49
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object cbbGrupoLista: TFCombo
                Left = 0
                Top = 0
                Width = 145
                Height = 21
                LookupTable = tbComboGrupo
                LookupKey = 'ID'
                LookupDesc = 'DESCRICAO_GRUPO'
                Flex = True
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = True
                HideClearButtonOnNullValue = False
                OnChange = cbbGrupoListaChange
                OnClearClick = cbbGrupoListaClearClick
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
              object hboxAtivoInativo: TFHBox
                Left = 145
                Top = 0
                Width = 170
                Height = 37
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hboxAtivo: TFHBox
                  Left = 0
                  Top = 0
                  Width = 80
                  Height = 32
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  OnClick = hboxAtivoClick
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox5: TFHBox
                    Left = 0
                    Top = 0
                    Width = 13
                    Height = 26
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftMin
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel1: TFLabel
                    Left = 13
                    Top = 0
                    Width = 35
                    Height = 16
                    Caption = 'Ativo'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object FHBox6: TFHBox
                    Left = 48
                    Top = 0
                    Width = 13
                    Height = 26
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftMin
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object hboxInativo: TFHBox
                  Left = 80
                  Top = 0
                  Width = 80
                  Height = 32
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  OnClick = hboxInativoClick
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox8: TFHBox
                    Left = 0
                    Top = 0
                    Width = 13
                    Height = 26
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftMin
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel2: TFLabel
                    Left = 13
                    Top = 0
                    Width = 46
                    Height = 16
                    Caption = 'Inativo'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object FHBox9: TFHBox
                    Left = 59
                    Top = 0
                    Width = 13
                    Height = 26
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftMin
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
              end
            end
            object gridListaPainel: TFGrid
              Left = 0
              Top = 50
              Width = 474
              Height = 304
              TabOrder = 1
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbListaPainel
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              Columns = <
                item
                  Expanded = False
                  FieldName = 'ID'
                  Font = <>
                  Title.Caption = 'Id'
                  Width = 70
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{534EABA1-B1DA-40B3-AC79-B8051ED160C2}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'DESCRICAO'
                  Font = <>
                  Title.Caption = 'Painel'
                  Width = 40
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{E99A09C6-DE09-414A-AE08-703EF9DBD650}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'DESCRICAO_GRUPO'
                  Font = <>
                  Title.Caption = 'Grupo'
                  Width = 140
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{D82840DB-734B-41BD-8C55-55F177D8171A}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
          end
          object FVBox1: TFVBox
            Left = 509
            Top = 0
            Width = 80
            Height = 254
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object btnNovo: TFButton
              Left = 0
              Top = 0
              Width = 75
              Height = 60
              Caption = 'Novo'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Layout = blGlyphTop
              ParentFont = False
              TabOrder = 0
              OnClick = btnNovoClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
                5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
                2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
                33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
                6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
                5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
                9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
                CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
                84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
                E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
                BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
                603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
                84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
                281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
                0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
                0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
                C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
                37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
                D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
                768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
                0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
                19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
                B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
                74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
                3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
                AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
                92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
                69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
                AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
                08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
                71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
                C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
                44AE426082}
              ImageId = 6
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object btnAlterar: TFButton
              Left = 0
              Top = 61
              Width = 75
              Height = 60
              Caption = 'Alterar'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Layout = blGlyphTop
              ParentFont = False
              TabOrder = 1
              OnClick = btnAlterarClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA
                5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31
                ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E
                C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E
                86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD
                2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74
                EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2
                30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7
                38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584
                AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979
                0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875
                08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80
                6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618
                713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524
                87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5
                B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045
                4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36
                5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A
                7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5
                42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF
                984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102
                DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B
                1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2
                DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E
                166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42
                6082}
              ImageId = 7
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object btnExcluir: TFButton
              Left = 0
              Top = 122
              Width = 75
              Height = 60
              Caption = 'Excluir'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              Layout = blGlyphTop
              ParentFont = False
              TabOrder = 2
              OnClick = btnExcluirClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
                426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
                DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
                DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
                FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
                C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
                8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
                C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
                BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
                93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
                6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
                A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
                27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
                ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
                7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
                3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
                D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
                A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
                8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
                D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
                5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
                9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
                7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
                102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
                005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
                C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
                3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
                3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
                9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
                072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
                6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
                9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
                4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
                042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
                003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
                72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
                29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
                CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
                32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
                CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
                00000049454E44AE426082}
              ImageId = 8
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
          end
        end
      end
      object tabIndicadores: TFTabsheet
        Caption = 'Indicadores'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vboxIndicadores: TFVBox
          Left = 0
          Top = 0
          Width = 1001
          Height = 630
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 3
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox4: TFHBox
            Left = 0
            Top = 0
            Width = 677
            Height = 75
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 25
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel4: TFLabel
              Left = 0
              Top = 0
              Width = 187
              Height = 23
              Caption = 'Indicadores do Painel:'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -19
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object lblPainel: TFLabel
              Left = 187
              Top = 0
              Width = 167
              Height = 23
              Caption = 'Selecione um Painel'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = 15174738
              Font.Height = -19
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'DESCRICAO'
              Table = tbListaPainel
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox10: TFHBox
            Left = 0
            Top = 76
            Width = 676
            Height = 301
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object gridIndicadores: TFGrid
              Left = 0
              Top = 0
              Width = 496
              Height = 252
              TabOrder = 0
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbGridIndicadores
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              Columns = <
                item
                  Expanded = False
                  FieldName = 'ID'
                  Font = <>
                  Title.Caption = 'Id'
                  Width = 70
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{D9666348-8FA5-44DC-8C1A-E8F1D05715C2}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'DESCRICAO'
                  Font = <>
                  Title.Caption = 'Indicador'
                  Width = 120
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{12F18F17-4B0F-4E69-9A86-D99893F1EC39}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'DISPLAY_ORDER'
                  Font = <>
                  Title.Caption = 'Ordem'
                  Width = 65
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{2B65CB2C-D3E2-4345-9530-9D2CCBF23B59}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
            object FVBox2: TFVBox
              Left = 496
              Top = 0
              Width = 80
              Height = 254
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object btnNovoIndicadores: TFButton
                Left = 0
                Top = 0
                Width = 75
                Height = 60
                Caption = 'Novo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 0
                OnClick = btnNovoIndicadoresClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
                  5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
                  2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
                  33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
                  6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
                  5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
                  9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
                  CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
                  84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
                  E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
                  BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
                  603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
                  84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
                  281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
                  0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
                  0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
                  C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
                  37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
                  D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
                  768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
                  0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
                  19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
                  B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
                  74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
                  3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
                  AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
                  92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
                  69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
                  AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
                  08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
                  71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
                  C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
                  44AE426082}
                ImageId = 6
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object btnExcluirIndicadores: TFButton
                Left = 0
                Top = 61
                Width = 75
                Height = 60
                Caption = 'Excluir'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 1
                OnClick = btnExcluirIndicadoresClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
                  426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
                  DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
                  DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
                  FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
                  C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
                  8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
                  C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
                  BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
                  93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
                  6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
                  A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
                  27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
                  ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
                  7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
                  3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
                  D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
                  A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
                  8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
                  D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
                  5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
                  9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
                  7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
                  102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
                  005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
                  C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
                  3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
                  3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
                  9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
                  072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
                  6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
                  9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
                  4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
                  042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
                  003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
                  72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
                  29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
                  CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
                  32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
                  CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
                  00000049454E44AE426082}
                ImageId = 8
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object btnOrderAcima: TFButton
                Left = 0
                Top = 122
                Width = 75
                Height = 60
                Hint = 'Alterar Ordem Acima'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 2
                OnClick = btnOrderAcimaClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000002F84944415478DAB5944D4C134114C7FFD3EDC7B2B4B4209452BE0481
                  44E1E081281724A021018D44A31EF426897250A30723F182478D7A311E682207
                  231A120E2A7AC16022881F098990683406A950A45068615B68BBD06DC76E0B2D
                  94F0211B5F76F226336FDF6FE67D0CC17F1692B8E072B92EF8FD7E8BCFE783D7
                  EB85A40541888C402000AF7F097A1D078D46039665C171DCEAD1999191716653
                  40A2389DCEDB921645F1C6D71FE368BAD981AB8DD5387DAC1C0CC3742A95CAE6
                  D4D454EBB66FB091FC1C71D01317DB20B299508B73E878700E6525595BFEBF6D
                  40DDF9C7749467E09B1905979E0775D08B67F74FA2B43893C802BCE8B3D147ED
                  9F60F3AA303F390A51F08351A9A0CB2A002378F0F4DE71941665901D036A9A5E
                  518FA8C0C28C1382C7135DA414CA7092B52613B44AA0BFAD616780EACBDDD4BB
                  44E1E317E09F75C7AD695469F45AA8B51C741A05DE5B8E927F0248CE85204160
                  5184DBEE5C6F49A58F22C9A005A74B8232BCFFC1524FB605A8BAD44D4528A448
                  C0659BDEF81812246C9496930E1A0C41C510F4B7D6910D01BD8353B4B975100A
                  250336598329AB03A150286646482C05F1382D4332771B21F816C128083EAE82
                  C426ED6FACD4F27218629082D3B208854F44A39EC2108A5907BFEE0269463D54
                  1AD59AB5058F0FC5E664B4DFAA226B0047AEF550DE1D7E12BC42E4A8243C584E
                  0D35AB4652921AD3937C4292298C59062CCC0B6B42B6B418884C734C5A74DDA9
                  215B96E9A12B3D54ABD3C03EC127FA8739DB80C540086FEF56EDBC0F2440BA49
                  0FFBD4FCBA0ACA36A5C0CDFBE403B2F37761DCEEC1EA1849E9C935A760C6E191
                  0F282C3162EC8F27EE7EB980F27352306E9B930F282D33C36A5BDDC9349287C2
                  3C3D86879DF20115E5B9F8FE9B8F5456D47F3444FB0AF4F8F2CD211F505B5980
                  21AB544524167F09B27F8F01EF0626E4034ED51663E057BC0F5600078A0CE8EA
                  1B930F686CD88BFE617EF9FC5191BABBB2C48027DD23F20087AFF7D2E7CD151B
                  EE9F7D3884D72D07770EA86FF94CC9F2ABB2D2F7925E2955493603FC05A33A5C
                  28F4137AFF0000000049454E44AE426082}
                ImageId = 382022
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object btnOrderAbaixo: TFButton
                Left = 0
                Top = 183
                Width = 75
                Height = 60
                Hint = 'Alterar Ordem Abaixo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 3
                OnClick = btnOrderAbaixoClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F80000030E4944415478DAB5944D68134114C7DF6E36DD6D3E273169DAF4236D
                  6A44C443B0D47A28A550516B5550D0938A07F5E4552F82949E447AF1E2571511
                  443C7850A1158A50B407DB52A48855A8F623FD4A0CB126ED76B3C9661B6792DD
                  344D8C0D06FFF078BB9337FFDFCCDB9950C9641288288A0255EA18D1F19EB1CC
                  0B29517F4AE7642AF777B750B91EAA0F550CE0D9152F14D2C99B23F0F656DBBF
                  030E5E7B9F3C77B81186A7C240D3E91A2AB57680560F8247AFBEC2BBDB1DA501
                  4EB4B960EC7B38559329C325CD3B11BC18FC563AA0BDB91A26A6B30114E93E78
                  DD080687674B07ECDBEB802FB39194B95A476AF6342018195F281DE0F1D86066
                  3E02947A949416B9EBCC30F979B974406D9D057C8BAB90E30FAE1A13CC4C054B
                  07D81D2658585ECDF45F45D43A4DB0E4FB593AC08C74B014C000629EE5E1AC34
                  422810291DC06A69585E0A43B63FA9705623E0D762C5015A2E0DCC2524D9459E
                  CB58ED96951A8C1C04FD61C8255454218846E31017E3200AF1F4C270707A0E18
                  AD26FCE1DE11CB961D345D783DCE94314D06936ECB4AA498042BC108E4CAEA40
                  9BB71BCFA7353408BC4840713C741D037AF35AD4727940C289E1742CFC980BA6
                  C733BBD9BCC99B5D4C62080D956E0788EB31902519F7054E8DDCEF7C59F01BEC
                  BFD82FE27796C22B5A590CE5407284A7EFA8AB4881137149187D7054BFED47DE
                  75EC29323B50006F9915D6A2100DF379274835373B6DA0651990E3D2EAD8C32E
                  7351A788C8D3F904992AD0BC4CD1C6382F402CC2AB5DCA98975BCDA043060059
                  CE33DF1640D478E831D25B0D33345B66E1030148C46299ABCC994C60B0DB00C3
                  FD9F9E9F76FEB17BDB01881A3AEE7A292A39A4B7D9D19A7F167F44099F807230
                  56D5031FF04FD8DDF57746FBBAFAFE1940E46AEBF5D20C33C499AD4808CD83CE
                  5E0F42C83F81DBD4EE1BBE1A2934AF680051CD81EE34045561888FDCBAD6C591
                  9EC9BFCD2904200F1A25181C5A256B6CBBCF9E675874438C4C9F09CFBDF988C7
                  12CA1C922525CB3836D2FEF90062CAE128C7A1CB0932CE329C0525C45FBC6228
                  2A21E0585772548918F6DEA0B2FFD8FE877E03F621D7E0BAEED8FE0000000049
                  454E44AE426082}
                ImageId = 4600304
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
          end
        end
      end
      object tabAcessoFuncao: TFTabsheet
        Caption = 'Acesso por Fun'#231#227'o'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object vboxAcessoFuncao: TFVBox
          Left = 0
          Top = 0
          Width = 1001
          Height = 630
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 3
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox7: TFHBox
            Left = 0
            Top = 0
            Width = 677
            Height = 75
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 25
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel5: TFLabel
              Left = 0
              Top = 0
              Width = 150
              Height = 23
              Caption = 'Fun'#231#227'o do Painel:'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -19
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object FLabel6: TFLabel
              Left = 150
              Top = 0
              Width = 167
              Height = 23
              Caption = 'Selecione um Painel'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = 15174738
              Font.Height = -19
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              FieldName = 'DESCRICAO'
              Table = tbListaPainel
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox3: TFHBox
            Left = 0
            Top = 76
            Width = 676
            Height = 301
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FGrid1: TFGrid
              Left = 0
              Top = 0
              Width = 496
              Height = 252
              TabOrder = 0
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbGridAcessoFuncao
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              Columns = <
                item
                  Expanded = False
                  FieldName = 'ID'
                  Font = <>
                  Title.Caption = 'Id'
                  Width = 80
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{5380A707-2ED7-431F-9E49-06D6D3CFDDAC}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'FUNCAO'
                  Font = <>
                  Title.Caption = 'Fun'#231#227'o'
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{22467300-22D9-4957-84BB-5272D800E094}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
            object FVBox5: TFVBox
              Left = 496
              Top = 0
              Width = 80
              Height = 254
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object btnNovoAcessoFunc: TFButton
                Left = 0
                Top = 0
                Width = 75
                Height = 60
                Caption = 'Novo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 0
                OnClick = btnNovoAcessoFuncClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
                  5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
                  2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
                  33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
                  6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
                  5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
                  9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
                  CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
                  84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
                  E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
                  BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
                  603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
                  84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
                  281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
                  0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
                  0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
                  C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
                  37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
                  D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
                  768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
                  0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
                  19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
                  B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
                  74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
                  3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
                  AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
                  92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
                  69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
                  AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
                  08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
                  71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
                  C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
                  44AE426082}
                ImageId = 6
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object btnExcluirAcessoFunc: TFButton
                Left = 0
                Top = 61
                Width = 75
                Height = 60
                Caption = 'Excluir'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 1
                OnClick = btnExcluirAcessoFuncClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
                  426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
                  DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
                  DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
                  FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
                  C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
                  8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
                  C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
                  BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
                  93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
                  6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
                  A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
                  27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
                  ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
                  7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
                  3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
                  D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
                  A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
                  8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
                  D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
                  5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
                  9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
                  7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
                  102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
                  005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
                  C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
                  3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
                  3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
                  9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
                  072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
                  6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
                  9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
                  4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
                  042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
                  003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
                  72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
                  29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
                  CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
                  32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
                  CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
                  00000049454E44AE426082}
                ImageId = 8
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
          end
        end
      end
    end
  end
  object FPopupMenu1: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 724
    Top = 30
    object FMenuItem1: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Novo'
      ImageIndex = 6
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{82913876-F471-4681-BFAE-E98558825E03}'
    end
    object FMenuItem2: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Alterar'
      ImageIndex = 7
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{86FAB3D7-B311-4EEC-BFFF-B2E216A04523}'
    end
    object FMenuItem3: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Pesquisar'
      ImageIndex = 13
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{4B6D2DF7-3415-4008-B409-A82FADD8C790}'
    end
    object FMenuItem4: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Excluir'
      ImageIndex = 8
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{C90C4E81-ED6F-413D-85A2-64D234FA981F}'
    end
    object FMenuItem5: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Salvar'
      ImageIndex = 4
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{12250D43-CA9F-413A-B213-24459190538A}'
    end
    object FMenuItem6: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Cancelar'
      ImageIndex = 9
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{16ADAA9A-73BA-485E-8435-14B98938B6FB}'
    end
    object FMenuItem7: TFMenuItem
      Caption = 'subir'
      ImageIndex = 382022
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
    end
    object FMenuItem8: TFMenuItem
      Caption = 'descer'
      ImageIndex = 4600304
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
    end
  end
  object tbComboGrupo: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BSC_COMBO_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 263
  end
  object tbListaPainel: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BSC_LISTA_PAINEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 252
  end
  object tbPainel: TFTable
    FieldDefs = <
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_VALOR_NA_META'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Valor Na Meta'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_LAYOUT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Layout'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FREQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frequencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BSC_PAINEL'
    Cursor = 'BSC_PAINEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38203'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 270
  end
  object tbPainelAcessoFuncao: TFTable
    FieldDefs = <
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BSC_PAINEL_ACESSO_FUNCAO'
    Cursor = 'BSC_PAINEL_ACESSO_FUNCAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38204'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 270
  end
  object tbPainelIndicador: TFTable
    FieldDefs = <
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_INDICADOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Indicador'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PONTUACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pontua'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DISPLAY_ORDER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Display Order'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BSC_PAINEL_INDICADOR'
    Cursor = 'BSC_PAINEL_INDICADOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38205'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 252
  end
  object tbGridIndicadores: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Indicador'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Formato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DISPLAY_ORDER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Display Order'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BSC_GRID_INDICADORES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38207'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 259
  end
  object tbGridAcessoFuncao: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BSC_GRID_ACESSO_FUNCAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38208'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 258
  end
  object tbPainelSemaforo: TFTable
    FieldDefs = <
      item
        Name = 'ID_SEMAFORO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Semaforo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERCENTUAL_ATE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Percentual Ate'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BSC_PAINEL_SEMAFORO'
    Cursor = 'BSC_PAINEL_SEMAFORO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382028;38209'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 263
  end
end
