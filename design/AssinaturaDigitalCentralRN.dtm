object AssinaturaDigitalCentralRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '455010'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbCentralAssinaturaGrid: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{0DC1BC68-23D6-4A04-8BBF-C5B73BA348DB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{07A60C54-65A1-4D84-BB9E-EE8867A5CEDB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{9A0A93E2-16E4-43A7-99B9-7F0F306DB441}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Codigo'
        GUID = '{7BB05CB3-5483-46C2-B0E7-3F13B78F3FCA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Status'
        GUID = '{0D4BD755-62FD-4045-BF5B-7170070037DB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENVIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Envio'
        GUID = '{78B0E4EC-905B-43D3-B0B9-91F35059B5C9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ABRIR_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Abrir Assinatura'
        GUID = '{EBC435B8-40C8-4406-ABBB-A9AABDAF8054}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento'
        GUID = '{675CCE2F-37C0-43EC-9C2C-65F25C89F387}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{2A32CA4E-38F0-42E6-ADFB-97BE963FD619}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS_LABEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Status Label'
        GUID = '{A0250440-73A9-48C9-8952-FBE7D8AABBF4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CENTRAL_ASSINATURA_GRID'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '455010;47401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{8CF6A046-E240-48D3-B801-88501869F781}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        GUID = '{16FE5974-8D9D-4C09-9267-76BD6068C1FA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_NOME_APELIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Nome Apelido'
        GUID = '{B8B713DF-9648-46D0-B3C8-82B11B0FD0E4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '455010;47402'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAssinaturaDigital: TFTable
    FieldDefs = <
      item
        Name = 'ID_ASSINATURA_DIGITAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Assinatura Digital'
        GUID = '{F2D35455-FE16-435B-B7BF-BEE42D871AB9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{32469A21-656C-408E-A3D5-219BCF375ACF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{4DC47600-0A51-4B99-9AF1-98C12E64B462}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_ASSINATURA_DIGITAL'
    Cursor = 'CRM_ASSINATURA_DIGITAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '455010;47403'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
