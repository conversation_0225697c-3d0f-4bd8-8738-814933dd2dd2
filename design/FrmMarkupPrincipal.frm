object FrmMarkupPrincipal: TFForm
  Left = 44
  Top = 162
  ActiveControl = FVBox1
  Caption = 'Markup Cadastro'
  ClientHeight = 482
  ClientWidth = 887
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600650'
  ShortcutKeys = <>
  InterfaceRN = 'MarkupPrincipalRN'
  Access = False
  ChangedProp = 
    'FrmMarkupPrincipal.Width;'#13#10'FrmMarkupPrincipal.Height;'#13#10#13#10'FrmMark' +
    'upPrincipal.ActiveControl'#13#10'FrmMarkupPrincipal_1.Touch.Interactiv' +
    'eGestures;'#13#10'FrmMarkupPrincipal_1.Touch.InteractiveGestureOptions' +
    ';'#13#10'FrmMarkupPrincipal_1.Touch.ParentTabletOptions;'#13#10'FrmMarkupPri' +
    'ncipal_1.Touch.TabletOptions;'#13#10'FrmMarkupPrincipal_1.Touch.Intera' +
    'ctiveGestures;'#13#10'FrmMarkupPrincipal_1.Touch.InteractiveGestureOpt' +
    'ions;'#13#10'FrmMarkupPrincipal_1.Touch.ParentTabletOptions;'#13#10'FrmMarku' +
    'pPrincipal_1.Touch.TabletOptions;'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 887
    Height = 482
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 3
    Padding.Left = 3
    Padding.Right = 3
    Padding.Bottom = 3
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 884
      Height = 60
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      ParentBackground = False
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnNovo: TFButton
        Left = 0
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Inclui um Novo Registro  (CRTL+ 2)'
        Caption = 'Novo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnNovoClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000046A4944415478DA8D967F681B6518C7BFEF5DDAA5AE6E36D58EC2DA4E
          BA5A1D42BBE93A990A56F6873237D81036D0BF1404C76C374BA508ADCC8D29FE
          530A8AEC0FC58242152B4341879B0C6BE956D0DDADBF67D35FA95996D4A5499A
          E4EE92BBBCBEEF2517EFB254FB1CC7BD6FDE7B9FCFF3E37D9E0B414E4856446C
          440420B33FE3C64B78C3B5C5F54AADA7761757E10BFB26F5B8FE1516F1293E86
          62EAB56D7BB4BBBBFB2CA5D4A98C10C74B5C7C311FFACBFBF71E78EE40DDF1C6
          E3B85F2CE79BC89A11C7C0AD017A65ECCA14D238882E2CD9F736AFAE46244555
          210884E63CE29ED9C750D20A7DFEA75652595B490F3DFC027119693CE56E3501
          D7D5ABD0C512FAC3C22532228F4C62062D0E402412BDA16A1C20C05268BFB9F4
          C97DF820781E2F3F7E18D56E116E4DC3B18A77CDB5AF57CF43DDB40901D5C0B7
          13DF43FB45EB7200A2D198A4A534D36AA18807462683677F7C86CE5578C9139B
          EBE80EB748B6A674BC5EF3396571249FF95E43B4D445175583FC9158827A4D1B
          7300626B6B528A594404A16888946412DB2F6DA7C6E634E9F3F4D23D35BB89C8
          343C56DD6486683A70130605BDB12C91F6F069646248D9014D6B1C904EE74322
          1484281A8DA26EA80E99521D7D15BDD85DD30C0ED855DD6C2A980AC81C006959
          46FB2A03A8D01D80783C2E738060599DCD45DE0366009EFE6D3F9D77CF916DCC
          D29DF7816CCB005D0D574D0F3E9C6D455000F52641824C339DC6AC139048C869
          0B209859F817C6C67CAD73B8935E485F20656CFF8E7206601A3E7A240B78E74F
          0660E0C5388859045FE0130720914848BAAE6743C2AC178A9C22694AC2D1DB47
          11D00228D3010F2BBAF71A7BCDB533B74E23CC3C525C6C12C66D9CC461072099
          4CCA268025397F8A6C632EAAAAD2C1E141D22576D1A01A2424068869981E1825
          CCAD2D6C1C43109DECBA834127405124230BC85B6FD544AE9D98CF582C86A1D1
          21F427FA315A368A10BBB854B16B9FB20F175FBD78121ABEE4AF3A008AA2C886
          6138AC66009A6B5516C49CA75229EAF7FBC9FCFC3CC2E1B0F99BC7E3417D7D3D
          6D686868612DE7F7C25ED4C4DA849CE1005E68B9245BA7C8E6813537956658F1
          B1DB1C8BA2681AE472B9F6B0DFE47B002CBE32AF56C106B014167A603D7973A4
          59C91921D0D2D29275009A26710F0A7B91657D612E7838F9A160E132C75C4457
          092A1ED85A1CA0699ACC5D261BF08029A6CC63C2AB3B1289507640080B0D8647
          AE5D7FBBBDED0803DE591720D87A911D60CDD966C20E040D8556CC24B339ADAA
          7A884C4C4ECCB4BDD5768841BDF9CF490140CAB098162B30FB98BD87BB77C3F0
          7ABDA87CB0123BEBEB71F9F2CFCB6F9E38F1A2FF2FFF944DE7BD1EF0A4157C64
          1CA78867931524F12D2FD3E9E919D2B2F7498C8F8FF9DB4F9D3AE89DF5DE4481
          3801A9944C6D39B0626E07F023C99A22999B9BA7C15088A84A62AEA3A3E3C8C2
          C2E2388A482140CA01D60D11CF11036061C987A1A15FE573EF9F39B6B2F2F72C
          D6113BA0B1A7A7E75C7E8190A21B780853BA81403014FDEE9B81B3AC852FE13F
          84148C37F6B725C762B7F17F2FFD034C50719467FC49DB0000000049454E44AE
          426082}
        ImageId = 6
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnAlterar: TFButton
        Left = 65
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Altera o Registro Selecionado  (CRTL+ 3)'
        Caption = 'Alterar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAlterarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000005094944415478DAAD956B6C536518C79FE7F4D0DDC82ED95A262BB0AC
          026EC16D201035121308514CCC844020881335442E11818D6BD84C4087CB3E90
          4C037E21A21F34442111A209E3E218868B894047071B3BDD4AB76EACDDADD773
          DAD39EC7F7F4E65A36C2079FE4DCCFFBFF3DB7F77D11FE476B5DB76259B63FFD
          3CA728D7291CDEB9B0A5C58AF18F1A8D465F5757F70511258F42047C0EF190B7
          1B372F685B1FFAAD38CF67B2A35796074196DF9838D63836362E8892041C8784
          5163FA49F72A8C20F214BDAA03C3012729E6B53830E8849AC634DA6A1BC5E93E
          1F8478FE4212607CDCD52D0554000709C189473CA2A86EE459098B106CDF00A2
          DB0A1F7CA5815E7B0066F276D89F0964EAC4F3490097CB2D04828188D7DC2411
          40FC59D56657A0308AE6ADA011EFD2278D1C9A1E89A048763ABD5F0E8B226F5A
          7B48AE4A02B83D1E211860008E9B3C459100309122FFA32398215E81DA131C5D
          BA3982A2D701273EF75361BEC6B6FA90BC667084BA26024A3C2A40961329E126
          4953BCE852DF0F90E6FA198EFF9A09A7CF09E0F7B9E068F528AC7CAD0056EE74
          7CD865A38BECB7B12480D7EBB5A8002EEE75B4164F1539E8BC82BCE35B3A7B43
          87479A6F422020D1F6779EE0A6B73361A4A0892A977DBA9CE9FDC30E6F32C0E7
          B3C8710017A9C27FB01820E4BE4FD0DF88B78599B4A3BE05032CA5554B07A876
          830687B26A000B56D3A2458B5E657A77D4EE4D02F87C3E21140A453D65DEA7A6
          88151094C70D2038F2E1A37D17C1EDF6C2EBF39CD0B04586E1B48DE09E5E0D85
          8585307FFEFC054CEF41BCD31200BFDF6F89005891135D14BB57641784ACC760
          D89341D57BAFAA3D4F65063736EFF0823F633959B9EDA88ACF993387F47AFD3C
          455184A701A22884A38084F7EA9C002508416B13484184CDFB5BA14BB083215F
          84933B4781CF29877BFE5DA0D3BF002525259108D2D3D3E73280E52980288A96
          70389C1C010205ACC7D93904DBEBAFC1AD3B56CA9B2EE3C9CF86495F68C06BCE
          5D9057308B8C4623161717435A5A1AF13C3F0540922C8A0A50275AACC801DB77
          C4830B0F345E873F5A7B2143AB50F3362796CDCDA1DFADDB3033D7A87A4EA5A5
          A5989595A5EA4C0D901820AC2891CE510192FD0C68C55BD474CA8C3F9E1F60EF
          098E7D3C4ACB17F378AEB39A28A31459CEA1A2A282727373631173A4D54E9B02
          1008086A04F1B568C0FC13FCD9F23D349C1A501D83DAB52E787F6518CE9AAB60
          9C2AA1A8A8082A2B2B41A7D301138CAECAFC34C8CBCD991CC07ADAA2FEA8A6C8
          EF7A8243D656103AFFA6A3CD5771D52B63B06F830417CC6F92E05EAA760C9597
          97A34EAF07AFC743AC4190A506FEBA71B375EF9EDDEF058341D7940016010D0A
          6DE8B0B68067E401897E172E9CD5079DC3F3C034B28A66CF9E8D068381B2B3B3
          B1BFDF0EAC31586BEAB0A3C3DC5E5353BBCAE9740E24B6931480A0B00D476DD1
          53DF1C80B2223658F683A29D0B9A9C25A037BC0CFA193340ABD5B249E6819E9E
          1EC82FC887178D46B87CF99275F79E3D2B846EA13769BF4A8D40DDD11E5BADB4
          7ACD1ADCBC7E196CDCB4857267BC84ACFDD45D0FD8776213126D7D7DF4F06127
          2E5DB218CCE6FBB683070FBE6532B577A6EE74C98060D0422C45A67B7749C3F3
          5851519958ECA27B0DAAC524B628A2C5D243430E074A92BFABEEF0E1773B3A1E
          74C324960A10285AE4E4253A261E03000340EF631BB4B55DBBDDD4F8F53A5687
          7E98C226028AEAEBEBBF8CBF8B0BA79A9AC260280C83438EF173BF9C69F0B8DD
          0E7886A5AA70F0FC46B1E399F62F6A8BA82D8608FAC10000000049454E44AE42
          6082}
        ImageId = 7
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = True
        IconReverseDirection = False
      end
      object btnExcluir: TFButton
        Left = 130
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Excluir  (CRTL+ 5)'
        Caption = 'Excluir'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnExcluirClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000016D4944415478DAEDD6CD2B055118C7F173950D29B150364A62435E36
          0A795F58B3C142FC0752B2F2BA932C6D2C9562C15E79B90965E3A56CA49494A2
          48E9DAA8E1FB34CFD4699A193323BBFBD4A7B973CF39F7377316CFB919F3CF95
          8939AF09D5A8D2FB07DCE3EAAF01FD58464BC8F80566B09F26A00307B8C50A4E
          F1AC631568C7346AD1A7E38902F650A3DBF31132A744B7E90E0351013D984581
          35D686577D83A8AA4339CEACEF1CCCE1C40B90ED5842191A718D7793AC4AADB5
          6FFE00AFBA71A46F944D1810B8F6B700B98E6142C727F18535BD5FC7863C69DA
          8005CC5BF3B2D63CA96F2CEABC7C403E201F1033403AE72586B06BDC7E228BBD
          2678A8D75EBD3A3A2E7D6C103B6836D641E40F901F7A316EC392F65B892EE3B6
          0329698A85FAA452E338C6A371DB7B8371CF0A272C406A049BC6ED2FB23DE7F8
          34C1558456DD26091FC5963D21ECC019C6AABE419C7AC214B6FD0351275A313A
          51AF9F832A871BDDA65CD084B8FF2A52D70F1A0B8E192C1E0DF5000000004945
          4E44AE426082}
        ImageId = 4600235
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnSalvar: TFButton
        Left = 195
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Salvar  (CRTL+ 5)'
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934
          31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2
          D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE
          6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5
          6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013
          090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF
          349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E
          8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871
          175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39
          FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622
          F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673
          BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E
          01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146
          0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219
          506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6
          0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991
          CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313
          9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0
          39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D
          AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7
          C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743
          FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE
          E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660
          474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF
          D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61
          33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E
          0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE
          F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF
          921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5
          A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E
          9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163
          8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09
          C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C
          D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E
          23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA
          FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E
          9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1
          5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A
          25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2
          FF02B065C443D9FE4B070000000049454E44AE426082}
        ImageId = 4
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnCancelar: TFButton
        Left = 260
        Top = 0
        Width = 65
        Height = 53
        Hint = 'Cancela as Altera'#231#245'es Correntes  (CRTL+ 6)'
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 4
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004EF4944415478DAAD566B4C1C55143E7766F6BD145020744BA98F1608
          A820DA508BB42A84B62A264D6A24D158E323C646230D4D7D24901AA8466BD336
          1A9AF84B1B7F580BD49ADA6A0B518A3450915D0BE9F2461E5BDEEC93DD9DDD9D
          39DE997D53B49A7893D9993B73EFF9CEF79DC75D02A141828385FF6120A2402F
          94EDC6BCCFA9A9A9A90BBD8F0E42E216DD6E5027A1BEBEBE4614C5FE95000556
          ABCDE8F17A8161088618491B629FA50D28D30DDD4346306C4BA150A05EAF2FA4
          00A65B006C367B8F9797001888188CBDC28C827623F358431CC7814EA75B1DC0
          6E7718791F2F7BCDACC200C273695FF81E048930E05816B55AEDEA000EA7D3E8
          E32900C3AC2E5150E38844244C254622960268349A28C08F8D1FAA76EE799FA7
          4BF31D0E0AE0F7CB12D8E60660B4F7023817478165782A9B0F44810351544342
          F206D8545801A9190510C5080E495EB55A1D05B8D6F2F96C47EB570D750DC673
          13169B716176047A5A8E6362A28BE43D940177A45136E00AC551DAA143EB0221
          E6EE29B42D2A49D1930721393D3BC24662A852A9A200674F96FAD3D6A6B2478E
          5F3A7DF8D32F2BCD9D0DB07D470E26ACF1DC427FC51CDDCB6AD27E6100EECADF
          8BB95BF64400944A6514A0E9B392C0CE8A3CE6BAC9014EA7171E29310043FCE1
          8AF91799CFC1CF1747213DFB79C82FA994258B03683C511C28DF95CBCA9910AD
          29D94BBFC30A9EA9110CCCCF11F00BC0A8B4A04C31A026732361944C8419028B
          DF37F79392678F41C6BD05486B210A70E6D816A1AC3C8F50B783CB25B55104E7
          C4EFE0991905AF4B8489291A0587086A4E80BB937D90A055803E771BA80DEBE9
          6251F6C8ED0538FBC322BCFEC177F10CBE3DBA5978A22C9701963A42931FA863
          CE9B9DE85F9E2137CC2C589C45985F5C49925232C0BE340B3D6D67F04EFB4FE4
          E1756ED46FDA4A546B374418B75D9D82ACC76AF181A21D51806F3E29141F2FCD
          22C0D17CE780F83C13E075F6A1CB4DC8B5EE62D8FB6E13320C1B0EA06CACFD7C
          0359F8E53D2C48779384FBCA81516BE5A2732C0BD03164C0970E7E1105387528
          4B2C2DA39A6A54489454204E04419CC280384EFABA11F4490770EBD307223545
          42557BA5E9301AACCD44AB53D1B8645255790262004EB75A71FF89AE2840C3FE
          F4E5CD391E0D9BB006B83549C0E913A847B460540140CE02FD9D023CB8FD2418
          EEA9801810989BE8034BFB1148532D8222391330E00614BC70F1AA055EFDA8A3
          90266010E0D02BEB9EE179B1C43CEE4D7BF9B5B75E945293EEA7B210228544AD
          51D2185490C494BC38068EA5691C683D4A52D91910153A14033C61580E4EB5CC
          77D6379CDF2D8A3813D78B6885E77B3CBC89520B363BB9DB31B2E6B1ED5A9A0B
          8240E66F8EE0E46F5F9380CB029C3A1193330A48D790A7FFCDAA772A68D31C8E
          9C0F311593CFF3BC51A469C7D0A4908A82A51EC5CA127EA6EB60F07A1B4DE50E
          48DBB80DD6E73C0A972F5F9A7C63DFBE5D9629CB8DB80368058049AA01EA238A
          4280704A0D65C6C6F619E938C4C5B949326EFE1597A607C9FD252F80B177D0F2
          7655D553C343C37FACACF378009FCF442D53003F9548411562E32492E2E3E3DD
          689D1F23B39366B4D9DDC4EA4B1AA9AEAEDE3D36F667EF6A8D64258011E51848
          DE32D1534C960C2566B465FBC1659F86C9F161B8D26536D5D57DFCDCFCFCC2D0
          DF75AA5880ECDADADAFAC887D8B3048310F22F95D0E7F3C0CCDC9CBDB9F15C9D
          D3E91AFFA75648563CFF97BF2D12A270BB457F0173456D3788187BC400000000
          49454E44AE426082}
        ImageId = 9
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object FHBox3: TFHBox
        Left = 325
        Top = 0
        Width = 26
        Height = 28
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = 'FHBox2'
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        Visible = False
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
    end
    object PageControlMurkup: TFPageControl
      Left = 0
      Top = 61
      Width = 883
      Height = 409
      ActivePage = tabMarkup
      Align = alClient
      TabOrder = 1
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabListagemMarkupModelo: TFTabsheet
        Caption = 'Listagem'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 877
          Height = 398
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox2: TFHBox
            Left = 0
            Top = 0
            Width = 713
            Height = 56
            Align = alClient
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 10
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox4: TFVBox
              Left = 0
              Top = 0
              Width = 86
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lfIdMarkup: TFLabel
                Left = 0
                Top = 0
                Width = 52
                Height = 13
                Align = alLeft
                Anchors = []
                Caption = 'Id. Markup'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhFilter
                WKey = '310021;31001;31001'
                VerticalAlignment = taAlignBottom
                WordBreak = False
                MaskType = mtText
              end
              object efIdMarkup: TFInteger
                Left = 0
                Top = 14
                Width = 76
                Height = 24
                Hint = 'Filtra pelo Id. markup sequence'
                TabOrder = 1
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhFilter
                WKey = '310021;31001;31001'
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                Maxlength = 0
                Align = alLeft
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                Alignment = taRightJustify
                OnEnter = filtrarGrid
              end
            end
            object FVBox5: TFVBox
              Left = 86
              Top = 0
              Width = 513
              Height = 50
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lfDescricao: TFLabel
                Left = 0
                Top = 0
                Width = 46
                Height = 13
                Align = alLeft
                Anchors = []
                Caption = 'Descri'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhFilter
                WKey = '310021;31001;31002'
                VerticalAlignment = taAlignBottom
                WordBreak = False
                MaskType = mtText
              end
              object efDescricao: TFString
                Left = 0
                Top = 14
                Width = 500
                Height = 24
                Hint = 'Filtra pelo Descri'#231#227'o do modelo de markup'
                TabOrder = 2
                AccessLevel = 0
                Flex = False
                WOwner = FrInterno
                WOrigem = EhFilter
                WKey = '310021;31001;31002'
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Align = alLeft
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = filtrarGrid
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
          end
          object gridPrincipal: TFGrid
            Left = 0
            Top = 57
            Width = 710
            Height = 120
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbMarkupModelo
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            Columns = <
              item
                Expanded = False
                FieldName = 'ID_MARKUP'
                Font = <>
                Title.Caption = 'Id. Markup'
                Width = 105
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{21F1D0C5-6A6A-416C-AD6D-3616AA2420E6}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'DESCRICAO'
                Font = <>
                Title.Caption = 'Descri'#231#227'o'
                Width = 480
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{CF894CD6-DF59-427E-A79B-AD770820D4C9}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end>
          end
        end
      end
      object tabCadastroMarkupModelo: TFTabsheet
        Caption = 'Modelo'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox3: TFVBox
          Left = 0
          Top = 0
          Width = 875
          Height = 381
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FGroupbox2: TFGroupbox
            Left = 0
            Top = 0
            Width = 772
            Height = 109
            Caption = 'Markup Modelo'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = False
            Closed = False
            Orient = coHorizontal
            Style = grp3D
            HeaderImageId = 0
            object FVBox9: TFVBox
              Left = 2
              Top = 15
              Width = 768
              Height = 92
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 5
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox16: TFHBox
                Left = 0
                Top = 0
                Width = 665
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox17: TFHBox
                  Left = 0
                  Top = 0
                  Width = 118
                  Height = 37
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox18: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel4: TFLabel
                    Left = 9
                    Top = 0
                    Width = 46
                    Height = 13
                    Caption = 'Descri'#231#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edDescricaoMarkupModelo: TFString
                  Left = 118
                  Top = 0
                  Width = 461
                  Height = 24
                  Hint = 'Descri'#231#227'o do modelo de markup'
                  Table = tbMarkupModelo
                  FieldName = 'DESCRICAO'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
              end
              object FHBox19: TFHBox
                Left = 0
                Top = 42
                Width = 665
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox20: TFHBox
                  Left = 0
                  Top = 0
                  Width = 118
                  Height = 37
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox21: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel6: TFLabel
                    Left = 9
                    Top = 0
                    Width = 51
                    Height = 13
                    Caption = 'Tipo Custo'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edtipoCustoMarkupModelo: TFCombo
                  Left = 118
                  Top = 0
                  Width = 199
                  Height = 21
                  Hint = 'Tipo custo.  Op'#231#245'es: Contabil=C; Forncedor=F'
                  Table = tbMarkupModelo
                  FieldName = 'TIPO_CUSTO'
                  Flex = False
                  ListOptions = 'Contabil=C; Forncedor=F'
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = False
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
            end
          end
        end
      end
      object tabMarkup: TFTabsheet
        Caption = 'Markup'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox6: TFVBox
          Left = 0
          Top = 0
          Width = 875
          Height = 381
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object GridMarkup: TFGrid
            Left = 0
            Top = 0
            Width = 773
            Height = 81
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbMarkup
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            Columns = <
              item
                Expanded = False
                FieldName = 'ID_MARKUP_TIPO'
                Font = <>
                Title.Caption = 'Descri'#231#227'o item'
                Width = 210
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.LookupDesc = 'DESCRICAO'
                Editor.LookupKey = 'ID_MARKUP_TIPO'
                Editor.LookupTable = tbMarkupTipo
                Editor.EditType = etTFCombo
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{50D6B5D6-1272-48C6-B1FD-4AAF7888B13C}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'VALOR_FIXO'
                Font = <>
                Title.Caption = 'Valor Fixo'
                Width = 120
                Visible = True
                Precision = 2
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{0B9D4223-3A07-47E0-A385-28C9585CF14B}'
                    WOwner = FrWizard
                    WOrigem = EhNone
                    WKey = '310021;31002;31003'
                    Mask = 'R$ ,##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFDecimal
                Editor.Precision = 2
                Editor.Step = 0
                Editor.Mask = 'R$ ,##0.00'
                Editor.MaxLength = 10
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = True
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{F117CC88-4022-436D-8174-CF8999671D72}'
                WOwner = FrWizard
                WOrigem = EhAttribute
                WKey = '310021;31002;31003'
                Hint = 'Valor numerico'
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'VALOR_PERCENTUAL'
                Font = <>
                Title.Caption = 'Valor Percentual'
                Width = 169
                Visible = True
                Precision = 2
                TextAlign = taRight
                FieldType = ftDecimal
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{39E744F2-28E3-463A-80AE-59283FCF30E8}'
                    WOwner = FrWizard
                    WOrigem = EhNone
                    WKey = '310021;31002;31004'
                    Mask = '##0,00%'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFDecimal
                Editor.Precision = 2
                Editor.Step = 0
                Editor.Mask = '##0,00%'
                Editor.MaxLength = 4
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = True
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{E9132E24-5F89-4963-BF8C-392EB9FD72F0}'
                WOwner = FrWizard
                WOrigem = EhAttribute
                WKey = '310021;31002;31004'
                Hint = 'Valor percentual.'
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'TIPO_COMISSAO'
                Font = <>
                Title.Caption = 'Tipo Comiss'#227'o'
                Width = 164
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftCombo
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.LookupListOptions = 'Sobre o lucro=L; Sobre a venda=V'
                Editor.EditType = etTFCombo
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 1
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = True
                Editor.ReadOnly = False
                ListOptions = 'Sobre o lucro=L; Sobre a venda=V'
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{99ECD573-358D-4033-9D96-444B1846B3A5}'
                WOwner = FrWizard
                WOrigem = EhAttribute
                WKey = '310021;31002;31006'
                Hint = 
                  'Tipo comiss'#227'o, Sobre o Lucro .  Op'#231#245'es: Sobre o lucro=L; Sobre a' +
                  ' venda=V'
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end>
          end
          object FHBox11: TFHBox
            Left = 0
            Top = 82
            Width = 438
            Height = 41
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object btnAlterarMarkup: TFButton
              Left = 0
              Top = 0
              Width = 48
              Height = 35
              Hint = 'Alterar Item'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 0
              OnClick = btnAlterarMarkupClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA
                5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31
                ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E
                C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E
                86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD
                2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74
                EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2
                30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7
                38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584
                AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979
                0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875
                08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80
                6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618
                713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524
                87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5
                B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045
                4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36
                5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A
                7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5
                42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF
                984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102
                DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B
                1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2
                DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E
                166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42
                6082}
              ImageId = 7
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object btnSalvarMarkup: TFButton
              Left = 48
              Top = 0
              Width = 48
              Height = 35
              Hint = 'Salvar Item'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 1
              OnClick = btnSalvarMarkupClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3
                0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220
                AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950
                4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5
                5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37
                35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067
                603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7
                29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B
                DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA
                B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B
                A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194
                B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002
                EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715
                87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B
                AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9
                32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66
                5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E
                4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05
                05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF
                0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76
                093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0
                C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65
                C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70
                9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D
                80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12
                B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8
                6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087
                94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF
                0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34
                2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2
                134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000
                0049454E44AE426082}
              ImageId = 4
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object btnCancelarMarkup: TFButton
              Left = 96
              Top = 0
              Width = 48
              Height = 35
              Hint = 'Cancelar Item'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 2
              OnClick = btnCancelarMarkupClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14
                8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912
                1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3
                FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965
                29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B
                D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422
                C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F
                6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B
                81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700
                558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45
                1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D
                E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E
                1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848
                DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655
                BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240
                F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D
                1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850
                7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945
                0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433
                D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F
                B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382
                CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6
                8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC
                EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924
                C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903
                98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0
                90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36
                8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000
                49454E44AE426082}
              ImageId = 9
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
          end
          object FVBox7: TFVBox
            Left = 0
            Top = 124
            Width = 771
            Height = 100
            Align = alClient
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 5
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox14: TFHBox
              Left = 0
              Top = 0
              Width = 753
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox27: TFHBox
                Left = 0
                Top = 0
                Width = 364
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox28: TFHBox
                  Left = 0
                  Top = 0
                  Width = 118
                  Height = 37
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox29: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel5: TFLabel
                    Left = 9
                    Top = 0
                    Width = 69
                    Height = 13
                    Caption = 'Descri'#231#227'o item'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edIdmarkupTipoMarkup: TFCombo
                  Left = 118
                  Top = 0
                  Width = 237
                  Height = 21
                  Table = tbMarkup
                  LookupTable = tbMarkupTipo
                  FieldName = 'ID_MARKUP_TIPO'
                  LookupKey = 'ID_MARKUP_TIPO'
                  LookupDesc = 'DESCRICAO'
                  Flex = False
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = False
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object FHBox10: TFHBox
                Left = 364
                Top = 0
                Width = 364
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox12: TFHBox
                  Left = 0
                  Top = 0
                  Width = 94
                  Height = 37
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox13: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel3: TFLabel
                    Left = 9
                    Top = 0
                    Width = 68
                    Height = 13
                    Caption = 'Tipo Comiss'#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edTipoComissaoMarkup: TFCombo
                  Left = 94
                  Top = 0
                  Width = 237
                  Height = 21
                  Hint = 
                    'Tipo comiss'#227'o, Sobre o Lucro .  Op'#231#245'es: Sobre o lucro=L; Sobre a' +
                    ' venda=V'
                  Table = tbMarkup
                  FieldName = 'TIPO_COMISSAO'
                  Flex = False
                  ListOptions = 'Sobre o lucro=L; Sobre a venda=V'
                  ReadOnly = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Selecione'
                  Constraint.Expression = 'value=""'
                  Constraint.Message = 'tipo comiss'#227'o vazio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  ClearOnDelKey = False
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
            end
            object FHBox15: TFHBox
              Left = 0
              Top = 46
              Width = 752
              Height = 45
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox4: TFHBox
                Left = 0
                Top = 0
                Width = 364
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox5: TFHBox
                  Left = 0
                  Top = 0
                  Width = 118
                  Height = 37
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox6: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel1: TFLabel
                    Left = 9
                    Top = 0
                    Width = 47
                    Height = 13
                    Caption = 'Valor Fixo'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edValorFixoMarkup: TFInteger
                  Left = 118
                  Top = 0
                  Width = 121
                  Height = 24
                  Table = tbMarkup
                  FieldName = 'VALOR_FIXO'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                end
              end
              object FHBox7: TFHBox
                Left = 364
                Top = 0
                Width = 364
                Height = 41
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox8: TFHBox
                  Left = 0
                  Top = 0
                  Width = 95
                  Height = 37
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 5
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox9: TFHBox
                    Left = 0
                    Top = 0
                    Width = 9
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object FLabel2: TFLabel
                    Left = 9
                    Top = 0
                    Width = 78
                    Height = 13
                    Caption = 'Valor Percentual'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edValorPercentualMarkup: TFInteger
                  Left = 95
                  Top = 0
                  Width = 121
                  Height = 24
                  Table = tbMarkup
                  FieldName = 'VALOR_PERCENTUAL'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                end
              end
            end
          end
        end
      end
    end
  end
  object tbMarkupModelo: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Custo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CP_MARKUP_MODELO'
    Cursor = 'CP_MARKUP_MODELO'
    MaxRowCount = 200
    OnAfterScroll = tbMarkupModeloAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 2
    Top = 4
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbMarkup
        GUID = '{50B0DE05-C1B9-460B-89B8-BE40B3315719}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbMarkup: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP_TIPO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_FIXO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Fixo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PERCENTUAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Percentual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCR_MARKUP_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_COMISSAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Comiss'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CP_MARKUP'
    TableName = 'CP_MARKUP'
    Cursor = 'CP_MARKUP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 2
    Top = 4
  end
  object tbMarkupTipo: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CP_MARKUP_TIPO'
    Cursor = 'CP_MARKUP_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 4
    Top = 4
  end
  object tbMarkup1: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CP_MARKUP'
    Cursor = 'CP_MARKUP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbParmFluxo: TFTable
    FieldDefs = <
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP_OFICINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Markup Oficina'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PARM_FLUXO'
    Cursor = 'CRM_PARM_FLUXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600650;46007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
