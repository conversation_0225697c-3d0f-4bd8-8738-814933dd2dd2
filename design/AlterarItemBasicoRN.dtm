object AlterarItemBasicoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000173'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbItens
        GUID = '{BF6B49E0-461E-4498-936B-20AC06F9B839}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbItensFornecedor
        GUID = '{B6088CE2-66B2-4F99-9317-A4798693CF3C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbItensHistBloqueio
        GUID = '{A1FA700F-6301-4F92-A768-4914C2FCF398}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbItens: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{168C0D91-BD57-4876-907B-188571FD6B5A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO_INTERNO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Interno'
        GUID = '{A7051606-06E0-40C2-A617-470DEF7BBF8D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_GRUPO_INTERNO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo Interno'
        GUID = '{5A3CCCB4-9415-44BC-BE72-FA4FF62FC4E6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMBALAGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Embalagem'
        GUID = '{52ABA0CD-5BE4-400F-8F2E-4B8AA59C71EE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UNIDADE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Unidade'
        GUID = '{85B65FCF-35C3-44F7-B726-952BE35AC7A8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica'#231#227'o'
        GUID = '{3530FC14-FE60-4AAB-86E6-5FF0771F53CA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_LIQUIDO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso L'#237'quido'
        GUID = '{262CF1B5-EB79-4F66-90EE-3309FC158DFE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_BRUTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso Bruto'
        GUID = '{68D25FE2-19C0-4A00-A659-B076BFCE1FA9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Volume'
        GUID = '{8E53147B-126E-447A-93C7-8DE5C3B282BC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALTURA_VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Altura Volume'
        GUID = '{35577341-E968-47D4-AC10-6A4A655BFF93}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LARGURA_VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Largura Volume'
        GUID = '{BDDDF3C4-4580-4644-ADA5-AF4141C881EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPRIMENTO_VOLUME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comprimento Volume'
        GUID = '{7271A699-28A5-43EA-A92E-53A209BE9961}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MAX_DESC'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Max Desconto'
        GUID = '{F7799655-F64E-40ED-A41C-B534142C7C7E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ITENS'
    TableName = 'ITENS'
    Cursor = 'ITENS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensFornecedor: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{0A83EE73-B145-4808-AEC6-03779F95C623}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        GUID = '{DCDDE054-A6B4-4228-B236-B6A11FAB97E2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICANTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabricante'
        GUID = '{10787F6D-E6F5-4376-9127-A341877695CD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SITUACAO_ESPECIAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Situa'#231#227'o Especial'
        GUID = '{980D90B0-8B4E-4940-A0FB-1831CDF947E3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_BARRAS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Barras'
        GUID = '{9FCA2148-9F47-4DC0-B01E-E2BA32346958}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        GUID = '{BCA22238-D65C-4A86-AF07-4427C8C01AA7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GTIN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Gtin'
        GUID = '{F89743B8-6A21-49DF-BF47-10E24A7D2330}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_PECA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Peca'
        GUID = '{42C3B782-A3CA-4ACF-9A33-D796CB9D2609}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_DIAS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Reserva Dias'
        GUID = '{829B6CBF-953A-4B74-94D9-47B740B5AF92}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PART_NUMBER'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Part Number'
        GUID = '{0A7C25BA-B855-47A0-B569-92C74B5A18EE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORIGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Origem'
        GUID = '{E4CD26A1-8919-42B9-A440-8FC64906D550}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPRAS_BLOQUEADAS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Compras Bloqueadas'
        GUID = '{99806ADB-79D2-44FF-A201-4648C5F3163C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ITENS_FORNECEDOR'
    TableName = 'ITENS_FORNECEDOR'
    Cursor = 'ITENS_FORNECEDOR'
    MaxRowCount = 1000
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensGrupoInterno: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_INTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Interno'
        GUID = '{97CF5D6C-468B-4021-8AE8-4F6B4901B4A7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{5B7D8C0E-12AF-4E38-BB00-9C6DB4402EBB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_GRUPO_INTERNO'
    Cursor = 'ITENS_GRUPO_INTERNO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensSubGrupo: TFTable
    FieldDefs = <
      item
        Name = 'COD_SUB_GRUPO_INTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo Interno'
        GUID = '{B48867C2-450A-4F81-AE5B-83F2E4C7F42F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{BBA42E21-EC91-4AA6-91DD-66BF75DEACC1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_SUB_GRUPO'
    Cursor = 'ITENS_SUB_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFabricante: TFTable
    FieldDefs = <
      item
        Name = 'COD_FABRICANTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabricante'
        GUID = '{3AD40074-A7E6-41D8-B4C4-92B5FBBA3B4D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{BBF3C8C9-C546-4C86-9094-14855236F518}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'FABRICANTE'
    Cursor = 'FABRICANTE'
    MaxRowCount = 2000
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensUnidadeMedida: TFTable
    FieldDefs = <
      item
        Name = 'COD_UNIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Unidade'
        GUID = '{20F79855-AF89-44C0-ADE5-5BAD7E537D3F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{2E59A4A6-34E5-4AEA-930B-B9E40B10F22A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_UNIDADE_MEDIDA'
    Cursor = 'ITENS_UNIDADE_MEDIDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItemSituacaoEspecial: TFTable
    FieldDefs = <
      item
        Name = 'ID_SITUACAO_ESPECIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Situa'#231#227'o Especial'
        GUID = '{159A789B-8700-484D-816B-B6ACDA820A9C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{22B6D18E-3BDF-4F91-9CB6-FDE709444C31}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITEM_SITUACAO_ESPECIAL'
    Cursor = 'ITEM_SITUACAO_ESPECIAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;70007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMarca: TFTable
    FieldDefs = <
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        GUID = '{ABB00281-3C73-4DDF-A210-34BC7AB0AFE6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{1841E458-5BF4-4A54-8703-A6572D3427A4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'MARCA'
    Cursor = 'MARCA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;24301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaLetraDescontoGeral: TFTable
    FieldDefs = <
      item
        Name = 'LETRA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Max Desconto'
        GUID = '{5623C8AE-0510-4C1D-A96D-4EACED084FA2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA_PERCENTUAL_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{4EF163DF-2BD7-4EB2-8ED8-262C9198CF1E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_LETRA_DESCONTO_GERAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;27402'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensOrigem: TFTable
    FieldDefs = <
      item
        Name = 'COD_ORIGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Origem'
        GUID = '{83D7FD04-E873-4E84-94FD-2E7D5CF84CAF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{02B7AC7D-2127-41C4-8AB0-400CA3C5A712}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_ORIGEM'
    Cursor = 'BUSCA_ITENS_ORIGEM'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;31101'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensHistBloqueio: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{5D828199-5CDC-4782-95F0-6B95B29F7ACD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{61112E23-4F4E-40FF-86B2-37C3C4F6C7EC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{44059B7C-42B1-41CE-A182-4A086FC32134}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        GUID = '{71687C19-49E3-4156-BDDC-5F54423142F2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{09F4DD32-9912-4522-8A2C-3DB4166976A2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Hora'
        GUID = '{77D7D9F9-0295-4C9E-A40C-CB38D3FF4A43}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        GUID = '{E4B88B18-96F6-4D9D-BFA8-0AFD59081DD5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{4A2D5A7C-A9C8-4588-8797-8859A7C46CA6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'ITENS_HIST_BLOQUEIO'
    TableName = 'ITENS_HIST_BLOQUEIO'
    Cursor = 'ITENS_HIST_BLOQUEIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;47703'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbBuscaItensFornecedor: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        GUID = '{C8AD5559-8CE9-423B-9D6D-A1A4DD4F33C1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        GUID = '{AAC9C3BA-**************-88B2B1060AE5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ITENS_FORNECEDOR'
    Cursor = 'BUSCA_ITENS_FORNECEDOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000173;47704'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
