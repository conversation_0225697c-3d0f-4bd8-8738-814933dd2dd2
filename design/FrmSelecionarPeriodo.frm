object FrmSelecionarPeriodo: TFForm
  Left = 44
  Top = 163
  ActiveControl = vBoxPrincipal
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Selecionar per'#237'odo'
  ClientHeight = 62
  ClientWidth = 282
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = frmShow
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '5300733'
  ShortcutKeys = <>
  InterfaceRN = 'SelecionarPeriodoRN'
  Access = False
  ChangedProp = 
    'FrmSelecionarPeriodo.Width;'#13#10'FrmSelecionarPeriodo.Height;'#13#10'FrmSe' +
    'lecionarPeriodo.ActiveControlFrmSelecionarPeriodo.Width;'#13#10'FrmSel' +
    'ecionarPeriodo.ActiveControl;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 282
    Height = 62
    Hint = 'Selecionar per'#237'odo'
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxLinha01: TFHBox
      Left = 0
      Top = 0
      Width = 270
      Height = 30
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object dtInicial: TFDate
        Left = 0
        Top = 0
        Width = 121
        Height = 24
        Hint = 'In'#237'cio'
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Prompt = 'In'#237'cio'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Format = 'dd/MM/yyyy'
        ShowCheckBox = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = dtInicialEnter
        ShowTime = False
        ShowOnFocus = False
      end
      object dtFinal: TFDate
        Left = 121
        Top = 0
        Width = 121
        Height = 24
        Hint = 'Fim'
        TabOrder = 0
        AccessLevel = 0
        Flex = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Prompt = 'Fim'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        Format = 'dd/MM/yyyy'
        ShowCheckBox = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = dtFinalEnter
        ShowTime = False
        ShowOnFocus = False
      end
      object btnAceitar: TFButton
        Left = 242
        Top = 0
        Width = 21
        Height = 18
        Hint = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 2
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
          E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
          B9B9B6418D210000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'calendar-check-o'
        IconReverseDirection = False
        UploadMime = 'image/*'
      end
    end
  end
end
