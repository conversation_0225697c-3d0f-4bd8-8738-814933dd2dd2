object FrmFichaItemLocacao: TFForm
  Left = 44
  Top = 162
  ActiveControl = FHBox1
  Caption = 'Selecionar Loca'#231#227'o'
  ClientHeight = 385
  ClientWidth = 414
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310045'
  ShortcutKeys = <>
  InterfaceRN = 'FichaItemLocacaoRN'
  Access = False
  ChangedProp = 
    'FrmFichaItemLocacao.Width;'#13#10'FrmFichaItemLocacao.Height;'#13#10#13#10'FrmFi' +
    'chaItemLocacao.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FHBox1: TFHBox
    Left = 0
    Top = 0
    Width = 414
    Height = 61
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 2
    Margin.Left = 3
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftFalse
    Flex.Hflex = ftFalse
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object btnVoltar: TFButton
      Left = 0
      Top = 0
      Width = 65
      Height = 53
      Hint = 'Voltar'
      Caption = 'Voltar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 0
      OnClick = btnVoltarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
        FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
        290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
        086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
        152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
        F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
        86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
        B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
        AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
        EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
        AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
        736BB6EF9B710000000049454E44AE426082}
      ImageId = 700081
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
    object btnSalvar: TFButton
      Left = 65
      Top = 0
      Width = 65
      Height = 53
      Hint = 'Salvar'
      Caption = 'Salvar'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Layout = blGlyphTop
      ParentFont = False
      TabOrder = 1
      OnClick = btnSalvarClick
      PngImage.Data = {
        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
        F8000004DD4944415478DAB5956B6C145514C7CF9DD9F7B32F444C6AE4A36934
        31D10413356DD3620BB5501011F181E8074894475B2BA52D334B1F46688288A2
        D637BE916A1B9116905AD9B6B4807D91F85DBE501343DBED86BA8FD9EB39F7CE
        6E778BFDE864CFDC7B67EE9CDF39FF7BEE5D06FFF3C5E8565B5373AFA2A84FC5
        6251F0F9B3A0B0740DF8BD3E5055056730FC4953148586001C7F3C210C6FC013
        090885C370BEF767989D9E06D5A2427676F6F70D8D8D7F08C0CE1D3BB4A9A9BF
        349BCDC6F2F3EF865DAF3771B7C3CECC2038330391201C0B1EE38A6C015B1E9E
        8FB0379B35980B85384DF5FBFDFAA1F6C301F1E1CBDB5FD2C2E1B04611AE5871
        175437E830772B7A7BAA6CA1CFCC07D41224CB638756AD9100628EC7E3091C39
        FA96046CDFF6A2868D26C85959B04F6BE1596E472A03E92905E1D2274B658622
        F199F03FACE5400384666739BD74BBDDFADBEF1C93806DCFBF9001683CD8C673
        BCCE4C80D921D15176C6B96C137843E3A15B11F686260134DFED71EBEF1E3F2E
        01CF6D7D564328018000FB036D8B24E2A6737381934FC462CBBED76583438146
        0288699841E0BD0FDE9780AD5B9E1119506A7EACA23ABD957B9CB6CC0CF8D219
        506F3E1A67ED070930232472B95C7AC7471F4AC096CD4F6748547BA0953B6DD6
        0C004F66227E043401E6B368DC60479A172422C0C79F7E22019B9FDCA4812991
        CFEF87EAA63608CD4781A574CFECF034A94CA890E8684B43AA8A9C4E67E0B313
        9F4BC0A60D1B6506989ACFE787DD4DAD224DB6E087A5E94F1E99909FA43233A0
        39C75A1B201CC20CF05304E827BEFC420236ACAFD29894082C562B14576CC45D
        AC82058D5AB183C5AE520427914808EF06B646DC80B81187846140DFE94E1CC7
        C51C0766F0D5375F4BC0FACA75A93548D79D2D2AD345659B5EBE7CF1B70E8743
        FFF6E4771250B9B6222551B250FEB39F3E5E6A8ED977D8EDFAC9CE53125051BE
        E6F60C7092220F388E3231299338EC4871B34C392399501E3CEF128CA44B6660
        474067D78F1250BEFAF124009287DA9E9A6AC8CBCB13012D75D1BBCE539DD0DF
        D727D6030F4BF07A3CE21DF603DDA77F9280D2A2E264998AB1D56AE5BBF6EE61
        33D33370FDFA9F22BAFCFC7CB867E54AE1571C44781986C12E9CFF050682418E
        0016C705763A1C2203F4A19F39DB2B01858F3E962111D279CD6BB56C707010CE
        F59EE5F8212B2E2981CA7595627FD1184DB4BFF5F7C3C8F0084799582C1613DF
        921F1B02CEF55D908047563D9CDA68A254B134EBF6D7C310027ACFF400460AC5
        A525505656061425193DA316A387DFAF5C15A54B63D41E2C160BFD09057E0D5E
        9480550F3E949981DDCEEBEAF7B1818B41E8EDE9115214161741496929391163
        8C96A3B14B8343303E36965A642CCFE426D583978624E081FBEEAF5654B53D09
        C012E3BBABF7B2607000BABBBB44B59495954361512190639206210230323C0C
        D7262610C0459D5A5122AC3E82E957C7C724C0E574B9962F5BF683CFEB5D6D9E
        23B0F3D557442B762CCA11894484A5CB43115FB97C19AE8D4F0869A994491EBA
        FEBE79F3F08DA91BF5C9BAB7A115DD79C7F2665C1C6F4E4E8EB2B6F209DCED4E
        9A6DC1805474AAD27110C7C8F199416B8DE3F8E4E46474627C3C4259CAFF69B1
        5762D3B3B31DA1B9B98EF423C08D968DA616141458ABAAAADCB9B9B92A2E9A8A
        25A790038C989BFB82D6C1088542B1D1D1D1F9AEAEAE0866A4A4F932D066D0C2
        FF02B065C443D9FE4B070000000049454E44AE426082}
      ImageId = 4
      WOwner = FrInterno
      WOrigem = EhNone
      Color = clBtnFace
      Access = False
      IconReverseDirection = False
    end
  end
  object FVBox1: TFVBox
    Left = 0
    Top = 61
    Width = 414
    Height = 324
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 3
    Padding.Right = 3
    Padding.Bottom = 3
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 3
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox3: TFHBox
      Left = 0
      Top = 0
      Width = 399
      Height = 33
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 3
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object comboLocalEstoque: TFCombo
        Left = 0
        Top = 0
        Width = 210
        Height = 21
        LookupTable = tbLocalEstoque
        LookupKey = 'COD_LOCAL_ESTOQUE'
        LookupDesc = 'INITCAP_NOME_LOCAL'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Local estoque'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = True
        OnChange = comboLocalEstoqueChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object edtLocacao: TFString
        Left = 210
        Top = 0
        Width = 150
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Loca'#231#227'o'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        IconDirection = idLeft
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = edtLocacaoEnter
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
    end
    object gridLocacao: TFGrid
      Left = 0
      Top = 34
      Width = 397
      Height = 192
      Align = alClient
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbItemLocacaoDispEmp
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = False
      Paging.PageSize = 0
      Paging.DbPaging = False
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      ActionButtons.BtnAccept = False
      ActionButtons.BtnView = False
      ActionButtons.BtnEdit = False
      ActionButtons.BtnDelete = False
      ActionButtons.BtnInLineEdit = False
      Columns = <
        item
          Expanded = False
          Font = <>
          Title.Caption = ' #'
          Width = 33
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <
            item
              Expression = 'SEL='#39'S'#39
              Hint = 'Loca'#231#227'o selecionada'
              EvalType = etExpression
              GUID = '{A1A0E068-61C3-43D9-A36D-4ED3983BF445}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 310010
              OnClick = 'selecionar'
              Color = clBlack
            end
            item
              Expression = 'SEL='#39'N'#39
              Hint = 'Loca'#231#227'o n'#227'o selecionada.'
              EvalType = etExpression
              GUID = '{D71ADDA1-199C-4DDD-949A-3BE24C97E218}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 310011
              OnClick = 'retirarSelecao'
              Color = clBlack
            end>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{77AACCC6-B885-4CB2-AE86-20B99B5D9A20}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'NOME_DO_LOCAL'
          Font = <>
          Title.Caption = 'Local estoque'
          Width = 164
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = True
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{*************-4C51-878E-3275C85AECA2}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'LOCACAO'
          Font = <>
          Title.Caption = 'Loca'#231#227'o'
          Width = 150
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{48B9F5CB-0EBE-4646-86E9-CBE1E9F5D98C}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end>
    end
  end
  object tbItemLocacaoDispEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Do Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOCACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Loca'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ITEM_LOCACAO_DISP_EMP'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310045;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLocalEstoque: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Do Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INITCAP_NOME_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Initcap Nome Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'LOCAL_ESTOQUE'
    Cursor = 'LOCAL_ESTOQUE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310045;31002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
