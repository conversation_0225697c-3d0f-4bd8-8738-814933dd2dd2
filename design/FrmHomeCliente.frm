object FrmHomeCliente: TFForm
  Left = 150
  Top = 150
  Caption = 'Home'
  ClientHeight = 494
  ClientWidth = 1248
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '340057'
  ShortcutKeys = <>
  InterfaceRN = 'HomeClienteRN'
  Access = False
  ChangedProp = 
    'FrmHome.Width;'#13#10'FrmHome.Spacing;'#13#10#13#10'FrmHome_1.Touch.InteractiveG' +
    'estures;'#13#10'FrmHome_1.Touch.InteractiveGestureOptions;'#13#10'FrmHome_1.' +
    'Touch.ParentTabletOptions;'#13#10'FrmHome_1.Touch.TabletOptions;'#13#10'FrmH' +
    'ome_1.Touch.InteractiveGestures;'#13#10'FrmHome_1.Touch.InteractiveGes' +
    'tureOptions;'#13#10'FrmHome_1.Touch.ParentTabletOptions;'#13#10'FrmHome_1.To' +
    'uch.TabletOptions;FrmHome.Height;'#13#10
  Spacing = 2
  PixelsPerInch = 96
  TextHeight = 13
  object borderPanel: TFBorderPanel
    Left = 0
    Top = 0
    Width = 1248
    Height = 494
    North = pnlTop
    Center = pnlCenter
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    NorthCollapsible = False
    NorthSplittable = False
    NorthOpen = True
    SouthOpen = True
    EastOpen = True
    WestOpen = True
    SouthCollapsible = False
    SouthSplittable = False
    EastCollapsible = True
    EastSplittable = True
    WestCollapsible = True
    WestSplittable = True
    WOwner = FrInterno
    WOrigem = EhNone
    NorthSizePercent = 0
    SouthSizePercent = 0
    EastSizePercent = 0
    WestSizePercent = 0
    Align = alClient
    ExplicitLeft = 92
    ExplicitTop = 48
    ExplicitWidth = 185
    ExplicitHeight = 160
    object pnlTop: TFVBox
      Left = 0
      Top = 0
      Width = 1244
      Height = 75
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stBoxShadow
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 1
      Margin.Right = 1
      Margin.Bottom = 1
      Spacing = 0
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 5
      BoxShadowConfig.VerticalLength = 0
      BoxShadowConfig.BlurRadius = 30
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 8
      object FHBox2: TFHBox
        Left = 0
        Top = 0
        Width = 1241
        Height = 64
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 248
          Height = 60
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stBoxShadow
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 5
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 5
          BoxShadowConfig.VerticalLength = 5
          BoxShadowConfig.BlurRadius = 64
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 8
          object FHBox3: TFHBox
            Left = 0
            Top = 0
            Width = 185
            Height = 16
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
          end
          object FGridPanel2: TFGridPanel
            Left = 0
            Top = 17
            Width = 242
            Height = 30
            Caption = 'FGridPanel2'
            ColumnCollection = <
              item
                Value = 35.580564301561310000
              end
              item
                Value = 64.419435698438690000
              end>
            ControlCollection = <
              item
                Column = 0
                Control = imgLogo
                Row = 0
              end
              item
                Column = 1
                Control = lblSistema
                Row = 0
              end>
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            RowCollection = <
              item
                Value = 100.000000000000000000
              end
              item
                SizeStyle = ssAuto
              end>
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            AllRowFlex = False
            WOwner = FrInterno
            WOrigem = EhNone
            ColumnTabOrder = False
            object imgLogo: TFImage
              Left = 41
              Top = 1
              Width = 45
              Height = 28
              Align = alRight
              Stretch = True
              OnClick = imgLogoClick
              ImageSrc = '/images/crmparts310038.png'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = False
              ExplicitLeft = 46
            end
            object lblSistema: TFLabel
              Left = 86
              Top = 1
              Width = 133
              Height = 33
              Align = alLeft
              Caption = 'CRMParts'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -27
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              OnClick = lblSistemaClick
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
            end
          end
          object hbBoxVersao: TFHBox
            Left = 0
            Top = 48
            Width = 242
            Height = 16
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 26
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            object FHBox9: TFHBox
              Left = 0
              Top = 0
              Width = 16
              Height = 8
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
            end
            object lblVersao: TFLabel
              Left = 16
              Top = 0
              Width = 42
              Height = 13
              Align = alRight
              Caption = 'Vers'#227'o:'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
            end
          end
        end
        object FHBox4: TFHBox
          Left = 248
          Top = 0
          Width = 987
          Height = 62
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          ParentBackground = False
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 10
          object FVBox3: TFVBox
            Left = 0
            Top = 0
            Width = 900
            Height = 61
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            object FHBox5: TFHBox
              Left = 0
              Top = 0
              Width = 64
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
            end
            object hBoxPesquisas: TFHBox
              Left = 0
              Top = 21
              Width = 894
              Height = 32
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              OnClick = hBoxPesquisasClick
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              object gpPesqCodigo: TFGridPanel
                Left = 0
                Top = 0
                Width = 593
                Height = 36
                Caption = 'gpPesqCodigo'
                ColumnCollection = <
                  item
                    SizeStyle = ssAbsolute
                    Value = 20.000000000000000000
                  end
                  item
                    SizeStyle = ssAbsolute
                    Value = 24.000000000000000000
                  end
                  item
                    SizeStyle = ssAuto
                    Value = 50.000000000000000000
                  end
                  item
                    Value = 100.000000000000000000
                  end
                  item
                    SizeStyle = ssAbsolute
                    Value = 50.000000000000000000
                  end>
                ControlCollection = <
                  item
                    Column = 0
                    Control = FHBox8
                    Row = 0
                  end
                  item
                    Column = 1
                    Control = imgBuscar
                    Row = 0
                  end
                  item
                    Column = 2
                    Control = edtBuscarCodigo
                    Row = 0
                  end
                  item
                    Column = 3
                    Control = btnSearchCodigo
                    Row = 0
                  end
                  item
                    Column = 4
                    Control = vBoxNovoImage
                    Row = 0
                  end>
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                RowCollection = <
                  item
                    SizeStyle = ssAuto
                  end
                  item
                    SizeStyle = ssAuto
                  end>
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                AllRowFlex = False
                WOwner = FrInterno
                WOrigem = EhNone
                ColumnTabOrder = False
                DesignSize = (
                  593
                  36)
                object FHBox8: TFHBox
                  Left = 1
                  Top = 17
                  Width = 20
                  Height = 8
                  Anchors = []
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                end
                object imgBuscar: TFImage
                  Left = 21
                  Top = 9
                  Width = 24
                  Height = 25
                  Anchors = []
                  Stretch = False
                  ImageSrc = '/images/700089.png'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxSize = 0
                  GrayScaleOnDisable = False
                  ExplicitTop = 1
                end
                object edtBuscarCodigo: TFString
                  Left = 45
                  Top = 1
                  Width = 324
                  Height = 41
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Buscar por C'#243'digo/Descri'#231#227'o Item'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 0
                  Align = alLeft
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  OnEnter = edtBuscarCodigoEnter
                  OnChanging = edtBuscarCodigoChanging
                  SaveLiteralCharacter = False
                  ExplicitHeight = 24
                end
                object btnSearchCodigo: TFButton
                  Left = 369
                  Top = 1
                  Width = 75
                  Height = 41
                  Align = alLeft
                  Caption = 'Buscar'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 2
                  Visible = False
                  OnClick = btnSearchCodigoClick
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
                object vBoxNovoImage: TFVBox
                  Left = 542
                  Top = 1
                  Width = 44
                  Height = 41
                  Align = alLeft
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 6
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 3
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  DesignSize = (
                    40
                    37)
                  object iconClassNovo: TFIconClass
                    Left = 0
                    Top = 0
                    Width = 26
                    Height = 26
                    Hint = 'Novo Or'#231'amento'
                    Anchors = []
                    Picture.Data = {
                      07544269746D6170C60A0000424DC60A00000000000036000000280000001A00
                      00001A0000000100200000000000900A00000000000000000000000000000000
                      0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      67006767670067676700F0F0F000F0F0F000F0F0F00067676700676767006767
                      6700F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700F0F0F000F0F0F000F0F0F000676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      67006767670067676700676767006767670067676700F0F0F000F0F0F000F0F0
                      F000676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      670067676700676767006767670067676700676767006767670067676700F0F0
                      F000F0F0F000F0F0F00067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      670067676700F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                      6700676767006767670067676700F0F0F000F0F0F000F0F0F000676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      67006767670067676700F0F0F000676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      670067676700676767006767670067676700F0F0F00067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700F0F0F0006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700F0F0F0006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      67006767670067676700F0F0F000676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      670067676700F0F0F0006767670067676700676767006767670067676700F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                      67006767670067676700676767006767670067676700F0F0F000676767006767
                      670067676700F0F0F000F0F0F000F0F0F0006767670067676700676767006767
                      670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000676767006767670067676700676767006767670067676700676767006767
                      670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                      6700676767006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F0006767670067676700676767006767670067676700676767006767
                      6700676767006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F0006767670067676700676767006767670067676700F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000676767006767670067676700676767006767
                      6700676767006767670067676700676767006767670067676700F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000676767006767670067676700F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00067676700676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                      6700676767006767670067676700676767006767670067676700676767006767
                      67006767670067676700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F0006767670067676700676767006767670067676700676767006767
                      670067676700676767006767670067676700F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000676767006767670067676700676767006767
                      67006767670067676700676767006767670067676700F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000676767006767
                      670067676700676767006767670067676700676767006767670067676700F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000676767006767670067676700676767006767670067676700676767006767
                      6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F00067676700676767006767
                      6700F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                      F000F0F0F000F0F0F000F0F0F000F0F0F000}
                    OnClick = iconClassNovoClick
                    IconClass = 'user-plus'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Size = 26
                    Color = 6776679
                  end
                end
              end
            end
          end
          object FVBox1: TFVBox
            Left = 900
            Top = 0
            Width = 77
            Height = 61
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            object FHBox7: TFHBox
              Left = 0
              Top = 0
              Width = 64
              Height = 22
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
            end
            object FHBox1: TFHBox
              Left = 0
              Top = 23
              Width = 72
              Height = 34
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              DesignSize = (
                68
                30)
              object imageUsuario: TFImage
                Left = 0
                Top = 0
                Width = 30
                Height = 30
                Hint = 'Usu'#225'rio'
                Anchors = []
                Stretch = False
                OnClick = imageUsuarioClick
                ImageSrc = '/images/700090.png'
                WOwner = FrInterno
                WOrigem = EhNone
                BoxSize = 0
                GrayScaleOnDisable = False
              end
            end
          end
        end
      end
    end
    object pnlCenter: TFVBox
      Left = 2
      Top = 76
      Width = 572
      Height = 389
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 1
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      object pgctrlPrincipal: TFPageControl
        Left = 0
        Top = 0
        Width = 555
        Height = 351
        ActivePage = tabHome
        TabOrder = 0
        TabPosition = tpTop
        OnChange = pgctrlPrincipalChange
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        RenderStyle = rsTabbed
        object tabHome: TFTabsheet
          Caption = 'Or'#231'amentos do cliente: '
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
          ExplicitLeft = 0
          ExplicitTop = 0
          ExplicitWidth = 0
          ExplicitHeight = 0
        end
      end
    end
  end
  object popMenuLogin: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 345
    Top = 160
    object mmPerfil: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Perfil'
      ImageIndex = 310061
      OnClick = mmPerfilClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{5F7AB040-819B-4B16-86C2-AAA875DAA46B}'
    end
    object mmAlterarSenha: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Alterar Senha'
      ImageIndex = 310067
      OnClick = mmAlterarSenhaClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{088B3F17-36C0-4813-8F26-56191DC488EA}'
    end
    object mmHelp: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Help'
      ImageIndex = 7000109
      OnClick = mmHelpClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{403F7BB9-CC8F-4A33-BEF1-B30C4B37AD88}'
    end
    object FMenuItem1: TFMenuItem
      AutoHotkeys = maManual
      Caption = '-'
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{0FFC53E9-624B-4BC4-A56D-BBCD934385F2}'
    end
    object mmSair: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Sair'
      ImageIndex = 310060
      OnClick = mmSairClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{2B518677-4731-4FF5-87F3-1AE12EA0C456}'
    end
  end
  object imageList: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 615
    Top = 217
    object FMenuItem2: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'ImagemUsuario'
      ImageIndex = 700090
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{60F3998E-8E77-4D05-99FD-807E5ADAAD10}'
    end
    object FMenuItem3: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'ImagemSair'
      ImageIndex = 310065
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{F04714CE-74EF-46BC-B5F8-1EE1E22728B6}'
    end
    object FMenuItem4: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Calendar'
      ImageIndex = 310062
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{1171A811-9853-48B2-ABBC-2E83689EC050}'
    end
    object FMenuItem5: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Globo'
      ImageIndex = 310063
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{72663FB0-18ED-4BA4-AE2E-98FEBA45926F}'
    end
    object FMenuItem6: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'User Cicle'
      ImageIndex = 310064
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{FF491F45-D306-48F0-B2A1-764A30EB6AD5}'
    end
    object FMenuItem7: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Account Logout Black'
      ImageIndex = 310065
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{29CEF3D5-21FB-4061-899C-566102111D5E}'
    end
    object FMenuItem8: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Version'
      ImageIndex = 310066
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{FFD43B50-3D86-42A6-96BD-5867C1AE9374}'
    end
    object FMenuItem9: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Alterar Senha'
      ImageIndex = 310067
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{99140CB8-1C41-4222-8E55-C20A34650164}'
    end
    object Pesquisar: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Pesquiisar'
      ImageIndex = 700089
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{AF1D1D3D-D665-49C0-9E9D-A256A508C7A6}'
    end
    object FMenuItem10: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'LogoNbs'
      ImageIndex = 310038
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{88536D5D-1750-424A-998F-0B0E4285AE5F}'
    end
  end
  object tbUserInformation: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Divis'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'USER_INFORMATION'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34001'
    DeltaMode = dmChanged
  end
  object tbMenu: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Menu'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU_SUPER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Menu Super'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_FORM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Form'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORDEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Ordem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Id. Imagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'DBMENU'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34002'
    DeltaMode = dmChanged
  end
  object tbSistemaAcesso: TFTable
    FieldDefs = <
      item
        Name = 'COD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'C'#243'd. Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso Pai'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LEVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Level'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERIGOSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Perigoso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORD_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'Ord Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENU_ACESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Menu Acesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ACESSO_FUNCAO_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'C'#243'd. Acesso Fun'#231#227'o Pai'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SISTEMA_ACESSO'
    Cursor = 'SISTEMA_ACESSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34003'
    DeltaMode = dmChanged
  end
  object tbClienteDiverso: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Endere'#231'o Eletronico'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34005'
    DeltaMode = dmChanged
  end
  object tbCrmPartsFilaDaVez: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FILA_CRMPARTS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        Caption = 'Fila Crmparts'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'VW_CRM_PARTS_FILA_DA_VEZ'
    Cursor = 'VW_CRM_PARTS_FILA_DA_VEZ'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34006'
    DeltaMode = dmChanged
  end
  object tbClienteSelecionarEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CLIENTE_SELECIONAR_EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34007'
    DeltaMode = dmChanged
  end
  object tbMapa: TFTable
    FieldDefs = <
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ORC_MAPA'
    Cursor = 'ORC_MAPA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340057;34008'
    DeltaMode = dmChanged
  end
end
