object RequisitarOsOrcRelacItensCompRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '44208'
  Height = 299
  Width = 442
  object tbRequisitarItensComprarCache: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_PECA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Peca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ONDE_COMPRAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Onde Comprar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_COMPRAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Comprar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_ORC_DISPLAY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orc Display'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_ITENS_COMPRAR_CACHE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44208;44201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRequisitarItensComprar: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_PECA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Peca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Fornecedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ONDE_COMPRAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Onde Comprar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_COMPRAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Comprar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_ORC_DISPLAY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Orc Display'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'REQUISITAR_ITENS_COMPRAR_CACHE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '44208;44202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
