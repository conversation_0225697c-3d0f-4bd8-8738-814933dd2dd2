object FrmPoliticaLiberarDesconto: TFForm
  Left = 44
  Top = 162
  ActiveControl = vboxPrincipal
  Caption = 'Liberar Desconto'
  ClientHeight = 535
  ClientWidth = 820
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '16701'
  ShortcutKeys = <>
  InterfaceRN = 'PoliticaLiberarDescontoRN'
  Access = False
  ChangedProp = 
    'FrmPoliticaLiberarDesconto.Width;'#13#10'FrmPoliticaLiberarDesconto.He' +
    'ight;'#13#10#13#10'FrmPoliticaLiberarDesconto.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 820
    Height = 535
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object pcLiberarDesconto: TFPageControl
      Left = 0
      Top = 0
      Width = 801
      Height = 517
      ActivePage = tabUsuario
      TabOrder = 0
      TabPosition = tpTop
      OnChange = pcLiberarDescontoChange
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabFuncao: TFTabsheet
        Caption = 'Fun'#231#227'o'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox1: TFVBox
          Left = 0
          Top = 0
          Width = 793
          Height = 489
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox1: TFHBox
            Left = 0
            Top = 0
            Width = 721
            Height = 65
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox2: TFVBox
              Left = 0
              Top = 0
              Width = 525
              Height = 49
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel1: TFLabel
                Left = 0
                Top = 0
                Width = 35
                Height = 13
                Caption = 'Fun'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object edtPesquisar: TFString
                Left = 0
                Top = 14
                Width = 121
                Height = 24
                TabOrder = 0
                AccessLevel = 0
                Flex = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                IconDirection = idLeft
                CharCase = ccNormal
                Pwd = False
                Maxlength = 0
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                OnEnter = edtPesquisarEnter
                SaveLiteralCharacter = False
                TextAlign = taLeft
              end
            end
            object FVBox3: TFVBox
              Left = 525
              Top = 0
              Width = 80
              Height = 55
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 21
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object btnPesquisar: TFButton
                Left = 0
                Top = 0
                Width = 77
                Height = 33
                Caption = 'Pesquisar'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 0
                OnClick = btnPesquisarClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                  610000015D4944415478DA95924D28445114C7DFF551A6A421CB215958CECA4A
                  B290241F1B6343C946D95A594C22CACECE82958F48598C598C689295B5B5AC26
                  24110A0B91E6F9DD3AB7CE3C6F5EBD5BBFFEEF9C7BEEBFF3CEBDC653CB183380
                  6C439B4AFFC02AACFBBEFFED0596918335C8114C78D5D733A431790A33D842E6
                  24B70C1BF001F5D00339488A49BBEE84B3A6032D493CC6E6C9BF368D69426EC5
                  64919A356D60832C9CB23152AD7FEAFA910B994982DAB233B841BB6090E47984
                  4103F225612BB5AFCEE0136D846E92571106B5C8AF84760EF7CEE012ED856992
                  071106CDC89B84496ADF9DC10CBA0B8F9072FF1662B084ACC00335293DC4845C
                  591D1C4A27E5C0E151A420E110FBC5E03BB02FD00DD076B200D7D002F330ACFC
                  32181C5718289333E9246CBDD8E9CBF73826F90A03755593300B69B057659FF8
                  26DCC13E4CE94E8C1763D981217B764E92EA8B65A04C76C0DE5E36B68132E984
                  D21FD0B779D2A82A25DE0000000049454E44AE426082}
                ImageId = 700085
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
          end
          object gridFuncao: TFGrid
            Left = 0
            Top = 66
            Width = 719
            Height = 136
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbEmpresasFuncoes
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = False
            Paging.PageSize = 0
            Paging.DbPaging = False
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            Columns = <
              item
                Expanded = False
                FieldName = 'COD_FUNCAO'
                Font = <>
                Title.Caption = 'C'#243'd. Fun'#231#227'o'
                Width = 40
                Visible = False
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{0D369815-46AB-4BF1-8A69-1208A3A08FBC}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'DESCRICAO_CODFUNCAO'
                Font = <>
                Title.Caption = 'Fun'#231#227'o'
                Width = 200
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{6F592D74-89C1-43C5-A0AC-62A2933A809A}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'MAXIMO_DESC_BALCAO'
                Font = <>
                Title.Caption = '% M'#225'x Pe'#231'as'
                Width = 110
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{92C8F28A-B996-4C32-AC25-CFC61348FD85}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = '##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{CAC3B210-5D6E-49C9-83D8-40654ECCDED0}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end
              item
                Expanded = False
                FieldName = 'MAX_DESC_SERV'
                Font = <>
                Title.Caption = '% M'#225'x Serv'
                Width = 105
                Visible = True
                Precision = 0
                TextAlign = taRight
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <
                  item
                    Expression = '*'
                    EvalType = etExpression
                    GUID = '{BD7EF59B-0EE3-4CF9-A8D4-59F8ED320358}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Mask = '##0.00'
                    PadLength = 0
                    PadDirection = pdNone
                    MaskType = mtDecimal
                  end>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{E232D668-4B29-4FA2-85FD-CE20F7923A3D}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
              end>
          end
          object FVBox4: TFVBox
            Left = 0
            Top = 203
            Width = 720
            Height = 117
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox2: TFHBox
              Left = 0
              Top = 0
              Width = 714
              Height = 53
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox5: TFVBox
                Left = 0
                Top = 0
                Width = 445
                Height = 50
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel2: TFLabel
                  Left = 0
                  Top = 0
                  Width = 217
                  Height = 16
                  Caption = 'Percentual de Desconto de Pe'#231'as'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object chkTodasFuncPecas: TFCheckBox
                  Left = 0
                  Top = 17
                  Width = 313
                  Height = 17
                  Caption = 'Aplicar a Todas as Fun'#231#245'es Acima'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
              object FHBox3: TFHBox
                Left = 445
                Top = 0
                Width = 170
                Height = 46
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object edtFuncaoPecas: TFDecimal
                  Left = 0
                  Top = 0
                  Width = 89
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
                object btnSalvarFuncaoPecas: TFButton
                  Left = 89
                  Top = 0
                  Width = 75
                  Height = 33
                  Caption = 'Salvar'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnSalvarFuncaoPecasClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
                    C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
                    D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
                    1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
                    D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
                    9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
                    A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
                    BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
                  ImageId = 700080
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
              end
            end
            object FHBox4: TFHBox
              Left = 0
              Top = 54
              Width = 714
              Height = 53
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox6: TFVBox
                Left = 0
                Top = 0
                Width = 445
                Height = 50
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel3: TFLabel
                  Left = 0
                  Top = 0
                  Width = 234
                  Height = 16
                  Caption = 'Percentual de Desconto de Servi'#231'os'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object chkTodasFuncServicos: TFCheckBox
                  Left = 0
                  Top = 17
                  Width = 313
                  Height = 17
                  Caption = 'Aplicar a Todas as Fun'#231#245'es Acima'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
              object FHBox5: TFHBox
                Left = 445
                Top = 0
                Width = 170
                Height = 46
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object edtFuncaoServicos: TFDecimal
                  Left = 0
                  Top = 0
                  Width = 89
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
                object btnSalvarFuncaoServicos: TFButton
                  Left = 89
                  Top = 0
                  Width = 75
                  Height = 33
                  Caption = 'Salvar'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnSalvarFuncaoServicosClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
                    C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
                    D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
                    1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
                    D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
                    9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
                    A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
                    BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
                  ImageId = 700080
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
              end
            end
          end
        end
      end
      object tabUsuario: TFTabsheet
        Caption = 'Usu'#225'rio'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vboxAbaUsuario: TFVBox
          Left = 0
          Top = 0
          Width = 793
          Height = 489
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox7: TFVBox
            Left = 0
            Top = 0
            Width = 792
            Height = 321
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 5
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox18: TFHBox
              Left = 0
              Top = 0
              Width = 781
              Height = 25
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 5
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox19: TFHBox
                Left = 0
                Top = 0
                Width = 29
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblFuncao: TFLabel
                Left = 29
                Top = 0
                Width = 57
                Height = 19
                Caption = 'Fun'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clBlue
                Font.Height = -16
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FHBox20: TFHBox
                Left = 86
                Top = 0
                Width = 29
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
            object gridUsuario: TFGrid
              Left = 0
              Top = 26
              Width = 719
              Height = 72
              TabOrder = 1
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbEmpresasUsuarios
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              Columns = <
                item
                  Expanded = False
                  FieldName = 'COD_EMPRESA'
                  Font = <>
                  Title.Caption = 'C'#243'd. Empresa'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{2BC5C3DF-50E6-42F7-91F3-088FC6CDAD43}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'NOMECOMPLETO_LOGIN'
                  Font = <>
                  Title.Caption = 'Usu'#225'rios'
                  Width = 120
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{A96EF205-693A-41D0-B7DB-3254088B027C}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'MAXIMO_DESC_BALCAO'
                  Font = <>
                  Title.Caption = '% M'#225'x Pe'#231'as'
                  Width = 110
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{C30AE3EB-A6E4-4137-811A-E610C65E7C65}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = '##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{2D98207A-2F82-4BC5-8C60-C5C282F87895}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'MAXIMO_DESCONTO_SERVICO'
                  Font = <>
                  Title.Caption = '% M'#225'x Serv'
                  Width = 105
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{9358DD27-CC00-45E4-A585-6B9729532FBD}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = '##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{908D9F72-E9C1-4FB3-869F-D9502ADE3C15}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
            object FVBox10: TFVBox
              Left = 0
              Top = 99
              Width = 720
              Height = 117
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox7: TFHBox
                Left = 0
                Top = 0
                Width = 714
                Height = 53
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FVBox11: TFVBox
                  Left = 0
                  Top = 0
                  Width = 445
                  Height = 50
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel5: TFLabel
                    Left = 0
                    Top = 0
                    Width = 217
                    Height = 16
                    Caption = 'Percentual de Desconto de Pe'#231'as'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object chkTodasUsuarioPecas: TFCheckBox
                    Left = 0
                    Top = 17
                    Width = 313
                    Height = 17
                    Caption = 'Aplicar a Todos Usu'#225'rios Acima'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taAlignTop
                  end
                end
                object FHBox8: TFHBox
                  Left = 445
                  Top = 0
                  Width = 170
                  Height = 46
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 5
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edtUsuarioPecas: TFDecimal
                    Left = 0
                    Top = 0
                    Width = 89
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Maxlength = 0
                    Precision = 0
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taRightJustify
                    Mode = dmDecimal
                  end
                  object btnUsuariosPecas: TFButton
                    Left = 89
                    Top = 0
                    Width = 75
                    Height = 33
                    Caption = 'Salvar'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 1
                    OnClick = btnUsuariosPecasClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
                      C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
                      D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
                      1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
                      D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
                      9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
                      A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
                      BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
                    ImageId = 700080
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconReverseDirection = False
                  end
                end
              end
              object FHBox9: TFHBox
                Left = 0
                Top = 54
                Width = 714
                Height = 53
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FVBox12: TFVBox
                  Left = 0
                  Top = 0
                  Width = 445
                  Height = 50
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FLabel6: TFLabel
                    Left = 0
                    Top = 0
                    Width = 234
                    Height = 16
                    Caption = 'Percentual de Desconto de Servi'#231'os'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object chkTodasUsuarioServicos: TFCheckBox
                    Left = 0
                    Top = 17
                    Width = 313
                    Height = 17
                    Caption = 'Aplicar a Todos Usu'#225'rios Acima'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -12
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taAlignTop
                  end
                end
                object FHBox10: TFHBox
                  Left = 445
                  Top = 0
                  Width = 170
                  Height = 46
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 5
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edtUsuarioServicos: TFDecimal
                    Left = 0
                    Top = 0
                    Width = 89
                    Height = 24
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Maxlength = 0
                    Precision = 0
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taRightJustify
                    Mode = dmDecimal
                  end
                  object btnUsuariosServicos: TFButton
                    Left = 89
                    Top = 0
                    Width = 75
                    Height = 33
                    Caption = 'Salvar'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 1
                    OnClick = btnUsuariosServicosClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
                      C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
                      D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
                      1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
                      D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
                      9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
                      A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
                      BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
                    ImageId = 700080
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconReverseDirection = False
                  end
                end
              end
            end
          end
        end
      end
      object tabEmpresas: TFTabsheet
        Caption = 'Empresas'
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object FVBox9: TFVBox
          Left = 0
          Top = 0
          Width = 793
          Height = 489
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          ExplicitTop = 226
          ExplicitWidth = 787
          ExplicitHeight = 241
          object hboxLblEmpresasUsu: TFHBox
            Left = 0
            Top = 0
            Width = 781
            Height = 35
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 5
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox16: TFHBox
              Left = 0
              Top = 0
              Width = 29
              Height = 10
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblDescricaoEmpresaUsuario: TFLabel
              Left = 29
              Top = 0
              Width = 175
              Height = 19
              Caption = 'Empresas do Usu'#225'rio '
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlue
              Font.Height = -16
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object FHBox17: TFHBox
              Left = 204
              Top = 0
              Width = 29
              Height = 10
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object FHBox6: TFHBox
            Left = 0
            Top = 36
            Width = 781
            Height = 137
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox13: TFVBox
              Left = 0
              Top = 0
              Width = 87
              Height = 131
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object btnIncluirEmpresa: TFButton
                Left = 0
                Top = 0
                Width = 79
                Height = 57
                Caption = 'Incluir'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 0
                OnClick = btnIncluirEmpresaClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
                  5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
                  2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
                  33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
                  6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
                  5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
                  9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
                  CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
                  84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
                  E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
                  BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
                  603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
                  84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
                  281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
                  0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
                  0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
                  C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
                  37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
                  D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
                  768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
                  0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
                  19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
                  B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
                  74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
                  3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
                  AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
                  92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
                  69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
                  AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
                  08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
                  71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
                  C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
                  44AE426082}
                ImageId = 6
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object btnExcluirEmpresa: TFButton
                Left = 0
                Top = 58
                Width = 79
                Height = 57
                Caption = 'Excluir'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                Layout = blGlyphTop
                ParentFont = False
                TabOrder = 1
                OnClick = btnExcluirEmpresaClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                  F80000016D4944415478DAEDD6CD2B055118C7F173950D29B150364A62435E36
                  0A795F58B3C142FC0752B2F2BA932C6D2C9562C15E79B90965E3A56CA49494A2
                  48E9DAA8E1FB34CFD4699A193323BBFBD4A7B973CF39F7377316CFB919F3CF95
                  8939AF09D5A8D2FB07DCE3EAAF01FD58464BC8F80566B09F26A00307B8C50A4E
                  F1AC631568C7346AD1A7E38902F650A3DBF31132A744B7E90E0351013D984581
                  35D686577D83A8AA4339CEACEF1CCCE1C40B90ED5842191A718D7793AC4AADB5
                  6FFE00AFBA71A46F944D1810B8F6B700B98E6142C727F18535BD5FC7863C69DA
                  8005CC5BF3B2D63CA96F2CEABC7C403E201F1033403AE72586B06BDC7E228BBD
                  2678A8D75EBD3A3A2E7D6C103B6836D641E40F901F7A316EC392F65B892EE3B6
                  0329698A85FAA452E338C6A371DB7B8371CF0A272C406A049BC6ED2FB23DE7F8
                  34C1558456DD26091FC5963D21ECC019C6AABE419C7AC214B6FD0351275A313A
                  51AF9F832A871BDDA65CD084B8FF2A52D70F1A0B8E192C1E0DF5000000004945
                  4E44AE426082}
                ImageId = 4600235
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
            object gridEmpresas: TFGrid
              Left = 87
              Top = 0
              Width = 588
              Height = 120
              TabOrder = 1
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbBuscaLibEmpresaUsuario
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              Columns = <
                item
                  Expanded = False
                  FieldName = 'COD_EMPRESA'
                  Font = <>
                  Title.Caption = 'C'#243'd. Empresa'
                  Width = 40
                  Visible = False
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{17D2C2F8-05E2-4AC0-AA2D-1BE3DAC89569}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'EMPRESA_NOME_CODIGO'
                  Font = <>
                  Title.Caption = 'Empresa Usu'#225'rio'
                  Width = 200
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{C30759EF-68A2-445D-BC35-62DE57D6D33E}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'PERC_DESCONTO'
                  Font = <>
                  Title.Caption = '% M'#225'x Pe'#231'as'
                  Width = 110
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{D6174F7F-2DF0-49F8-8471-92B7CBF55F79}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = '##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{F144DAD3-890F-4265-8847-2DD77B76BB03}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end
                item
                  Expanded = False
                  FieldName = 'PER_DESCONTO_SERVICO'
                  Font = <>
                  Title.Caption = '% M'#225'x Serv'
                  Width = 100
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{2AAF5A14-69F6-42B6-98F3-D25F333A4C90}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = '##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{6BA30CB6-00B5-4DDB-837C-56BBC0EE14EB}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                end>
            end
          end
          object FVBox14: TFVBox
            Left = 0
            Top = 174
            Width = 744
            Height = 117
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox11: TFHBox
              Left = 0
              Top = 0
              Width = 714
              Height = 53
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox15: TFVBox
                Left = 0
                Top = 0
                Width = 445
                Height = 50
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel4: TFLabel
                  Left = 0
                  Top = 0
                  Width = 217
                  Height = 16
                  Caption = 'Percentual de Desconto de Pe'#231'as'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object chkTodasEmpPecas: TFCheckBox
                  Left = 0
                  Top = 17
                  Width = 313
                  Height = 17
                  Caption = 'Aplicar a Todas as Empresas Acima'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
              object FHBox12: TFHBox
                Left = 445
                Top = 0
                Width = 170
                Height = 46
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object edtEmpresasPecas: TFDecimal
                  Left = 0
                  Top = 0
                  Width = 89
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
                object btnEmpresasPecas: TFButton
                  Left = 89
                  Top = 0
                  Width = 75
                  Height = 33
                  Caption = 'Salvar'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnEmpresasPecasClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
                    C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
                    D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
                    1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
                    D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
                    9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
                    A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
                    BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
                  ImageId = 700080
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
              end
            end
            object FHBox13: TFHBox
              Left = 0
              Top = 54
              Width = 714
              Height = 53
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox16: TFVBox
                Left = 0
                Top = 0
                Width = 445
                Height = 50
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel7: TFLabel
                  Left = 0
                  Top = 0
                  Width = 234
                  Height = 16
                  Caption = 'Percentual de Desconto de Servi'#231'os'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object chkTodasEmpServicos: TFCheckBox
                  Left = 0
                  Top = 17
                  Width = 313
                  Height = 17
                  Caption = 'Aplicar a Todas as Empresas Acima'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
              object FHBox14: TFHBox
                Left = 445
                Top = 0
                Width = 170
                Height = 46
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 5
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object edtEmpresasServicos: TFDecimal
                  Left = 0
                  Top = 0
                  Width = 89
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Maxlength = 0
                  Precision = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Alignment = taRightJustify
                  Mode = dmDecimal
                end
                object btnEmpresasServicos: TFButton
                  Left = 89
                  Top = 0
                  Width = 75
                  Height = 33
                  Caption = 'Salvar'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnEmpresasServicosClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000E64944415478DAED954D0AC2301085335D28889EA9B8722F1EC0859E
                    C433B8D0AD5B71EF5EBC456F5105413BBE485242989626CDCE0E3C12323F1F74
                    D20911D142297586A62ACE0A68079D98B9F29DA84F0FAC93C8E2AEDDA039206F
                    1FC07A030799036E289021864D4C86E563CEC7D01EDA40772877219D01D6EF24
                    D679DAB03D405B1F9204600B4990640001A27B9227053890A3E9C93AB6C93AB6
                    9200C63FC2F2820A0910726D4BE4CD2487AD2B01F48F77E90079424BE45D8300
                    A96C00C4037A4CD7125AD9A6B701FA4CD7FADAB601A23E59539D01F02780548F
                    BE6425054CCF50FB4DDB2F27D8FBBBCBAB8B310000000049454E44AE426082}
                  ImageId = 700080
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconReverseDirection = False
                end
              end
            end
          end
        end
      end
    end
  end
  object tbEmpresasFuncoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODFUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codfun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAXIMO_DESC_BALCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Maximo Desconto Balc'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_FUNCOES'
    Cursor = 'EMPRESAS_FUNCOES'
    MaxRowCount = 200
    OnAfterScroll = tbEmpresasFuncoesAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '16701;16701'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOMECOMPLETO_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nomecompleto Login'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAXIMO_DESCONTO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Maximo Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAXIMO_DESC_BALCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Maximo Desconto Balc'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    OnAfterScroll = tbEmpresasUsuariosAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '16701;16702'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbBuscaLibEmpresaUsuario: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO_NOME_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Nome Login'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PER_DESCONTO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Per Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'LIB_EMPRESA_USUARIO'
    Cursor = 'BUSCA_LIB_EMPRESA_USUARIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '16701;16703'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFuncoesSave: TFTable
    FieldDefs = <
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_FUNCOES'
    Cursor = 'EMPRESAS_FUNCOES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '16701;16704'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasUsuariosSave: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '16701;16705'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLibEmpresaUsuarioSave: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'LIB_EMPRESA_USUARIO'
    Cursor = 'BUSCA_LIB_EMPRESA_USUARIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '16701;16706'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
