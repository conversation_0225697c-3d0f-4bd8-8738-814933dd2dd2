object FrmCrmQuestionarioPergunta: TFForm
  Left = 321
  Top = 163
  ActiveControl = FVBox1
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Pergunta'
  ClientHeight = 482
  ClientWidth = 713
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '45205'
  ShortcutKeys = <>
  InterfaceRN = 'CrmQuestionarioPerguntaRN'
  Access = False
  ChangedProp = 
    'FrmCrmQuestionarioPergunta.Width;'#13#10'FrmCrmQuestionarioPergunta.He' +
    'ight;'#13#10'FrmCrmQuestionarioPergunta.ActiveControltbQuestionario.Ma' +
    'xRowCount;'#13#10'tbOpcoes.MaxRowCount;'#13#10'tbRespostasOpcoes.MaxRowCount' +
    ';'#13#10'tbQuestionarioCadastro.MaxRowCount;'#13#10'tbRespostasOpcoes.Master' +
    'Fields;'#13#10'tbRespostasOpcoes.DetailFilters;'#13#10'tbRespostasOpcoes.Mas' +
    'terTabletbRespostasOpcoes.DeltaMode;'#13#10'tbRespostasOpcoes.MasterTa' +
    'bleFrmCrmQuestionarioPergunta.Width;'#13#10'tbRespostasOpcoes.MasterTa' +
    'ble;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 713
    Height = 482
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxBotoes: TFHBox
      Left = 0
      Top = 0
      Width = 750
      Height = 65
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 5
      Padding.Left = 5
      Padding.Right = 5
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 3
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        Visible = False
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnPesquisarTbPerguntas: TFButton
        Left = 60
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Pesquisar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnPesquisarTbPerguntasClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004BD4944415478DA9D955948636714C7CF4D62E292C47D19199BAB1444
          A6A363C1818A2B8856EA8E4A952A06D427A14F3EF4D127518A880BB850B562A5
          3EF8A80F76AC11DC1DC4655C06B154A341E3BEC6254BCFF932B9F5AA38A51F5C
          6EEE77BFFBFDBEF33FFF73C2D96C36E8E9E991A5A6A6FEE1E4E49400FF614824
          12B8BABA5A191E1E7E5D5050607D6E2D4780C1C1C1A0F8F8789DABAB6B083D7F
          6ED09ACBCB4B30180CBD333333A5C5C5C5A66701FDFDFD7C4A4A8A4EA9546AAC
          56EB6701777777603299E0FAFA1A767777FB2A2A2A7E181B1B333F0B484E4ED6
          A954AA470092E3E1B8BDBD85A3A323F0F4F4848B8B0B585F5FEF89898929319B
          CD9627017D7D7D3CE640A756AB4500DA9C9E777676D86965322928952AF0F2F2
          02A3D108EEEEEE80B2C2F1F1316C6C6CFC3E35355582D1DC3C02F4F6F6F26969
          690280E338766D6EFE0D6B6B1F715325787878B03993E91AE47227C068D90114
          0A05A039E0F0F010B6B6B67A878686CA6A6A6AAE440074119F9E9EAEC3133100
          7DB8BABA0A7ABD1EA2A2A284CD68D0FBD3D353D8DBDB03373737364F92C96432
          72161E682D373B3BBB5F04E8EEEEE633323274784A0D4DD2C70B0B8B909090C0
          64A1A48AC2E6242CC9676767A056AB00B567CEB2582CB0B2B2529A9898F88B08
          D0D5D5C5676666EA30690C303E3E0E1A0D0F010101F8A15D32FAF85F07DBD8EF
          F3F30BA0C028428A8CF2B4B0B0508207FB5504E8ECEC64004C9E864EF5E1C312
          BC79F3F5275938B0E7050400DDE9D5CDCD0DD6C305F8FBFB83542A65CF5817DA
          B8B8B82E11A0A3A38301BCBDBD35240F260B5EBDFA0A37E2D8C60F87D56A63F3
          24CDC181115EBC08C0643B33C0ECECAC168B560C686F6FE773727204805EBF0D
          21215F3269E8641209774F1A1B6E6CD79B40C7C7461601D995724511600EC480
          B6B63601400E595D5D83D0D030269154CA319908466BE9A28D2D167BBD6C6DFD
          05C1C1C1E0E2E2C200580BDAA4A42431A0A5A58501C84564B7F7EF67D1F75E98
          E860FCE89639C901A08DCDE63B94C405DB840125DA87F0F0701611D975626242
          8B452B06343737F3B9B9B9AC0EE8D494B8999959CCC36B78F93250482C398A0A
          9D6C6A306C836EE41D7C9BFA1DF8FAFAB21A2000F6242D16AD18D0D0D0C0E7E7
          E70B952C972BB0320F606969097C7CBC2128E80BB4A29A45717E7E0EDBDB7A98
          9E9E86136C116161A110F5F61BCC831F6B19A3A3A35A54E331202F2F4F879529
          F422B95CCE4E849509FBFBFB9FA2B0319DA9C991EEF3F3F3D0DADA0A111111A0
          D56A51520D0C0C0C68510D31A0A9A989E5009D2000689E7A8CA305D04573944C
          2A2C85428E4DD000D5D5D530323202E81C282A2A026767E7EF232323FB4480FA
          FAFA4711382034C8AA8E06E8680974276B52CFAAADAD85C9C949888E8EA64EFB
          23E6B44104A8ABAB7B1260EF3B1C3CF72F47D12C2F2F4355551559140203038D
          58800598BF3F05406363239F9595A5C3F034CF6DC63D51D63447AD7C7171112A
          2B2B616E6E8EF2777572729285397CC700480F292F2F9F469BFA383AE34399EE
          3F3F7CEF806015435959193306CAB889EFDE32406C6CACAAB0B0F0673F3FBF30
          94E89664B257ACF55EF55A858B06E5C171C7F7368448D11437F8E7F51BE6A314
          FBD23CAEFD89739C0617C8F0E64CBD0CFEDF20FD2CB8DF354AED850630E1FFB6
          E91F70B7FB1897F803840000000049454E44AE426082}
        ImageId = 13
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnIncluirTbPerguntas: TFButton
        Left = 125
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Novo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnIncluirTbPerguntasClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003EC4944415478DAB5965B681C5518C7FF33BB9BDD6433934DD6D840B5
          5523B489ED830F8A7879B708E2B320EA8B977A01056DB5F6C10749B14F229446
          2822A5488B0FA542DFBCE083340A5E9A6EBA694D8D4D1392B6BB3BDDD9CD6E32
          33E7F83FB333B31B6603C5E20CDF7E67CE39F3FDCE77396756C3FF7C69B73BB1
          6EEF36A97280BC9B3A4F9DA54E511C9AA953DFA0FE2B6B4C57BB020E7DFAC92B
          5493DD8CF7F6369149AFC134EB30FAEBFE732AE550D2709C35DCB76DF1483E6F
          9FA7B9054E9FCE1A1716BA01E4337B9EC5E8E8280DF446C6A5AC51AE438839B8
          CE15B617D979038DF9A3FEB8187E0ECB2B750CE6964F99A6F74722A1CDD2EC8F
          84946380B7DF7C079EE775ACDDA5417A2EE7E02CEC41CFBDEFB2EF2AD617BEC1
          E078039A9641B9A0411B7914954A1AD9AC75DA30C414CDFE4EF99EE1723600F6
          BDF7216AB55A6BE5EA960CA7BCC65514097829C29A3B7E4132F948E0A185CACC
          603496D93E3E1900BEA5174B5D01522AF3EAED126516EEB5A7D1FFE07748A69E
          84AEF76C5A08CE7A11F6E531053908E86709F82D06B06DBB05A0402E736486A1
          281072906171D84E6E0A50E1CA6C1FFB89AB9F6AE5A1703606A8DEBA158447C9
          0A746D3A007C8CBE07F6219D99E073BCBA5DB780EAEC2E053847E34A7E20E04C
          0C6059960F8050BF6502CEFB005D2F300F2791DB79137A22DF75F5A97BC69148
          C8D083D304FC1C0754CA103E007E9235FD2221339422DCC5630CD33A81A9AE00
          37FF30FAFB9B6768FC57E6E06BE6602E06A8544AAC79D9AA23C998E30A0D1691
          D02FC15B3A8CA187547F8395D3E7BF97DB799D1E0DFB002BFD1446464A932D80
          768280660C502ADD8C92EC271A3586E70212DA1CBDB90AB1F4993F3FBD6D37C1
          0E9AFF1483F27C031767E731B663FE031A3F49E37FC736DADED7DE42B3B11A25
          19116485B1BD4410F784B6CC7095A8D5F1E3706612526491EAD98A7353F378FC
          B13F9F50B1EF7A54284063B5DE5145A20D4215498649D35768DCE28B04681E87
          74027AD197BD1FC74F14B0F7F5890D251603ACD6ED68A3B500AA2102A0CB870A
          3DA16865CEF1203C039E303194DF852347BFC0FEF70F7405E804780A50AB5769
          AF1D9ED003E1971522CF5A7DD25F809ABF65642B019F6F0AE82160CD07D85647
          92C33349B49F6508EAEC9330CC1CBE3AFEA50224E017F946804140F5C5175EE6
          69EAB457AF6E213B8022285F74E4A8D53607864280FA10354348081822A0A400
          AEB30EDFF95612028888562FD1F6A43D476060E0AE1030CC5E8BA2CE7D190206
          08B014E04EAE00B0456D6C753C757A9039F0D1FE6386693C7F2700AB629D3A34
          71F85536EDC08308A0B43AE80D8AFAB8AB73204D51094B053A19CCD3FD44B445
          C55B7DA5AA1DE20563B7FFAFE2BF5EFF02A8A671371FD8FBD30000000049454E
          44AE426082}
        ImageId = 6
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnExcluirTbPerguntas: TFButton
        Left = 190
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Excluir'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnExcluirTbPerguntasClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
          426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
          DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
          DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
          FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
          C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
          8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
          C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
          BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
          93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
          6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
          A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
          27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
          ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
          7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
          3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
          D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
          A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
          8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
          D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
          5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
          9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
          7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
          102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
          005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
          C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
          3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
          3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
          9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
          072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
          6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
          9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
          4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
          042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
          003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
          72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
          29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
          CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
          32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
          CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
          00000049454E44AE426082}
        ImageId = 8
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnEditarTbPerguntas: TFButton
        Left = 255
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Alterar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 4
        OnClick = btnEditarTbPerguntasClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003094944415478DAB5955B4853711CC7BF53A7E1D26990E14374F172BA
          5850910F163DFA641245049104654A48D04BB59C5A14A5250425157651307B31
          ED02068BE841891E4C66D843584FBA34DD76B6E32EB1D13CFFFEFF73F3B88B4E
          C13FFBEFFF3FE7FCF7FDFCAE6706ACF230E82F5AEEDEAAA14BFB4A8422E1C89E
          86C66BDF9602909AEAF3C8CDCD5D96B8DD6EC7C74F367C1EFC52D2DFFFFE07BD
          2526045CB9548F4020B0408010A24D7A052212F68D81810194951D405E5E9E74
          EE4EEB6DD88747B89E9EDE9F4901241955987D88C8BE24F38828C266B3A1A2E2
          30CC663382C1204C269304B15CB61A96043C7CFC60D1B0549F3987376FDF81F7
          38639E2D09F0FBFD3ACB45885268945051CB89EC0EC6271CE05D2E0C0D7F9584
          AC0D4DC979E09B9D8D0A8F2429C79E88DA338FC78BC8BF3084591FDD7B505979
          0423559A64D5DE6E74C705088280F6A78F965549A5FBF623A7B31C9C05C8F875
          08A37D83A000437C80D70366274428162BABEC060D1914AF4418D38CF81B0C80
          6F2CD4C4C3438318732031C0EBE5218A4A49AA252A12A582E4FBFAE74ECBA618
          713A9A28E0665C00CFBBF1ACE34952A1291FB526144F9864B7DB199BE0051524
          87CC7975738CF8F78DC771BAF99541D12671012EE78C5629D0BA58D481085CF5
          B1E2DC8D61B4F57E50CB3431C039338DCEAE8E6585656D6D1F8A0E1E55FB2045
          4A50A210CD4C4FC99646558FEC09E0B66EC1EE63F3E239D7C7248FB716708B36
          5A0A05CC31C09FE949AD6AF4B9A0050ADE5AC0CA0F75274A71D638442DEF454A
          7E8974BEB0689B0A4885F246D503D229202C01A61CE87AF9226E78E88F613FC5
          42F21AA9F93B34AFD85A54BC7D51401605F81860727262DE7AF5F5AC4BB65CFF
          984FBEB22FE676AA00133D1462103D601D05F00CF0DB312E7529D13A56295102
          DDBB489F1FB9AB396E970A584FEF0A7446F40033050817EA2E22333333A9268B
          1EA15008F7DBEE31C0067AE98906ACB136589E6765679D5C91BA3204AFD0D3D2
          DC5A4BB77E3AE7F400B64F67B9A0339B4EE646069D2C6146654D53CEA975AE4E
          166FF637E8D3CD394437DA6A8CFFFE8D6837CCAC0E4F0000000049454E44AE42
          6082}
        ImageId = 7
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnSalvarEdicaoTbPerguntas: TFButton
        Left = 320
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 5
        OnClick = btnSalvarEdicaoTbPerguntasClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3
          0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220
          AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950
          4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5
          5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37
          35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067
          603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7
          29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B
          DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA
          B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B
          A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194
          B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002
          EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715
          87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B
          AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9
          32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66
          5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E
          4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05
          05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF
          0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76
          093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0
          C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65
          C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70
          9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D
          80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12
          B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8
          6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087
          94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF
          0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34
          2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2
          134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000
          0049454E44AE426082}
        ImageId = 4
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnCancelarEdicaoTbPerguntas: TFButton
        Left = 385
        Top = 0
        Width = 65
        Height = 56
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 6
        OnClick = btnCancelarEdicaoTbPerguntasClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14
          8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912
          1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3
          FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965
          29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B
          D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422
          C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F
          6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B
          81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700
          558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45
          1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D
          E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E
          1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848
          DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655
          BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240
          F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D
          1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850
          7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945
          0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433
          D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F
          B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382
          CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6
          8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC
          EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924
          C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903
          98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0
          90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36
          8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000
          49454E44AE426082}
        ImageId = 9
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object pgControlTemplates: TFPageControl
      Left = 0
      Top = 66
      Width = 748
      Height = 461
      ActivePage = tabCruzamentoOpcao
      Align = alClient
      TabOrder = 1
      TabPosition = tpTop
      OnChange = pgControlTemplatesChange
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabListagem: TFTabsheet
        Caption = 'Listagem'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        ExplicitLeft = 0
        ExplicitTop = 0
        ExplicitWidth = 0
        ExplicitHeight = 0
        object vboxPrincipal2: TFVBox
          Left = 0
          Top = 0
          Width = 740
          Height = 433
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 5
          Padding.Left = 5
          Padding.Right = 5
          Padding.Bottom = 5
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hboxFiltros: TFHBox
            Left = 0
            Top = 0
            Width = 945
            Height = 30
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 4
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object ftQuestionario: TFCombo
              Left = 0
              Top = 0
              Width = 273
              Height = 21
              LookupTable = tbQuestionario
              LookupKey = 'COD_QUESTIONARIO'
              LookupDesc = 'COD_DESCRICAO_QUESTIONARIO'
              Flex = True
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Selecione'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = True
              UseClearButton = False
              HideClearButtonOnNullValue = False
              OnChange = ftQuestionarioChange
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
              MultiSelection = False
              IconReverseDirection = False
            end
            object FHBox1: TFHBox
              Left = 273
              Top = 0
              Width = 78
              Height = 34
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 3
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object ftAtivo: TFCheckBox
                Left = 0
                Top = 0
                Width = 50
                Height = 21
                Align = alRight
                Caption = 'Ativo'
                Checked = True
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -15
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                State = cbChecked
                TabOrder = 0
                CheckedValue = 'S'
                UncheckedValue = 'N'
                OnCheck = ftAtivoCheck
                ReadOnly = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
              end
            end
          end
          object gridTbPerguntas: TFGrid
            Left = 0
            Top = 31
            Width = 488
            Height = 279
            TabOrder = 1
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -11
            TitleFont.Name = 'Tahoma'
            TitleFont.Style = []
            Table = tbPerguntas
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Paging.Enabled = True
            Paging.PageSize = 3
            Paging.DbPaging = True
            FrozenColumns = 0
            ShowFooter = False
            ShowHeader = True
            MultiSelection = False
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            Crosstab.Enabled = False
            Crosstab.GroupType = cgtConcat
            EnablePopup = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditionEnabled = False
            AuxColumnHeaders = <>
            NoBorder = False
            ActionButtons.BtnAccept = False
            ActionButtons.BtnView = False
            ActionButtons.BtnEdit = False
            ActionButtons.BtnDelete = False
            ActionButtons.BtnInLineEdit = False
            CustomActionButtons = <>
            ActionColumn.Title = 'A'#231#245'es'
            ActionColumn.Width = 100
            ActionColumn.TextAlign = taCenter
            ActionColumn.Visible = True
            Columns = <
              item
                Expanded = False
                FieldName = 'COD_PERGUNTA'
                Font = <>
                Title.Caption = 'C'#243'digo'
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFInteger
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{9700DFD0-EE49-4EBB-B914-A9BC4A78E1D4}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'PERGUNTA_RESUMIDA'
                Font = <>
                Title.Caption = 'Pergunta'
                Visible = True
                Precision = 20
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = True
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 20
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{24652C50-54D3-4072-97C0-350342D6CAFC}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'SEQ'
                Font = <>
                Title.Caption = 'Seq.'
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{075CD25F-E08A-450F-9382-13376BE74FC6}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'ATIVA'
                Font = <>
                Visible = False
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <
                  item
                    Expression = 'ATIVA='#39'N'#39' or ATIVA IS NULL'
                    EvalType = etExpression
                    GUID = '{2750E1DF-C7D8-4E7F-9FAB-B90DE5010755}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 700106
                    Color = clBlack
                  end
                  item
                    Expression = 'ATIVA='#39'S'#39
                    EvalType = etExpression
                    GUID = '{6D362786-5F69-4357-A6E2-5CECEA006B94}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    ImageId = 7000105
                    Color = clBlack
                  end>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{046B7BEB-7520-4D5B-B1C2-57DD184EB36A}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
          end
          object hboxbotoestbperguntas: TFHBox
            Left = 0
            Top = 311
            Width = 487
            Height = 42
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hboxEspacamentoControleSequeciaTbPergunta: TFHBox
              Left = 0
              Top = 0
              Width = 41
              Height = 34
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object btnMoverSequenciaParaCima: TFButton
              Left = 41
              Top = 0
              Width = 48
              Height = 35
              Hint = 'Subir Sequ'#234'ncia Pergunta'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 1
              OnClick = btnMoverSequenciaParaCimaClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
                F4000001AA4944415478DAED96BF4BC34014C7EF25A8B5EAE0ACE6CEA493E0E4
                24D4C9C1C5A59376128A547015FC393928545C1C9C8A2082A0C5FFA050100417
                370982F647AEAD38084E454B6973BEB487285841FB430A7970DCF75DEE3D3EF7
                CD1102E49F035C0017C005E858007D581F27AA7DA210307BFABD21D3344B6D03
                300C6384942BD72887649758CAB282A8EC9603689A36D805CA15CAB1AF4F4434
                C579B8A5008C318F2A441CCBFC75BAEDA1136BAD02500CCA2E700EC8DCB15B91
                BA88C353F54190F574D68A341D40A7A38740C4B24C2F713CE198975D02449073
                54DDD55C403895CD449B06E0A3745310D89115B74251A6A06C1FA05E70963CAF
                7D036FDEC20C10702054C71D10104C6633B186010CC6E6F07467B593919C6A97
                27EFF3F9474363C79F01CC67B3A0531A428823595AB205CC66B2997863009425
                9D0937BEE02BF03F707E575DFF06A0B69FAE60DB7D599E48716BBA21001F63DB
                78B1A684ADACA673E99B0FB03A0012620BEFC19200B191E6FCB42180BACEFC00
                F09B70013A188052FCD0C0A2A32B407A2DCB2AB6D9016D82801A016227929CEF
                FEB54FE7FE90B8002E800BD0AC7807B333A6218963F5E20000000049454E44AE
                426082}
              ImageId = 4600315
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object btnMoverSequenciaParaBaixo: TFButton
              Left = 89
              Top = 0
              Width = 48
              Height = 35
              Hint = 'Descer Sequ'#234'ncia Pergunta'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 2
              OnClick = btnMoverSequenciaParaBaixoClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
                F4000001B64944415478DAED963B4B034110C7675613309DD848C4BBF3629732
                160A96010B6DA228D681682F245622588888686363888D209646D39A522D2CFC
                02B9DC83808518045F8924B74E9213147CE4128D446E61B8DD59E6BFBF99DB5D
                16E18F1B3A000E8003D0B6008140C0757B7D1347067D65C488AAAA7A4B01E47E
                7908997951154158C868DA564B01062569987338B764A28AAE6E3800FF134016
                A455DA6461E018530C75AF1E00591427C9B7C4806F67747DB729009F285DD1A7
                97AC0CC0A7155D3FFC0A4016E409443349DD0EB2A4A26BA1E62A208A61047CCD
                A2C8B9399E358CF44700962F4D0E0FD923021FA50A5C360550ABC24094B25FAF
                8DF83D430C728A7D0B60324831939FD2A0A7522DE418CA186AEA3BEDBA372165
                B7460B2E5A4179028971C04415097093B2A5FF0E526D1E23195D4DD4A36BEB14
                F8246987569BB386CF646EAB6F92B12A0C87E5ACA1ADD4AB69F7183282382088
                994FD4E28AA6CDDB11B47D0FF8FD7E77E1EEE19822C7DE4D7038520C6D0AAAA7
                E517012ACDEBF57ABA5CEE13EA8E58AEB362B914CCE5724F76B51ABE090541E8
                7621DBA74BAAB3502ACDD2E2F94674DAF741E20038000EC04FB517FD4FB721A1
                E6C2680000000049454E44AE426082}
              ImageId = 4600316
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
          end
        end
      end
      object tabPergunta: TFTabsheet
        Caption = 'Cadastro'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxPrincipalPerguntas: TFVBox
          Left = 0
          Top = 0
          Width = 740
          Height = 433
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object groupboxtbPerguntas: TFGroupbox
            Left = 0
            Top = 0
            Width = 726
            Height = 427
            Caption = 'Detalhes tbPerguntas'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentFont = False
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = False
            Closed = False
            Orient = coHorizontal
            Style = grp3D
            HeaderImageId = 0
            object vboxDetalhetbPerguntas: TFVBox
              Left = 2
              Top = 15
              Width = 722
              Height = 410
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 5
              Padding.Left = 5
              Padding.Right = 20
              Padding.Bottom = 5
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 8
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = True
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxLinhatbPerguntas0: TFHBox
                Left = 0
                Top = 0
                Width = 945
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hboxQuestionario: TFHBox
                  Left = 0
                  Top = 0
                  Width = 115
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblQuestionario: TFLabel
                    Left = 0
                    Top = 0
                    Width = 80
                    Height = 18
                    Align = alRight
                    Anchors = []
                    Caption = 'Questionario'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edCodQuestionario: TFCombo
                  Left = 115
                  Top = 0
                  Width = 524
                  Height = 21
                  Table = tbPerguntas
                  LookupTable = tbQuestionarioCadastro
                  FieldName = 'COD_QUESTIONARIO'
                  LookupKey = 'COD_QUESTIONARIO'
                  LookupDesc = 'COD_DESCRICAO_QUESTIONARIO'
                  Flex = True
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = True
                  Prompt = 'Selecione'
                  Constraint.Expression = 'value is null'
                  Constraint.Message = 'Campo Multipla, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.GroupName = 'tbPerguntaR'
                  Constraint.EnableUI = True
                  Constraint.Enabled = True
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Align = alLeft
                  Enabled = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object hboxLinhatbPerguntas1: TFHBox
                Left = 0
                Top = 31
                Width = 750
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hboxPergunta: TFHBox
                  Left = 0
                  Top = 0
                  Width = 115
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblPergunta: TFLabel
                    Left = 0
                    Top = 0
                    Width = 58
                    Height = 18
                    Align = alRight
                    Anchors = []
                    Caption = 'Pergunta'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edPergunta: TFMemo
                  Left = 115
                  Top = 0
                  Width = 525
                  Height = 60
                  CharCase = ccNormal
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Lines.Strings = (
                    'FMemo1')
                  Maxlength = 0
                  ParentFont = False
                  TabOrder = 1
                  FieldName = 'PERGUNTA'
                  Table = tbPerguntas
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Constraint.Expression = 'value is null or trim(value) = '#39#39
                  Constraint.Message = 'Campo Pergunta, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = True
                  Constraint.GroupName = 'tbPerguntaR'
                  Constraint.EnableUI = True
                  Constraint.Enabled = True
                  Constraint.FormCheck = True
                  Required = True
                end
              end
              object hboxLinhatbPerguntas8: TFHBox
                Left = 0
                Top = 62
                Width = 750
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hboxAviso: TFHBox
                  Left = 0
                  Top = 0
                  Width = 115
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblAviso: TFLabel
                    Left = 0
                    Top = 0
                    Width = 34
                    Height = 18
                    Align = alRight
                    Anchors = []
                    Caption = 'Aviso'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    WKey = '7000192;70002;43003'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edAviso: TFMemo
                  Left = 115
                  Top = 0
                  Width = 523
                  Height = 60
                  Hint = 'Aviso/Alerta a ser exibido  com a pergunta'
                  CharCase = ccNormal
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Lines.Strings = (
                    'FMemo1')
                  Maxlength = 0
                  ParentFont = False
                  TabOrder = 1
                  FieldName = 'AVISO'
                  Table = tbPerguntas
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  HelpCaption = 'Aviso/Alerta a ser exibido  com a pergunta'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Constraint.Expression = 'value is null or trim(value) = '#39#39
                  Constraint.Message = 'Campo Pergunta, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.GroupName = 'tbPerguntaR'
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = False
                  Required = True
                end
              end
              object hboxLinhatbPerguntas9: TFHBox
                Left = 0
                Top = 93
                Width = 750
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 3
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hboxResumoPergunta: TFHBox
                  Left = 0
                  Top = 0
                  Width = 115
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblResumoPergunta: TFLabel
                    Left = 0
                    Top = 0
                    Width = 53
                    Height = 18
                    Hint = 'Resumo pergunta'
                    Align = alRight
                    Anchors = []
                    Caption = 'Resumo'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    WKey = '7000192;70002;43003'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edResumoPergunta: TFString
                  Left = 115
                  Top = 0
                  Width = 523
                  Height = 24
                  Table = tbPerguntas
                  FieldName = 'RESUMO_PERGUNTA'
                  TabOrder = 5
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Resumo Pergunta'
                  Constraint.Expression = 'value is null or trim(value) = '#39#39
                  Constraint.Message = 'Campo Resumo pergunta, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.GroupName = 'tbPergunta'
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = False
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 100
                  Align = alLeft
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
              end
              object hboxLinhatbPerguntas2: TFHBox
                Left = 0
                Top = 124
                Width = 945
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 4
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hboxVigenciaInicial: TFHBox
                  Left = 0
                  Top = 0
                  Width = 115
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblVigenciaInicial: TFLabel
                    Left = 0
                    Top = 0
                    Width = 88
                    Height = 18
                    Align = alRight
                    Anchors = []
                    Caption = 'Vigencia inicial'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edVigenciaInicial: TFDate
                  Left = 115
                  Top = 0
                  Width = 150
                  Height = 24
                  Table = tbPerguntas
                  FieldName = 'VIGENCIA_INICIAL'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Data'
                  Constraint.Expression = 'value is null'
                  Constraint.Message = 'Campo Vigencia inicial, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.GroupName = 'tbPergunta'
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = False
                  IconDirection = idLeft
                  Format = 'dd/MM/yyyy'
                  ShowCheckBox = False
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ShowTime = False
                  ShowOnFocus = False
                end
                object hboxCodigo: TFHBox
                  Left = 265
                  Top = 0
                  Width = 61
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblCodigo: TFLabel
                    Left = 0
                    Top = 0
                    Width = 43
                    Height = 18
                    Align = alRight
                    Anchors = []
                    Caption = 'Codigo'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    WKey = '7000192;70002;43003'
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edCodigo: TFString
                  Left = 326
                  Top = 0
                  Width = 224
                  Height = 24
                  Table = tbPerguntas
                  FieldName = 'CODIGO'
                  TabOrder = 5
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'C'#243'digo'
                  Constraint.Expression = 'value is null or trim(value) = '#39#39
                  Constraint.Message = 'Campo Codigo, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.GroupName = 'tbPergunta'
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = False
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 100
                  Align = alLeft
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
              end
              object hboxLinhatbPerguntas3: TFHBox
                Left = 0
                Top = 155
                Width = 945
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 5
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hboxVigenciaFinal: TFHBox
                  Left = 0
                  Top = 0
                  Width = 115
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblVigenciaFinal: TFLabel
                    Left = 0
                    Top = 0
                    Width = 82
                    Height = 18
                    Align = alRight
                    Anchors = []
                    Caption = 'Vigencia final'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edVigenciaFinal: TFDate
                  Left = 115
                  Top = 0
                  Width = 150
                  Height = 24
                  Table = tbPerguntas
                  FieldName = 'VIGENCIA_FINAL'
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'Data'
                  Constraint.Expression = 'value is null'
                  Constraint.Message = 'Campo Vigencia final, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.GroupName = 'tbPergunta'
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  Format = 'dd/MM/yyyy'
                  ShowCheckBox = False
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ShowTime = False
                  ShowOnFocus = False
                end
                object hboxMultipla: TFHBox
                  Left = 265
                  Top = 0
                  Width = 61
                  Height = 27
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 10
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblMultipla: TFLabel
                    Left = 0
                    Top = 0
                    Width = 28
                    Height = 18
                    Align = alRight
                    Anchors = []
                    Caption = 'Tipo'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object edMultipla: TFCombo
                  Left = 326
                  Top = 0
                  Width = 200
                  Height = 21
                  Table = tbPerguntas
                  LookupTable = tbTipoPerguntas
                  FieldName = 'MULTIPLA'
                  LookupKey = 'COD_TIPO_PERGUNTA'
                  LookupDesc = 'DESCRICAO_CODIGO_NOME'
                  Flex = True
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = True
                  Prompt = 'Selecione'
                  Constraint.Expression = 'value is null'
                  Constraint.Message = 'Campo Multipla, preenchimento '#233' obrigat'#243'rio'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = True
                  Constraint.GroupName = 'tbPerguntaR'
                  Constraint.EnableUI = True
                  Constraint.Enabled = True
                  Constraint.FormCheck = True
                  ClearOnDelKey = True
                  UseClearButton = False
                  HideClearButtonOnNullValue = False
                  Align = alLeft
                  Colors = <>
                  Images = <>
                  Masks = <>
                  Fonts = <>
                  MultiSelection = False
                  IconReverseDirection = False
                end
              end
              object hboxLinhatbPerguntas4: TFHBox
                Left = 0
                Top = 186
                Width = 945
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 6
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hBoxAtiva: TFHBox
                  Left = 0
                  Top = 0
                  Width = 70
                  Height = 34
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 3
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edAtiva: TFCheckBox
                    Left = 0
                    Top = 0
                    Width = 50
                    Height = 21
                    Align = alRight
                    Caption = 'Ativa'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    Table = tbPerguntas
                    FieldName = 'ATIVA'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                  end
                end
                object hBoxObrigatorio: TFHBox
                  Left = 70
                  Top = 0
                  Width = 110
                  Height = 34
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 3
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edObrigatorio: TFCheckBox
                    Left = 0
                    Top = 0
                    Width = 102
                    Height = 21
                    Align = alRight
                    Caption = 'Obrigatorio'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    Table = tbPerguntas
                    FieldName = 'OBRIGATORIO'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                  end
                end
                object hBoxResultado: TFHBox
                  Left = 180
                  Top = 0
                  Width = 216
                  Height = 34
                  Hint = 'Vai ser a pergunta de resultado final da pesquisa'
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 3
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edResultado: TFCheckBox
                    Left = 0
                    Top = 0
                    Width = 214
                    Height = 21
                    Hint = 'Pergunta de resultado final'
                    Align = alRight
                    Caption = 'Pergunta Resultado'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    Table = tbPerguntas
                    FieldName = 'RESULTADO'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                  end
                end
                object hBoxExibeReclamacao: TFHBox
                  Left = 396
                  Top = 0
                  Width = 186
                  Height = 34
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 3
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 3
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edExibeReclamacao: TFCheckBox
                    Left = 0
                    Top = 0
                    Width = 166
                    Height = 21
                    Hint = 'Exibir reclama'#231#227'o da OS'
                    Align = alRight
                    Caption = 'Exibe reclama'#231#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    Table = tbPerguntas
                    FieldName = 'EXIBE_RECLAMACAO'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                  end
                end
              end
              object hboxLinhatbPerguntas5: TFHBox
                Left = 0
                Top = 217
                Width = 945
                Height = 30
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 7
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 4
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hBoxPerguntaDependente: TFHBox
                  Left = 0
                  Top = 0
                  Width = 184
                  Height = 34
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 3
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object edPerguntaDependente: TFCheckBox
                    Left = 0
                    Top = 0
                    Width = 180
                    Height = 21
                    Align = alRight
                    Caption = 'Pergunta dependente'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -15
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    Table = tbPerguntas
                    FieldName = 'PERGUNTA_DEPENDENTE'
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    OnCheck = edPerguntaDependenteCheck
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                  end
                end
              end
              object grupoPerguntaDepende: TFVBox
                Left = 0
                Top = 248
                Width = 750
                Height = 101
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stSingleLine
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 10
                Padding.Left = 10
                Padding.Right = 10
                Padding.Bottom = 10
                TabOrder = 8
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 8
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel1: TFLabel
                  Left = 0
                  Top = 0
                  Width = 384
                  Height = 13
                  Caption = 
                    'Indique o numero sequencial da pergunta ou resposta no questiona' +
                    'rio ex: 1,2,3'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clRed
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object hboxLinhatbPerguntas7: TFHBox
                  Left = 0
                  Top = 14
                  Width = 945
                  Height = 30
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 4
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hboxPerguntaDependenteResposta: TFHBox
                    Left = 0
                    Top = 0
                    Width = 195
                    Height = 27
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 10
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblPerguntaDependenteResposta: TFLabel
                      Left = 0
                      Top = 0
                      Width = 183
                      Height = 18
                      Align = alRight
                      Anchors = []
                      Caption = 'Depende de Qual Pergunta?'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -15
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      WKey = '7000192;70002;43003'
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                  end
                  object edPerguntaDependenteResposta: TFString
                    Left = 195
                    Top = 0
                    Width = 112
                    Height = 24
                    Table = tbPerguntas
                    FieldName = 'PERGUNTA_DEPENDENTE_RESPOSTA'
                    TabOrder = 5
                    AccessLevel = 0
                    Flex = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Prompt = '1,2,3'
                    Constraint.Expression = 'value is null or trim(value) = '#39#39
                    Constraint.Message = 'Campo Pergunta dependente resposta, preenchimento '#233' obrigat'#243'rio'
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.GroupName = 'tbPergunta'
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = False
                    IconDirection = idLeft
                    CharCase = ccNormal
                    Pwd = False
                    Maxlength = 100
                    Align = alLeft
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    SaveLiteralCharacter = False
                    TextAlign = taLeft
                  end
                end
                object hboxLinhatbPerguntas6: TFHBox
                  Left = 0
                  Top = 45
                  Width = 945
                  Height = 30
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 4
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hboxPerguntaDependentePergunta: TFHBox
                    Left = 0
                    Top = 0
                    Width = 195
                    Height = 27
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 10
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblPerguntaDependentePergunta: TFLabel
                      Left = 0
                      Top = 0
                      Width = 185
                      Height = 18
                      Align = alRight
                      Anchors = []
                      Caption = 'Depende de Qual Resposta?'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -15
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      WKey = '7000192;70002;43003'
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                  end
                  object edPerguntaDependentePergunta: TFString
                    Left = 195
                    Top = 0
                    Width = 112
                    Height = 24
                    Table = tbPerguntas
                    FieldName = 'PERGUNTA_DEPENDENTE_PERGUNTA'
                    TabOrder = 5
                    AccessLevel = 0
                    Flex = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Constraint.Expression = 'value is null or trim(value) = '#39#39
                    Constraint.Message = 'Campo Pergunta dependente pergunta, preenchimento '#233' obrigat'#243'rio'
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.GroupName = 'tbPergunta'
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = False
                    IconDirection = idLeft
                    CharCase = ccNormal
                    Pwd = False
                    Maxlength = 100
                    Align = alLeft
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    SaveLiteralCharacter = False
                    TextAlign = taLeft
                  end
                end
                object grupoPerguntaDependeEspaco1: TFVBox
                  Left = 0
                  Top = 76
                  Width = 185
                  Height = 9
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
        end
      end
      object tabCruzamentoOpcao: TFTabsheet
        Caption = 'Op'#231#245'es'
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxPrincipalCruzamentoOpcoes: TFVBox
          Left = 0
          Top = 0
          Width = 740
          Height = 433
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 8
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object edCruzamentoOpcao: TFDualList
            Left = 0
            Top = 0
            Width = 629
            Height = 285
            Caption = 'Selecionados'
            LookupCaption = 'Dispon'#237'veis'
            CaptionHiperLink = False
            LookupCaptionHiperLink = False
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            LookupColumns = <
              item
                Expanded = False
                FieldName = 'COD_OPCOES'
                Font = <>
                Title.Caption = 'C'#243'd.'
                Width = 97
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{C42A3E89-E7CD-4984-9B3E-E82B5EE425E6}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'INITCAP_OPCAO'
                Font = <>
                Title.Caption = 'Op'#231#227'o'
                Width = 140
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{FC4545D6-EADA-4D4B-9A82-68C4A344DB88}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
            Columns = <
              item
                Expanded = False
                FieldName = 'COD_OPCOES'
                Font = <>
                Title.Caption = 'C'#243'd.'
                Width = 106
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{95972506-612E-4B92-8942-05B74B5DB9B2}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'INITCAP_OPCAO'
                Font = <>
                Title.Caption = 'Op'#231#227'o'
                Width = 42
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = True
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{468148D3-49F8-4112-946B-6B72F2B26DFB}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end
              item
                Expanded = False
                FieldName = 'SEQ'
                Font = <>
                Title.Caption = 'Seq.'
                Width = 57
                Visible = True
                Precision = 0
                TextAlign = taLeft
                FieldType = ftString
                FlexRatio = 0
                Sort = False
                ImageHeader = 0
                Wrap = False
                Flex = False
                Colors = <>
                Images = <>
                Masks = <>
                CharCase = ccNormal
                BlobConfig.MimeType = bmtText
                BlobConfig.ShowType = btImageViewer
                ShowLabel = True
                Editor.EditType = etTFString
                Editor.Precision = 0
                Editor.Step = 0
                Editor.MaxLength = 100
                Editor.LookupFilterKey = 0
                Editor.LookupFilterDesc = 0
                Editor.PopupHeight = 400
                Editor.PopupWidth = 400
                Editor.CharCase = ccNormal
                Editor.LookupColumns = <>
                Editor.Enabled = False
                Editor.ReadOnly = False
                Editor.Filter = False
                Editor.ShowClearButton = False
                CheckedValue = 'S'
                UncheckedValue = 'N'
                HiperLink = False
                GUID = '{23DE02A7-3196-4BE6-946B-97F006333F53}'
                WOwner = FrInterno
                WOrigem = EhNone
                EditorConstraint.CheckWhen = cwImmediate
                EditorConstraint.CheckType = ctExpression
                EditorConstraint.FocusOnError = False
                EditorConstraint.EnableUI = True
                EditorConstraint.Enabled = False
                EditorConstraint.FormCheck = True
                Empty = False
                MobileOpts.ShowMobile = False
                MobileOpts.Order = 0
                BoxSize = 0
                ImageSrcType = istSource
                IconReverseDirection = False
                FooterConfig.ColSpan = 0
                FooterConfig.TextAlign = taLeft
                FooterConfig.Enabled = False
                HeaderTextAlign = taLeft
                Priority = 0
              end>
            Table = tbRespostasOpcoes
            LookupTable = tbOpcoes
            MultiSelection = False
            Paging = True
            WOwner = FrInterno
            WOrigem = EhNone
            Align = alClient
            RelationshipCols = <>
            Grouping.Enabled = False
            Grouping.Expanded = False
            Grouping.ShowFooter = False
            GroupingLookup.Enabled = False
            GroupingLookup.Expanded = False
            GroupingLookup.ShowFooter = False
          end
          object hboxBtnCruzamentoOpcao: TFHBox
            Left = 0
            Top = 286
            Width = 639
            Height = 50
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 4
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox2: TFVBox
              Left = 0
              Top = 0
              Width = 325
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FLabel2: TFLabel
                Left = 0
                Top = 0
                Width = 317
                Height = 13
                Caption = 'C'#243'd. Tipo Evento (Caso preenchido gera evento RAC Autom'#225'tico)'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object cbCodTipoEvento: TFCombo
                Left = 0
                Top = 14
                Width = 290
                Height = 21
                Table = tbRespostasOpcoes
                LookupTable = tbTipoEventoRac
                FieldName = 'COD_TIPO_EVENTO'
                LookupKey = 'COD_TIPO_EVENTO'
                LookupDesc = 'DESCRICAO'
                Flex = True
                ReadOnly = True
                WOwner = FrInterno
                WOrigem = EhNone
                Required = False
                Prompt = 'Selecione'
                Constraint.CheckWhen = cwImmediate
                Constraint.CheckType = ctExpression
                Constraint.FocusOnError = False
                Constraint.EnableUI = True
                Constraint.Enabled = False
                Constraint.FormCheck = True
                ClearOnDelKey = True
                UseClearButton = True
                HideClearButtonOnNullValue = False
                Colors = <>
                Images = <>
                Masks = <>
                Fonts = <>
                MultiSelection = False
                IconReverseDirection = False
              end
            end
            object FHBox4: TFHBox
              Left = 325
              Top = 0
              Width = 30
              Height = 34
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object FHBox3: TFHBox
              Left = 355
              Top = 0
              Width = 265
              Height = 41
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 5
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxEspacamentoControleSeqCruzamentoOpcao: TFHBox
                Left = 0
                Top = 0
                Width = 38
                Height = 34
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object btnMoverSequenciaParaCimaCruzamentoOpcao: TFButton
                Left = 38
                Top = 0
                Width = 48
                Height = 35
                Hint = 'Subir Sequ'#234'ncia Op'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 1
                OnClick = btnMoverSequenciaParaCimaCruzamentoOpcaoClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
                  F4000001AA4944415478DAED96BF4BC34014C7EF25A8B5EAE0ACE6CEA493E0E4
                  24D4C9C1C5A59376128A547015FC393928545C1C9C8A2082A0C5FFA050100417
                  370982F647AEAD38084E454B6973BEB487285841FB430A7970DCF75DEE3D3EF7
                  CD1102E49F035C0017C005E858007D581F27AA7DA210307BFABD21D3344B6D03
                  300C6384942BD72887649758CAB282A8EC9603689A36D805CA15CAB1AF4F4434
                  C579B8A5008C318F2A441CCBFC75BAEDA1136BAD02500CCA2E700EC8DCB15B91
                  BA88C353F54190F574D68A341D40A7A38740C4B24C2F713CE198975D02449073
                  54DDD55C403895CD449B06E0A3745310D89115B74251A6A06C1FA05E70963CAF
                  7D036FDEC20C10702054C71D10104C6633B186010CC6E6F07467B593919C6A97
                  27EFF3F9474363C79F01CC67B3A0531A428823595AB205CC66B2997863009425
                  9D0937BEE02BF03F707E575DFF06A0B69FAE60DB7D599E48716BBA21001F63DB
                  78B1A684ADACA673E99B0FB03A0012620BEFC19200B191E6FCB42180BACEFC00
                  F09B70013A188052FCD0C0A2A32B407A2DCB2AB6D9016D82801A016227929CEF
                  FEB54FE7FE90B8002E800BD0AC7807B333A6218963F5E20000000049454E44AE
                  426082}
                ImageId = 4600315
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object btnMoverSequenciaParaBaixoCruzamentoOpcao: TFButton
                Left = 86
                Top = 0
                Width = 48
                Height = 35
                Hint = 'Descer Sequ'#234'ncia Op'#231#227'o'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 2
                OnClick = btnMoverSequenciaParaBaixoCruzamentoOpcaoClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
                  F4000001B64944415478DAED963B4B034110C7675613309DD848C4BBF3629732
                  160A96010B6DA228D681682F245622588888686363888D209646D39A522D2CFC
                  02B9DC83808518045F8924B74E9213147CE4128D446E61B8DD59E6BFBF99DB5D
                  16E18F1B3A000E8003D0B6008140C0757B7D1347067D65C488AAAA7A4B01E47E
                  7908997951154158C868DA564B01062569987338B764A28AAE6E3800FF134016
                  A455DA6461E018530C75AF1E00591427C9B7C4806F67747DB729009F285DD1A7
                  97AC0CC0A7155D3FFC0A4016E409443349DD0EB2A4A26BA1E62A208A61047CCD
                  A2C8B9399E358CF44700962F4D0E0FD923021FA50A5C360550ABC24094B25FAF
                  8DF83D430C728A7D0B60324831939FD2A0A7522DE418CA186AEA3BEDBA372165
                  B7460B2E5A4179028971C04415097093B2A5FF0E526D1E23195D4DD4A36BEB14
                  F8246987569BB386CF646EAB6F92B12A0C87E5ACA1ADD4AB69F7183282382088
                  994FD4E28AA6CDDB11B47D0FF8FD7E77E1EEE19822C7DE4D7038520C6D0AAAA7
                  E517012ACDEBF57ABA5CEE13EA8E58AEB362B914CCE5724F76B51ABE090541E8
                  7621DBA74BAAB3502ACDD2E2F94674DAF741E20038000EC04FB517FD4FB721A1
                  E6C2680000000049454E44AE426082}
                ImageId = 4600316
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
              object FHBox2: TFHBox
                Left = 134
                Top = 0
                Width = 33
                Height = 34
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 3
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
          end
          object hboxCruzamentoOpcaoEspaco1: TFVBox
            Left = 0
            Top = 337
            Width = 185
            Height = 9
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
      end
    end
  end
  object tbPerguntas: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{5192C965-FBAE-4273-9257-78AA7F2A5D34}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        GUID = '{AB5A9801-9C27-47BF-972F-851E340EEFBF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MULTIPLA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Multipla'
        GUID = '{ECA5AC8D-EBE8-4F96-963B-18526B114D66}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta'
        GUID = '{E003435D-4AB3-4497-81A8-103EDC717B92}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativa'
        GUID = '{EEE76F1F-E966-41A4-82CC-58F86493E92B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Obrigatorio'
        GUID = '{5766FDAF-B2D5-4801-8CCA-A579FAF70C36}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'
        GUID = '{1B2E5996-4E27-43AE-A5E3-D4A20D2BD7C4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESULTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resultado'
        GUID = '{EC7C97EC-C49E-44E4-A6BE-2F3DEFA0F41E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_DEPENDENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Dependente'
        GUID = '{863D2E47-8ABC-42D8-8AAD-A9FC0931C31B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_RESP_ESPEC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Resp Espec'
        GUID = '{3E65E478-BEE2-4327-B2A7-FA43E20C5EEB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_QTDE_RESP_ESPEC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Quantidade Resp Espec'
        GUID = '{4846A07A-DF81-4307-AB0C-8D1E52EFBD79}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_DEPENDENTE_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Dependente Pergunta'
        GUID = '{6E82AA47-C222-4737-A0AD-9AF4181B581F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_DEPENDENTE_RESPOSTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Dependente Resposta'
        GUID = '{093FC2AE-6A25-4CEE-9818-56A174B74580}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VIGENCIA_INICIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Vigencia Inicial'
        GUID = '{6314E6F7-51E1-42CE-B58D-66275314417B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VIGENCIA_FINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Vigencia Final'
        GUID = '{DCC834B2-AFAD-4258-AEB9-E1EAC7678E6E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        GUID = '{B1F0ADF6-4CDC-4E6E-8AAB-F9F5610500F5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AVISO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aviso'
        GUID = '{32E69628-0DC4-4591-8139-3D06E099A279}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESUMO_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resumo Pergunta'
        GUID = '{A4076DD0-4B0D-4031-B06D-542EE1C83B7D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MYHONDA_SEQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Myhonda Seq'
        GUID = '{B181867D-5B07-4C3D-82AE-74AD28358165}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXIBE_RECLAMACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Exibe Reclama'#231#227'o'
        GUID = '{93E8D068-98D7-493E-A855-7BA36DFA04B5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERGUNTA_RESUMIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pergunta Resumida'
        GUID = '{BC103EA6-DA95-45EB-B360-15F3787B0A47}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PERGUNTAS'
    Cursor = 'CRM_PERGUNTAS'
    MaxRowCount = 0
    OnAfterDelete = tbPerguntasAfterDelete
    OnAfterScroll = tbPerguntasAfterScroll
    OnAfterOpen = tbPerguntasAfterOpen
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTipoPerguntas: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Pergunta'
        GUID = '{84AB16E0-05BF-4066-8E23-12E15877C767}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Tipo'
        GUID = '{B8C2C08C-B322-48A8-8F25-1563BC5C866D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo Nome'
        GUID = '{84736D7E-3890-48D8-8107-79431D7F94FE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIPO_PERGUNTAS'
    Cursor = 'CRM_TIPO_PERGUNTAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbQuestionario: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{0C8DEB55-CEA6-4846-B1E4-2DF1FF56D829}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Questionario'
        GUID = '{2A4ACB75-E2FE-49C8-AC4D-F2C982351417}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_DESCRICAO_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Descri'#231#227'o Questionario'
        GUID = '{0D21C4AD-E5A5-4531-B66C-2F10BF591AEF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_QUESTIONARIO'
    Cursor = 'CRM_QUESTIONARIO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45203'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOpcoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_OPCOES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Opc'#245'es'
        GUID = '{4C4E2434-9D69-446C-9388-70D2DACA6CB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Op'#231#227'o'
        GUID = '{8013C0C6-C0AD-4765-BF75-5FE73F067BF9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INITCAP_OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Initcap Op'#231#227'o'
        GUID = '{5AACDE26-FE51-40B7-BA1C-CAD44A483A42}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_OPCOES'
    Cursor = 'CRM_OPCOES'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45205'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRespostasOpcoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{962A94DE-30B3-46DD-95F2-06899B713E72}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OPCOES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Opc'#245'es'
        GUID = '{B2A63A5F-177D-4B01-A213-548FDF418144}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PERGUNTA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pergunta'
        GUID = '{4A3C4BAC-954B-4E86-B954-F43CA4D3712A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MULTIPLA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Multipla'
        GUID = '{8BA239B0-19F9-4BC0-81FB-B50432498DC4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'
        GUID = '{*************-495B-B64E-EE257B554A9F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INITCAP_OPCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Initcap Op'#231#227'o'
        GUID = '{FAE9C57E-102D-42FC-A9FF-5264DA83DB37}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        GUID = '{CB74A965-17FF-4FA5-8358-C0B2D1236464}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'COD_QUESTIONARIO; COD_PERGUNTA; MULTIPLA'
    DetailFilters = 'COD_QUESTIONARIO; COD_PERGUNTA; MULTIPLA'
    TableName = 'CRM_RESPOSTAS_OPCOES'
    Cursor = 'CRM_RESPOSTAS_OPCOES'
    MaxRowCount = 0
    MasterTable = tbPerguntas
    OnAfterDelete = tbRespostasOpcoesAfterDelete
    OnAfterOpen = tbRespostasOpcoesAfterOpen
    OnBeforePost = tbRespostasOpcoesBeforePost
    OnBeforeOpen = tbRespostasOpcoesBeforeOpen
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45206'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbQuestionarioCadastro: TFTable
    FieldDefs = <
      item
        Name = 'COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Questionario'
        GUID = '{F9C9884B-962D-46CC-B3A6-4E2D9DCB6ADA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Questionario'
        GUID = '{0AD7FFC4-8A10-4C11-ABFC-D272148C0B5C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_DESCRICAO_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Descri'#231#227'o Questionario'
        GUID = '{5449A798-D6EF-46E9-BCC7-EA987ECE580E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_QUESTIONARIO'
    Cursor = 'CRM_QUESTIONARIO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;45207'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTipoEventoRac: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{3DA5F87F-22EF-48EE-AAAA-9FAC8291B1A6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        GUID = '{F9A6BDC6-C4D3-4782-AEEC-2CE331619C82}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRM_TIPO_EVENTO_RAC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45205;49401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
