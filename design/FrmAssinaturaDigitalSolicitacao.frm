object FrmAssinaturaDigitalSolicitacao: TFForm
  Left = 44
  Top = 118
  ActiveControl = boxPrinciapl
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Assinatura Digital Solicita'#231#227'o'
  ClientHeight = 502
  ClientWidth = 584
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '45506'
  ShortcutKeys = <>
  InterfaceRN = 'AssinaturaDigitalSolicitacaoRN'
  Access = False
  ChangedProp = 
    'FrmAssinaturaDigitalSolicitacao.Width;'#13#10'FrmAssinaturaDigitalSoli' +
    'citacao.Height;'#13#10'tbSolicitacoesAssinaturas.FieldDefs;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object boxPrinciapl: TFVBox
    Left = 0
    Top = 0
    Width = 584
    Height = 502
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hboxBotoes: TFHBox
      Left = 0
      Top = 0
      Width = 680
      Height = 57
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 5
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 50
        Height = 50
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
          306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'fas fa-reply  icon-color-black icon-24px'
        IconReverseDirection = False
      end
      object btnEnviar: TFButton
        Left = 50
        Top = 0
        Width = 50
        Height = 50
        Hint = 'Enviar assinatura'
        Align = alLeft
        Caption = 'Enviar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
          306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'fas fa-paper-plane icon-color-black icon-24px'
        IconReverseDirection = False
      end
      object btnReenviar: TFButton
        Left = 100
        Top = 0
        Width = 50
        Height = 50
        Hint = 'Reenviar Assinatura'
        Align = alLeft
        Caption = 'Reenviar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 2
        OnClick = btnReenviarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
          306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'fas fa-paper-plane icon-color-black icon-24px'
        IconReverseDirection = False
      end
      object btnCancelar: TFButton
        Left = 150
        Top = 0
        Width = 50
        Height = 50
        Hint = 'Cancelar Assinatura'
        Align = alLeft
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 3
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
          306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'fas fa-ban icon-color-black icon-24px'
        IconReverseDirection = False
      end
      object btnArquivar: TFButton
        Left = 200
        Top = 0
        Width = 50
        Height = 50
        Hint = 'Cancelar Assinatura'
        Align = alLeft
        Caption = 'Arquivar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 4
        OnClick = btnArquivarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
          306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconClass = 'fas fa-archive icon-color-black icon-24px'
        IconReverseDirection = False
      end
      object hboxControleRemotoPresencial: TFVBox
        Left = 250
        Top = 0
        Width = 170
        Height = 50
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 5
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        DesignSize = (
          166
          46)
        object hboxControleRemotoPresencialEspaco1: TFHBox
          Left = 0
          Top = 0
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object hboxControleRemotoPresencialInterno: TFHBox
          Left = 0
          Top = 9
          Width = 170
          Height = 27
          Anchors = []
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 0
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hboxSelcaoRemoto: TFHBox
            Left = 0
            Top = 0
            Width = 80
            Height = 23
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = clSilver
            Padding.Top = 2
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 0
            OnClick = hboxSelcaoRemotoClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hboxSelcaoAtivoEspaco1: TFVBox
              Left = 0
              Top = 0
              Width = 16
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblSelecaoRemoto: TFLabel
              Left = 16
              Top = 0
              Width = 43
              Height = 14
              Caption = 'Remoto'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object hboxSelcaoAtivoEspaco2: TFVBox
              Left = 59
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object hboxSelcaoPresencial: TFHBox
            Left = 80
            Top = 0
            Width = 80
            Height = 23
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 2
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 1
            OnClick = hboxSelcaoPresencialClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hboxSelcaoPresencialEspaco1: TFVBox
              Left = 0
              Top = 0
              Width = 12
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblSelecaoPresencial: TFLabel
              Left = 12
              Top = 0
              Width = 53
              Height = 14
              Caption = 'Presencial'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object hboxSelcaoPresencialEspaco2: TFVBox
              Left = 65
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
        end
        object hboxControleRemotoPresencialEspaco2: TFHBox
          Left = 0
          Top = 37
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object hboxStatusAssinaturaEspaco1: TFHBox
        Left = 420
        Top = 0
        Width = 13
        Height = 24
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 6
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object vBoxBtnAcaoLerQRCodePrincipal: TFVBox
        Left = 433
        Top = 0
        Width = 88
        Height = 55
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 7
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 0
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxBtnAcaoLerQRCodePrincipalEspaco1: TFHBox
          Left = 0
          Top = 0
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object FPanelBtnAcaoLerQRCode: TFPanelButton
          Left = 0
          Top = 9
          Width = 80
          Height = 45
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = FPanelBtnAcaoLerQRCodeClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          BoxShadowConfig.HorizontalLength = 0
          BoxShadowConfig.VerticalLength = 7
          BoxShadowConfig.BlurRadius = 10
          BoxShadowConfig.SpreadRadius = -5
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          Toggle = True
          ToggleColor = clBtnFace
          WOwner = FrInterno
          WOrigem = EhNone
          object FPanelBtnAcaoLerQRCodeItems: TFFastDesignCmpItems
            object FPanelBtnAcaoLerQRCodeVbox: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem1'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
              ItemType = itVBox
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaCenter
              Flex = True
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
              object FPanelBtnAcaoLerQRCodeLbl: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'Ler QRCode'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
                ItemType = itLabel
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clGray
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ItemAlign = iaCenter
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
              end
              object FPanelBtnAcaoLerQRCodeImg: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'FPanelButtonItem2'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
                ItemType = itImage
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ItemAlign = iaCenter
                ImageSrc = '/images/crmservice386027.png'
                Flex = False
                Width = 27
                Height = 27
                ItemVAlign = ivaTop
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
              end
            end
          end
        end
        object vBoxBtnAcaoLerQRCodePrincipalEspaco2: TFHBox
          Left = 0
          Top = 55
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Visible = False
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object hboxStatusAssinaturaEspaco2: TFHBox
        Left = 521
        Top = 0
        Width = 13
        Height = 24
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 8
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object vBoxBtnAcaoBaixarDocumentoPrincipal: TFVBox
        Left = 534
        Top = 0
        Width = 88
        Height = 55
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 9
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 4
        Flex.Vflex = ftTrue
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxBtnAcaoBaixarDocumentoPrincipalEspaco1: TFHBox
          Left = 0
          Top = 0
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object FPanelBtnAcaoBaixarDocumento: TFPanelButton
          Left = 0
          Top = 9
          Width = 73
          Height = 45
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          OnClick = FPanelBtnAcaoBaixarDocumentoClick
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          BoxShadowConfig.HorizontalLength = 0
          BoxShadowConfig.VerticalLength = 7
          BoxShadowConfig.BlurRadius = 10
          BoxShadowConfig.SpreadRadius = -5
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          Toggle = True
          ToggleColor = clBtnFace
          WOwner = FrInterno
          WOrigem = EhNone
          object FPanelBtnAcaoBaixarDocumentoItems: TFFastDesignCmpItems
            object FPanelBtnAcaoBaixarDocumentoVbox: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem1'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
              ItemType = itVBox
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaLeft
              Flex = True
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
              object FPanelBtnAcaoBaixarDocumentoLbl: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'Abrir Doc.'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
                ItemType = itLabel
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clGray
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ItemAlign = iaCenter
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
              end
              object FPanelBtnAcaoBaixarDocumentoImg: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'FPanelButtonItem2'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
                ItemType = itImage
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ItemAlign = iaCenter
                ImageSrc = '/images/crmservice4600365.png'
                Flex = False
                Width = 27
                Height = 27
                ItemVAlign = ivaTop
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
              end
            end
          end
        end
        object vBoxBtnAcaoBaixarDocumentoPrincipalEspaco2: TFHBox
          Left = 0
          Top = 55
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object FHBox3: TFHBox
        Left = 622
        Top = 0
        Width = 13
        Height = 24
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 10
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object vBoxlStatusAssinaturaPrincipal: TFVBox
        Left = 635
        Top = 0
        Width = 86
        Height = 55
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 11
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 4
        Flex.Vflex = ftTrue
        Flex.Hflex = ftMin
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxlStatusAssinaturaPrincipal1: TFHBox
          Left = 0
          Top = 0
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object FPanelStatusAssinatura: TFPanelButton
          Left = 0
          Top = 9
          Width = 164
          Height = 45
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          BoxShadowConfig.HorizontalLength = 0
          BoxShadowConfig.VerticalLength = 7
          BoxShadowConfig.BlurRadius = 10
          BoxShadowConfig.SpreadRadius = -5
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          Toggle = True
          ToggleColor = clBtnFace
          WOwner = FrInterno
          WOrigem = EhNone
          object FPanelStatusAssinaturaItems: TFFastDesignCmpItems
            object FPanelStatusAssinaturaVbox: TFPanelButtonItem
              AutoHotkeys = maManual
              Caption = 'FPanelButtonItem1'
              WOwner = FrInterno
              WOrigem = EhNone
              GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
              ItemType = itVBox
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -13
              Font.Name = 'Tahoma'
              Font.Style = []
              ItemAlign = iaLeft
              Flex = False
              ItemVAlign = ivaTop
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              BorderStyle = stNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              BevelInner = bvNone
              BevelKind = bkTile
              BevelOuter = bvNone
              Color = clBtnFace
              WordBreak = False
              object FPanelStatusAssinaturaLbl1: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'Status da Assinatura'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
                ItemType = itLabel
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clBlack
                Font.Height = -15
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ItemAlign = iaLeft
                Flex = False
                ItemVAlign = ivaBottom
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
              end
              object FPanelStatusAssinaturaLbl2: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'Status'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{696D3B0E-F8A0-468D-AC12-CA13DAFFBA8C}'
                ItemType = itLabel
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ItemAlign = iaLeft
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
              end
            end
          end
        end
        object vBoxlStatusAssinaturaPrincipal2: TFHBox
          Left = 0
          Top = 55
          Width = 76
          Height = 8
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
    end
    object VboxTemplateSolicitacaoAssinaturaPrincipal: TFVBox
      Left = 0
      Top = 58
      Width = 577
      Height = 106
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Visible = False
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 8
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object hboxTemplateSolicitacaoAssinatura: TFHBox
        Left = 0
        Top = 0
        Width = 576
        Height = 101
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 8
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vboxTemplateSolicitacaoAssinaturaCol1: TFVBox
          Left = 0
          Top = 0
          Width = 452
          Height = 87
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hboxTemplateSolicitacaoAssinaturaLinha1: TFHBox
            Left = 0
            Top = 0
            Width = 422
            Height = 30
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 3
            Spacing = 10
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hboxTemplateSolicitacaoAssinaturaHboxLblTipo: TFHBox
              Left = 0
              Top = 0
              Width = 35
              Height = 26
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftMin
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxTemplateSolicitacaoAssinaturaLblTipo: TFLabel
                Left = 0
                Top = 0
                Width = 42
                Height = 23
                Hint = 'Signatario'
                Align = alRight
                Anchors = []
                Caption = 'Tipo'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clBlack
                Font.Height = -19
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                FieldName = 'DESCRICAO'
                Table = tbSolicitacoesAssinaturas
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000192;70002;43003'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object hboxTemplateSolicitacaoAssinaturaHboxLblEditar: TFHBox
              Left = 35
              Top = 0
              Width = 60
              Height = 26
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 4
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxTemplateSolicitacaoAssinaturaIconEditar: TFIconClass
                Left = 0
                Top = 0
                Width = 16
                Height = 16
                Picture.Data = {
                  07544269746D6170DA020000424DDA0200000000000036000000280000000D00
                  00000D0000000100200000000000A40200000000000000000000000000000000
                  0000F0F0F000F0F0F000FF000000FF000000FF000000FF000000FF000000FF00
                  0000FF000000FF000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000FF00
                  0000FF000000FF000000FF000000FF000000FF000000FF000000FF000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000FF000000FF000000F0F0F000F0F0
                  F000F0F0F000F0F0F000FF000000FF000000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000FF000000FF000000F0F0F000F0F0F000F0F0F000F0F0F000FF00
                  0000FF000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000FF000000FF00
                  0000F0F0F000F0F0F000F0F0F000F0F0F000FF000000FF000000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000FF000000FF000000F0F0F000F0F0F000F0F0
                  F000F0F0F000FF000000FF000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000FF000000FF000000F0F0F000F0F0F000F0F0F000F0F0F000FF000000FF00
                  0000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000FF000000FF000000F0F0
                  F000F0F0F000F0F0F000F0F0F000FF000000FF000000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000FF000000FF000000FF000000FF000000FF000000FF00
                  0000FF000000FF000000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000FF00
                  0000FF000000FF000000FF000000FF000000FF000000FF000000FF000000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0F000F0F0
                  F000F0F0F000}
                IconClass = 'pencil'
                WOwner = FrInterno
                WOrigem = EhNone
                Size = 13
                Color = clBlue
              end
              object hboxTemplateSolicitacaoAssinaturaLblEditar: TFLabel
                Left = 16
                Top = 0
                Width = 41
                Height = 18
                Hint = 'mudar dados signatario'
                Align = alRight
                Anchors = []
                Caption = 'Mudar'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -15
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                OnClick = hboxTemplateSolicitacaoAssinaturaLblEditarClick
                HiperLink = True
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000192;70002;43003'
                VerticalAlignment = taAlignBottom
                WordBreak = False
                MaskType = mtText
              end
            end
            object hboxTemplateSolicitacaoAssinaturaHboxLblStatus: TFHBox
              Left = 95
              Top = 0
              Width = 135
              Height = 26
              Align = alLeft
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 4
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxTemplateSolicitacaoAssinaturaLblStatus: TFLabel
                Left = 0
                Top = 0
                Width = 41
                Height = 18
                Hint = 'Status'
                Align = alRight
                Anchors = []
                Caption = 'Status'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -15
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000192;70002;43003'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
          end
          object hboxTemplateSolicitacaoAssinaturaLinha2: TFHBox
            Left = 0
            Top = 31
            Width = 422
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 3
            Spacing = 8
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hboxTemplateSolicitacaoAssinaturaHboxLblNome: TFHBox
              Left = 0
              Top = 0
              Width = 252
              Height = 22
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxTemplateSolicitacaoAssinaturaLblNome: TFLabel
                Left = 0
                Top = 0
                Width = 39
                Height = 18
                Hint = 'Nome'
                Align = alRight
                Anchors = []
                Caption = 'Nome'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -15
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                FieldName = 'NOME'
                Table = tbSolicitacoesAssinaturas
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000192;70002;43003'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
          end
          object hboxTemplateSolicitacaoAssinaturaLinha3: TFHBox
            Left = 0
            Top = 57
            Width = 422
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 3
            Spacing = 8
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hboxTemplateSolicitacaoAssinaturaHboxLblEmail: TFHBox
              Left = 0
              Top = 0
              Width = 193
              Height = 22
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxTemplateSolicitacaoAssinaturaLblEmail: TFLabel
                Left = 0
                Top = 0
                Width = 33
                Height = 18
                Hint = 'Email'
                Align = alRight
                Anchors = []
                Caption = 'Email'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -15
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                FieldName = 'EMAIL'
                Table = tbSolicitacoesAssinaturas
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000192;70002;43003'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object hboxTemplateSolicitacaoAssinaturaHboxLblTelefone: TFHBox
              Left = 193
              Top = 0
              Width = 97
              Height = 22
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hboxTemplateSolicitacaoAssinaturaLblTelefone: TFLabel
                Left = 0
                Top = 0
                Width = 57
                Height = 18
                Hint = 'Telefone'
                Align = alRight
                Anchors = []
                Caption = 'Telefone'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -15
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                FieldName = 'TELEFONE'
                Table = tbSolicitacoesAssinaturas
                WOwner = FrInterno
                WOrigem = EhNone
                WKey = '7000192;70002;43003'
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
          end
        end
        object vBoxBtnAcaoLerQRCodeSignatarioPrincipal: TFVBox
          Left = 452
          Top = 0
          Width = 92
          Height = 81
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 4
          Flex.Vflex = ftTrue
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco2: TFHBox
            Left = 0
            Top = 0
            Width = 76
            Height = 8
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FPanelBtnQrCode: TFPanelButton
            Left = 0
            Top = 9
            Width = 83
            Height = 57
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            OnClick = FPanelBtnQrCodeClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BoxShadowConfig.HorizontalLength = 0
            BoxShadowConfig.VerticalLength = 7
            BoxShadowConfig.BlurRadius = 10
            BoxShadowConfig.SpreadRadius = -5
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Toggle = True
            ToggleColor = clBtnFace
            WOwner = FrInterno
            WOrigem = EhNone
            object FPanelBtnQrCodeItems: TFFastDesignCmpItems
              object FPanelBtnQrCodeVbox: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'FPanelButtonItem1'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
                ItemType = itVBox
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ItemAlign = iaLeft
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
                object FPanelBtnQrCodeLbl: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = 'Ler QRCode'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clGray
                  Font.Height = -12
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
                object FPanelBtnQrCodeImg: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = 'FPanelButtonItem2'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{082B6F01-28F2-4D2D-BD84-62F6AB6C5D69}'
                  ItemType = itImage
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ItemAlign = iaCenter
                  ImageSrc = '/images/crmservice386027.png'
                  Flex = False
                  Width = 30
                  Height = 30
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
              end
            end
          end
          object vBoxBtnAcaoLerQRCodeSignatarioPrincipalEspaco1: TFHBox
            Left = 0
            Top = 67
            Width = 76
            Height = 8
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
      end
    end
    object hboxIrParaVersaoAtual: TFHBox
      Left = 0
      Top = 165
      Width = 576
      Height = 22
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 4
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblIrParaVersaoAtualVisualizacao: TFLabel
        Left = 0
        Top = 0
        Width = 158
        Height = 19
        Align = alRight
        Anchors = []
        Caption = 'Apenas Vizualiza'#231#227'o - '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        WKey = '7000192;70002;43003'
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object lblIrParaVersaoAtual: TFLabel
        Left = 158
        Top = 0
        Width = 143
        Height = 19
        Align = alRight
        Anchors = []
        Caption = 'Ir para Vers'#227'o Atual'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        OnClick = lblIrParaVersaoAtualClick
        HiperLink = True
        WOwner = FrInterno
        WOrigem = EhNone
        WKey = '7000192;70002;43003'
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
    end
    object vBoxGridSolicitacaoAssinatura: TFVBox
      Left = 0
      Top = 188
      Width = 576
      Height = 308
      Align = alClient
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FGrid1: TFGrid
        Left = 0
        Top = 0
        Width = 571
        Height = 184
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbSolicitacoesAssinaturas
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Paging.Enabled = False
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = False
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        CustomActionButtons = <>
        ActionColumn.Title = 'A'#231#245'es'
        ActionColumn.Width = 100
        ActionColumn.TextAlign = taCenter
        ActionColumn.Visible = True
        Columns = <
          item
            Expanded = False
            FieldName = 'VER'
            Font = <>
            Width = 40
            Visible = False
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = False
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            Font = <>
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = True
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = False
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            Template = VboxTemplateSolicitacaoAssinaturaPrincipal
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{1C34C1FC-1550-4580-8EA8-6A06EEA015E4}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
            OnRenderTemplate = FGrid1Columns1RenderTemplate
          end>
      end
      object pgCtrlDados: TFPageControl
        Left = 0
        Top = 185
        Width = 568
        Height = 118
        ActivePage = tabDadosAssinatura
        Align = alClient
        TabOrder = 1
        TabPosition = tpTop
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        RenderStyle = rsCard
        object tabDadosAssinatura: TFTabsheet
          Caption = 'Dados Assinatura'
          Visible = True
          Closable = False
          WOwner = FrInterno
          WOrigem = EhNone
          object hboxTabDadosAssinaturaMain: TFVBox
            Left = 0
            Top = 0
            Width = 560
            Height = 90
            Align = alClient
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stSingleLine
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 5
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FHBox1: TFHBox
              Left = 0
              Top = 0
              Width = 533
              Height = 65
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 5
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object vboxObservador: TFVBox
                Left = 0
                Top = 0
                Width = 403
                Height = 60
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 3
                Padding.Left = 3
                Padding.Right = 3
                Padding.Bottom = 3
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object lblObservador: TFLabel
                  Left = 0
                  Top = 0
                  Width = 107
                  Height = 13
                  Caption = 'Observador (opcional)'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object edObservador: TFString
                  Left = 0
                  Top = 14
                  Width = 330
                  Height = 24
                  TabOrder = 0
                  AccessLevel = 0
                  Flex = True
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Required = False
                  Prompt = 'ex: <EMAIL>, <EMAIL>'
                  Constraint.CheckWhen = cwImmediate
                  Constraint.CheckType = ctExpression
                  Constraint.FocusOnError = False
                  Constraint.EnableUI = True
                  Constraint.Enabled = False
                  Constraint.FormCheck = True
                  IconDirection = idLeft
                  CharCase = ccNormal
                  Pwd = False
                  Maxlength = 0
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -13
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  SaveLiteralCharacter = False
                  TextAlign = taLeft
                end
              end
              object vboxBtnHistorico: TFVBox
                Left = 403
                Top = 0
                Width = 56
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object vboxBtnHistoricoEspaco1: TFHBox
                  Left = 0
                  Top = 0
                  Width = 49
                  Height = 6
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnHistorico: TFButton
                  Left = 0
                  Top = 7
                  Width = 50
                  Height = 50
                  Align = alLeft
                  Caption = 'Hist'#243'rico'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Layout = blGlyphTop
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnHistoricoClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000244944415478DA63FC0F040C34048CA3168C5A306AC1A805A3168C5A
                    306AC1A80543C302008F805FB9E6FF08BA0000000049454E44AE426082}
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconClass = 'fas fa-history icon-color-black icon-24px'
                  IconReverseDirection = False
                end
              end
              object FVBox1: TFVBox
                Left = 459
                Top = 0
                Width = 56
                Height = 62
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Visible = False
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox2: TFHBox
                  Left = 0
                  Top = 0
                  Width = 49
                  Height = 5
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object btnTesteLoading: TFButton
                  Left = 0
                  Top = 6
                  Width = 50
                  Height = 50
                  Align = alLeft
                  Caption = 'Teste Loading'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  Layout = blGlyphTop
                  ParentFont = False
                  TabOrder = 1
                  OnClick = btnTesteLoadingClick
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                    F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                    E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                    B9B9B6418D210000000049454E44AE426082}
                  ImageId = 0
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clBtnFace
                  Access = False
                  IconClass = 'history'
                  IconReverseDirection = True
                end
              end
            end
            object vBoxGridSolicitacaoAssinaturaEspacoRodape: TFHBox
              Left = 0
              Top = 66
              Width = 185
              Height = 9
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
        end
      end
    end
  end
  object menuVerDocumentos: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 514
    Top = 250
  end
  object timerAtualizarStatusAssinatura: TFTimer
    Enabled = False
    Interval = 5000
    OnTimer = timerAtualizarStatusAssinaturaTimer
    Repeats = True
    WOwner = FrNone
    WOrigem = EhNone
    Left = 518
    Top = 312
  end
  object FPopupMenu1: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 516
    Top = 194
    object imgAnexo: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgAnexo'
      ImageIndex = 4600365
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{A5EAB975-AE2A-437C-829E-778A481E800F}'
    end
    object imgQrCode: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'imgQrCode'
      ImageIndex = 4600365
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{B4C10F76-337C-49E2-991C-7B39916624B2}'
    end
  end
  object tbSolicitacoesAssinaturas: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{89D76651-BB6B-4000-843C-356FBFC8D75C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SIGNATARIO_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Signatario Tipo'
        GUID = '{E11F36DD-E074-4DF8-895B-222AFA7E1284}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SIGNATARIO_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Signatario C'#243'd.'
        GUID = '{D3BE5DA9-5C24-4499-90B0-2F7399FADE79}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{03128C2F-F467-412D-B44F-D36E3F624A1D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone'
        GUID = '{98F4819E-4F6A-4D78-BE55-F656EEAD3D2F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{4384EC3B-70E2-4994-ACA9-E3D331AE48AD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAG_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tag Documento'
        GUID = '{9B7DEE27-B6B1-4FCE-A98D-117CCFEEAC0E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{1D8DF52A-885E-4A8A-AB80-48E19DC75DD5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        GUID = '{A69D1BD5-DE90-4E87-BD25-72D0C1385373}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QR_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qr Code'
        GUID = '{0E648039-1F9D-4F28-A47D-F69B90373AC2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Assinatura'
        GUID = '{D131609B-3951-4686-B5A9-EEC6DA589331}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_ANDAMENTO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Andamento Assinatura'
        GUID = '{BF974792-58C5-4506-BD21-E63B2B394878}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{9AB5FD9E-3C0B-4398-8279-E55E9AE8C7BF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{992BCEDF-5725-4A20-AAF8-7DCD18ECCD61}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_DESTINATARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Destinatario'
        GUID = '{B576CC34-139E-4B26-9FE9-32245974DC7C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EDITAR'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'EDITAR'
        GUID = '{B0982939-4A0E-4F08-BD86-B77D31E980D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VER'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'VER'
        GUID = '{FA85F62B-1074-4E39-A7EB-DCCD498AC572}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINAR'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'ASSINAR'
        GUID = '{E7CD2F10-3A4A-429E-8F26-AA5E1BE87D07}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SOLICITACOES_ASSINATURAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45501'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiEnvelopeFilaResumo: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{1BFB1027-2D31-4730-8FFA-AD1CB29E1EB3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Codigo'
        GUID = '{72EFC27B-D6C9-4F99-BA82-13F328FACBE0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{B16C3B4B-4D8C-4CB4-AB18-1C86AFA4B89D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSUNTO_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assunto Envelope'
        GUID = '{E2AD4E7B-7B8A-48AD-8400-9B4D2BDDD69C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Nbs'
        GUID = '{D8ED1557-0FDE-4F2C-A205-80786848D5F8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Status'
        GUID = '{C32714AA-1DEC-4232-8457-EB3C25B8712A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATUALIZAR_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Atualizar Status'
        GUID = '{84600017-69ED-4597-950A-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BUSCAR_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Buscar Documento'
        GUID = '{B35F9E72-A085-4847-9F57-3022A7F793B2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PACOTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pacote'
        GUID = '{88873DF9-5DF8-48A6-8488-8C2498CC0587}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRESENCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Presencial'
        GUID = '{103F6A89-D182-4C57-A514-E6C142345B8E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_EXTERNAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status External'
        GUID = '{0B84C4C6-282F-474A-8FEA-FC862A3D399D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Documento'
        GUID = '{ECB5820A-264A-4AE1-882C-5009C268E213}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOC_BASE64'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Base64'
        GUID = '{3E1C1030-9137-42AA-B8D3-23CADA43E545}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINK_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Link Assinatura'
        GUID = '{95221468-1E5A-40A6-87AC-0312026AB5C6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QR_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Qr Code'
        GUID = '{557027F2-256E-4C97-ABAD-F1CF87152E26}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_OUVINTE_ASS_DIG'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observadores'
        GUID = '{58985D60-9387-476E-A2DB-45CE21B8ECBC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'NBSAPI_ENVELOPE_FILA_RESUMO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45503'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAssinaturaDigital: TFTable
    FieldDefs = <
      item
        Name = 'ID_ASSINATURA_DIGITAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Assinatura Digital'
        GUID = '{7C8B4BD7-7620-44C0-9173-03893A4ECCE5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{CE9FDAD9-1AB7-4093-8D8B-F45E09872413}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{26FCF8DB-017C-4950-8B47-20B9B7CD6C27}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{7A8D429E-CC61-47C6-A767-185B1B7303C5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{8CDF6B2C-27E1-4F00-8147-DF257C662055}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_ASSINATURA_DIGITAL'
    Cursor = 'CRM_ASSINATURA_DIGITAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45504'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiDocumentos: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{31CF17AF-74D3-4D80-B72B-9F7B299893C5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{361AFDD5-F7D4-4315-9631-99FC77EDB640}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOC_BASE64'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Base64'
        GUID = '{0BC548DA-07AC-4DF5-8AA3-15F9EF21AE94}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Documento'
        GUID = '{365A8E79-EA34-459F-8474-65A31DEF3F71}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXTENSAO_ARQ_DOC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Extens'#227'o Arq Documento'
        GUID = '{58A626B1-76CD-4DEA-9EC2-B9851C0A79C4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia'
        GUID = '{0587E115-D72B-4AA4-9E31-BC4A24FBAC64}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_DOC_EXTERNAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Documento External'
        GUID = '{71258EA6-0946-4D7F-B60F-E98D8FD05CBA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROCESSADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Processado'
        GUID = '{2315D248-0EF3-4921-9259-B00A94111AE6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBSAPI_DOCUMENTOS'
    Cursor = 'NBSAPI_DOCUMENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45505'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAssinaturaDigitalDoc: TFTable
    FieldDefs = <
      item
        Name = 'ID_ASSINATURA_DIGITAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Assinatura Digital'
        GUID = '{57D1DA60-646D-48C2-A08B-3203C20DDB23}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Item'
        GUID = '{5CA6E8CF-1EE3-403C-9B18-B1AA7F07CF29}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{F17D3BB5-E59F-4414-ACF3-93CD6BB938CA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RELATORIO_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Relatorio Nome'
        GUID = '{A6E1146D-FAEE-4C1A-934C-B9694057F6A3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{4396318A-6C19-434E-A8B7-FDA332A81F86}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_ASSINATURA_DIGITAL_DOC'
    Cursor = 'CRM_ASSINATURA_DIGITAL_DOC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45506'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiDocumentosAssinados: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{621BAEE5-4027-475C-A12E-F5FD397E11C5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENVELOPE_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Envelope Id.'
        GUID = '{F3ED5D91-E7BB-4675-AFDE-37F444664590}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENT_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Document Id.'
        GUID = '{4F9070D1-8F76-474F-A7E2-0BEBAAA0E2A4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Documento'
        GUID = '{2361FF06-5695-4CDC-AA51-E6DFD6DF89B9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Assinatura'
        GUID = '{D8AF0FE8-7040-4E6D-80D2-AB5E3C898168}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DOCUMENTO_ASSINADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Documento Assinado'
        GUID = '{468B41FE-77C8-434A-AD94-283E5BB2E09E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBSAPI_DOCUMENTOS_ASSINADOS'
    Cursor = 'NBSAPI_DOCUMENTOS_ASSINADOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;45507'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiEnvelopeFilaRank: TFTable
    FieldDefs = <
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{DA630CEA-CF8A-4672-B0CB-7E1D2114A3A2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA_MAIS_RECENTE_RANK'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura Mais Recente Rank'
        GUID = '{A1E16298-7F35-43B8-B923-99972192FDB6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'NBSAPI_ENVELOPE_FILA_RANK'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45506;47408'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
