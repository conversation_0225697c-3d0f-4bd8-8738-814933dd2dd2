object FrmObservacaoPendenciaFaturamento: TFForm
  Left = 44
  Top = 162
  ActiveControl = vBoxObservacao
  Caption = 'Observa'#231#227'o'
  ClientHeight = 349
  ClientWidth = 484
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600503'
  ShortcutKeys = <>
  InterfaceRN = 'ObservacaoPendenciaFaturamentoRN'
  Access = False
  ChangedProp = 
    'FrmObservacaoPendenciaFaturamento.Height;'#13#10#13#10'FrmObservacaoPenden' +
    'ciaFaturamento.ActiveControlFrmObservacaoPendenciaFaturamento.Wi' +
    'dth;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxObservacao: TFVBox
    Left = 0
    Top = 0
    Width = 484
    Height = 349
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxButton: TFHBox
      Left = 0
      Top = 0
      Width = 322
      Height = 65
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnSalvar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Salvar'
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        Visible = False
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001374944415478DA6364A031608431FEFFFF2F09A4F449D4FF1D88CF33
          32327EC26B01D4F00740CC468623419654002D9984CF020F20B51D88D700F141
          206E00626134B5CB80F838946D09C451503D1A40AC08C48D404B1A0859900B54
          3405C8BF03642BA3A9F504CAED40570FC4AB81783F106B62B384620BA0EAC571
          5942150BA062C896E4C3E2846A16205972028841B43850EE332916608B64140B
          A0665500A976207600CA1DC465C152A80184800550FD49340B7280D464988F71
          59C002645B0331271EC36F03D5DE451724CA02225C8E130C0E0B880C2264002A
          2E8E02F5FE21D602622319192C03EA8D26D60250321560809449C40090BA0F40
          BD2AA458C000D24064B8C3D58F5A40B105C8150E030369910CA3ED813804DD02
          4AAA4C6CE017102B002D784E69A58F0B5C04190E0E362A1988130000D0053128
          1A34B73E0000000049454E44AE426082}
        ImageId = 310032
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnVoltar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D494844520000001E00000019080600000026359E
          1A000001DC4944415478DAEDD6CF2B04611CC771CF4EAC6D776D5A3FF2EB226C
          92D64589A2251BE5A6DCA4943B07C5C93F20F9071C484E0845C82E2739283789
          9B9B835CD0B6DA19EFA9E7306DDBEC33BBB3EB62EAE99966BEDFCF6B9F999D9D
          15157FB4897F5865D3753DECF178DECB061B865105BA278498667F57D3B4D992
          C3805EB07DD029F921D2C0DE92C220D5C087A09396633AB0563218D00772041A
          CF3EC77D2EF8CB69DB08EA073D011DCDD92C84C6D05D850103C0A7040FDBD41C
          73FE81DD4BE63B8651140C5843E8194143AA41D4BF50BFC1D862FC3886090801
          9FD33C50C825A4FF91DE39EEFFBD324C532DE8058DFD85A0969C34190BE03B4A
          70269331EFD57831A805D7C99A07DF5659711F2BBEA221EC126EAE3C067E6B0B
          9B1B70940613AF73097F068E9297B28525DE4B4382E27A3770B625F0CDBCB0C4
          7B24DEE8C2AA5F81DBC9CAE485F3E11C4FC9472EC4686074726C907984E3C11C
          7163E0492558E2DD126FCA82BF7947F873D4FB986638BF4A4FC452BF4EFDB232
          2CC3223426096AB6047D1114B0B9BC958C15C69AF99BCE7C437DCC112CF12E89
          B7C8E04F82820A7D13E67B9CDD0FEADB1CC332A483906BF056E62782BA15FBE2
          D41F583FA8E3F72921E6BD362F59822FCB9B83BE45EB2355B67F99AC58585F9B
          BFD1F70D29E2565C250000000049454E44AE426082}
        ImageId = 4600399
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FLabel1: TFLabel
      Left = 0
      Top = 66
      Width = 58
      Height = 13
      Caption = 'Observa'#231#227'o'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      Visible = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object mmObservacao: TFMemo
      Left = 0
      Top = 80
      Width = 470
      Height = 90
      CharCase = ccNormal
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Lines.Strings = (
        'mmObservacao')
      Maxlength = 0
      ParentFont = False
      TabOrder = 1
      Visible = False
      FieldName = 'OBSERVACAO'
      Table = tbPendencia
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      Constraint.CheckWhen = cwImmediate
      Constraint.CheckType = ctExpression
      Constraint.FocusOnError = False
      Constraint.EnableUI = True
      Constraint.Enabled = False
      Constraint.FormCheck = True
    end
    object FLabel2: TFLabel
      Left = 0
      Top = 171
      Width = 51
      Height = 13
      Caption = 'Mensagem'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object FMemo1: TFMemo
      Left = 0
      Top = 185
      Width = 185
      Height = 110
      CharCase = ccNormal
      Enabled = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Lines.Strings = (
        'FMemo1')
      Maxlength = 0
      ParentFont = False
      TabOrder = 2
      FieldName = 'MENSAGEM'
      Table = tbPendencia
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      Constraint.CheckWhen = cwImmediate
      Constraint.CheckType = ctExpression
      Constraint.FocusOnError = False
      Constraint.EnableUI = True
      Constraint.Enabled = False
      Constraint.FormCheck = True
    end
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbPendencia
        GUID = '{590FA4EB-FC70-4CBB-ADEE-D50003A45A11}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbPendencia: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCR_OS_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Os Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Mensagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'OS_PENDENCIA'
    TableName = 'OS_PENDENCIA'
    Cursor = 'OS_PENDENCIA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600503;46002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
