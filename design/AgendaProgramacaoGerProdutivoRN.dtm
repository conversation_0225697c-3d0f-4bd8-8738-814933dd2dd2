object AgendaProgramacaoGerProdutivoRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '466014'
  Height = 299
  Width = 442
  object tbProgramacaoSitProdDetail: TFTable
    FieldDefs = <
      item
        Name = 'ROW_NUMBER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Row Number'
        GUID = '{F42FC4EC-8457-4D49-AAF3-FB807E78DF3C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        GUID = '{1B812811-A740-45BE-A892-85EB9E4A4AC3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        GUID = '{A24492FF-F4AD-4F4A-B843-75BC20165FA0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        GUID = '{EB67E56C-A594-4D04-A353-1F9D88F0332A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Entrada'
        GUID = '{101C34F0-6854-4C78-B70E-2010FC0D3985}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{A426FC26-3055-462B-819D-AAA5631213DB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{15910EF7-FC16-4562-A212-7BA364082A18}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda'
        GUID = '{E108959D-980B-4BBD-B21C-384626DCFA48}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_BOLSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Bols'#227'o'
        GUID = '{2746BE2E-26E3-4762-922F-02CAD5055A0C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os Tipo'
        GUID = '{39DA64CB-72E7-4A9D-A5AF-58C5D31D08A2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Trab'
        GUID = '{7FDAF188-8735-48EB-B5D4-4DFEFB4CE656}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo Servi'#231'o'
        GUID = '{00A799C4-4230-4553-A2D8-9C7DBAB75ADB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PROGRAMACAO_SIT_PROD_DETAIL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466014;46601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTempGauge: TFTable
    FieldDefs = <
      item
        Name = 'PERC_TRAB'
        Calculated = True
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'PERC_TRAB'
        GUID = '{02D363F1-4074-407A-B80C-71A0377CE52A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DUAL'
    Cursor = 'DUAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466014;46602'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaComboProdutivo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{AAFB745C-1E6C-42A2-9F30-0513ECAE5B0A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{724009D1-1267-4EDF-8B41-A2B99EABCEC6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_PRODUTIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '466014;46604'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
