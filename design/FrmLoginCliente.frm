object FrmLoginCliente: TFForm
  Left = 320
  Top = 162
  ActiveControl = cbxTema
  Caption = 'Form Login Cliente'
  ClientHeight = 430
  ClientWidth = 826
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '340052'
  ShortcutKeys = <>
  InterfaceRN = 'LoginClienteRN'
  Access = False
  ChangedProp = 
    'FrmLoginCliente.Height;'#13#10#13#10'FrmLoginCliente_1.Touch.InteractiveGe' +
    'stures;'#13#10'FrmLoginCliente_1.Touch.InteractiveGestureOptions;'#13#10'Frm' +
    'LoginCliente_1.Touch.ParentTabletOptions;'#13#10'FrmLoginCliente_1.Tou' +
    'ch.TabletOptions;'#13#10'FrmLoginCliente_1.Touch.InteractiveGestures;'#13 +
    #10'FrmLoginCliente_1.Touch.InteractiveGestureOptions;'#13#10'FrmLoginCli' +
    'ente_1.Touch.ParentTabletOptions;'#13#10'FrmLoginCliente_1.Touch.Table' +
    'tOptions;FrmLoginCliente.BackgroundImage;'#13#10#13#10'FrmLoginCliente.Act' +
    'iveControl'
  Spacing = 0
  BackgroundImage = 'images/CRM.jpg'
  PixelsPerInch = 96
  TextHeight = 13
  object FHBox3: TFHBox
    Left = 0
    Top = 402
    Width = 842
    Height = 28
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Color = 6776679
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    ParentBackground = False
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 0
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    object FHBox4: TFHBox
      Left = 0
      Top = 0
      Width = 409
      Height = 23
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
    end
    object FVBox1: TFVBox
      Left = 409
      Top = 0
      Width = 392
      Height = 23
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 0
      Flex.Vflex = ftTrue
      Flex.Hflex = ftMin
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      object FHBox5: TFHBox
        Left = 0
        Top = 0
        Width = 185
        Height = 9
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 0
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
      end
      object FLabel5: TFLabel
        Left = 0
        Top = 10
        Width = 278
        Height = 13
        Align = alRight
        Caption = 'Copyright NBS Inform'#225'tica - Todos os direitos reservados '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
    end
    object FVBox2: TFVBox
      Left = 801
      Top = 0
      Width = 10
      Height = 7
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
    end
  end
  object FHBox2: TFHBox
    Left = 0
    Top = 32
    Width = 824
    Height = 367
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 1
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    object FGridPanelLabel: TFGridPanel
      Left = 0
      Top = 0
      Width = 697
      Height = 197
      Align = alClient
      Caption = 'FGridPanelLabel'
      ColumnCollection = <
        item
          Value = 84.223292793855070000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAbsolute
          Value = 180.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 15.776707206144930000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      ControlCollection = <
        item
          Column = 1
          Control = cbxTema
          Row = 0
        end
        item
          Column = 1
          Control = txbUsuario
          Row = 1
        end
        item
          Column = 1
          Control = txbSenha
          Row = 2
        end
        item
          Column = 1
          Control = btnLogin
          Row = 5
        end
        item
          Column = 0
          Control = lblTema
          Row = 0
        end
        item
          Column = 0
          Control = FLabel3
          Row = 1
        end
        item
          Column = 0
          Control = FLabel4
          Row = 2
        end
        item
          Column = 1
          Control = FCheckBoxLembrarSenha
          Row = 3
        end
        item
          Column = 1
          Control = lblEsqueceuSenha
          Row = 4
        end>
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      RowCollection = <
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 50.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      AllRowFlex = True
      WOwner = FrInterno
      WOrigem = EhNone
      ColumnTabOrder = False
      DesignSize = (
        697
        197)
      object cbxTema: TFCombo
        Left = 434
        Top = 1
        Width = 145
        Height = 21
        Flex = True
        ReadOnly = False
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Selecione'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = False
        HideClearButtonOnNullValue = False
        OnChange = cbxTemaChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
      end
      object txbUsuario: TFString
        Left = 434
        Top = 22
        Width = 145
        Height = 21
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        CharCase = ccUpper
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = txbUsuarioEnter
        OnExit = txbUsuarioExit
        SaveLiteralCharacter = True
      end
      object txbSenha: TFString
        Left = 434
        Top = 43
        Width = 145
        Height = 21
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = True
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        CharCase = ccNormal
        Pwd = True
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        OnEnter = txbSenhaEnter
        OnExit = txbSenhaExit
        SaveLiteralCharacter = True
      end
      object btnLogin: TFButton
        Left = 539
        Top = 94
        Width = 75
        Height = 25
        Align = alRight
        Caption = 'Login'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 3
        OnClick = btnLoginClick
        ImageId = 0
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object lblTema: TFLabel
        Left = 399
        Top = 1
        Width = 35
        Height = 21
        Align = alRight
        Caption = 'Tema:'
        Color = clBtnFace
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentColor = False
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        ExplicitHeight = 14
      end
      object FLabel3: TFLabel
        Left = 399
        Top = 22
        Width = 35
        Height = 21
        Align = alRight
        Caption = 'E-mail:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        ExplicitHeight = 14
      end
      object FLabel4: TFLabel
        Left = 396
        Top = 43
        Width = 38
        Height = 21
        Align = alRight
        Caption = 'Senha:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 16645629
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        ExplicitHeight = 14
      end
      object FCheckBoxLembrarSenha: TFCheckBox
        Left = 475
        Top = 64
        Width = 97
        Height = 17
        Anchors = []
        Caption = 'Lembrar Senha?'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 4
        CheckedValue = 'S'
        UncheckedValue = 'N'
        OnCheck = FCheckBoxLembrarSenhaCheck
        ReadOnly = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taAlignTop
      end
      object lblEsqueceuSenha: TFLabel
        Left = 434
        Top = 81
        Width = 84
        Height = 13
        Align = alLeft
        Caption = 'Esqueceu Senha?'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsUnderline]
        ParentFont = False
        OnClick = lblEsqueceuSenhaClick
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
      end
    end
  end
  object FHBox1: TFHBox
    Left = 0
    Top = 0
    Width = 826
    Height = 33
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 2
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
  end
  object tbSchema: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRIPTION'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SYS.NBS_SCHEMA'
    Cursor = 'NBS_SCHEMA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbSchemaUser: TFTable
    FieldDefs = <
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SCHEMA_USER'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NBS_SCHEMA_USER'
    Cursor = 'NBS_SCHEMA_USER'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbUsuarioLogado: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'USUARIO_LOGADO'
    Cursor = 'USUARIO_LOGADO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbBtobCadastro: TFTable
    FieldDefs = <
      item
        Name = 'ID_BTOB'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BTOB_CADASTRO'
    Cursor = 'BTOB_CADASTRO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340052;34007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
end
