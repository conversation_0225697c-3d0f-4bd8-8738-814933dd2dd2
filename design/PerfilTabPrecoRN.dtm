object PerfilTabPrecoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310039'
  Height = 299
  Width = 442
  object tbPerfilTabPreco: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAB_PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PERFIL_TAB_PRECO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310039;31001'
    DeltaMode = dmChanged
  end
end
