object FichaItemLocacaoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310045'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbItemLocacaoDispEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Do Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOCACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Loca'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ITEM_LOCACAO_DISP_EMP'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310045;31001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLocalEstoque: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_LOCAL_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Local Estoque'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Do Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INITCAP_NOME_LOCAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Initcap Nome Local'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'LOCAL_ESTOQUE'
    Cursor = 'LOCAL_ESTOQUE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310045;31002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
