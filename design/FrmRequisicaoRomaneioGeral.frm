object FrmRequisicaoRomaneioGeral: TFForm
  Left = 321
  Top = 163
  ActiveControl = FVBox1
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Romaneio Total'
  ClientHeight = 690
  ClientWidth = 515
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '17609'
  ShortcutKeys = <>
  InterfaceRN = 'RequisicaoRomaneioGeralRN'
  Access = False
  ChangedProp = 
    'FrmRequisicaoRomaneioGeral.Width;'#13#10'FrmRequisicaoRomaneioGeral.He' +
    'ight;'#13#10'FrmRequisicaoRomaneioGeral.ActiveControlFrmRequisicaoRoma' +
    'neioGeral.Width;'#13#10'FrmRequisicaoRomaneioGeral.ActiveControl;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 515
    Height = 690
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxBtnTopo: TFHBox
      Left = 0
      Top = 0
      Width = 509
      Height = 65
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 63
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 63
        Top = 0
        Width = 70
        Height = 59
        Hint = 'Aceitar'
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
          0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
          1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
          95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
          101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
          1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
          A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
          C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
          482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
          1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
          C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
          8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
        ImageId = 700088
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object gridRequisicao: TFGrid
      Left = 0
      Top = 66
      Width = 320
      Height = 200
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbReqAssinaturaGrid
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = False
      Paging.PageSize = 0
      Paging.DbPaging = False
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      ActionButtons.BtnAccept = False
      ActionButtons.BtnView = False
      ActionButtons.BtnEdit = False
      ActionButtons.BtnDelete = False
      ActionButtons.BtnInLineEdit = False
      CustomActionButtons = <>
      ActionColumn.Title = 'A'#231#245'es'
      ActionColumn.Width = 100
      ActionColumn.TextAlign = taCenter
      ActionColumn.Visible = True
      Columns = <
        item
          Expanded = False
          FieldName = 'REQUISICAO'
          Font = <>
          Title.Caption = 'Requisi'#231#227'o'
          Width = 40
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{3CBFC0F1-C3ED-48C0-BB64-78D2FCC69110}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'QTDE_ITEM'
          Font = <>
          Title.Caption = 'Total Item'
          Width = 90
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{42F8DB56-C4EE-4379-B411-3B256009FE36}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end
        item
          Expanded = False
          FieldName = 'ENTREGUE'
          Font = <>
          Title.Caption = 'Entregue ?'
          Width = 90
          Visible = True
          Precision = 0
          TextAlign = taCenter
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <
            item
              Expression = 'ENTREGUE = '#39'S'#39
              EvalType = etExpression
              GUID = '{985E4A21-7303-4DB2-B44C-487DEA6C9E25}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 7000105
              Color = clBlack
            end
            item
              Expression = 'ENTREGUE = '#39'N'#39
              EvalType = etExpression
              GUID = '{BBD2AD7C-5ED5-487D-89D5-32DBF16B13C0}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 7000106
              Color = clBlack
            end>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = False
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          Editor.Filter = False
          Editor.ShowClearButton = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{FC714457-47C8-41BB-9CDA-732E5EFF2616}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
          Priority = 0
        end>
    end
    object vboxObservacao: TFVBox
      Left = 0
      Top = 267
      Width = 506
      Height = 93
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FLabel5: TFLabel
        Left = 0
        Top = 0
        Width = 67
        Height = 13
        Caption = 'Observa'#231#227'o'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object edtObservacao: TFMemo
        Left = 0
        Top = 14
        Width = 498
        Height = 69
        CharCase = ccNormal
        Enabled = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Lines.Strings = (
          'edtObservacao')
        Maxlength = 0
        ParentFont = False
        TabOrder = 0
        FieldName = 'OBSERVACAO'
        Table = tbReqAssinaturaGrid
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        WOwner = FrInterno
        WOrigem = EhNone
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        Required = False
      end
    end
    object vboxAssinatura: TFVBox
      Left = 0
      Top = 361
      Width = 505
      Height = 305
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object assinaturaProdutivo: TFImage
        Left = 0
        Top = 0
        Width = 511
        Height = 253
        Stretch = False
        Table = tbReqAssinaturaTecnico
        FieldName = 'ASSINATURA_PRODUTIVO'
        WOwner = FrInterno
        WOrigem = EhNone
        BoxSize = 0
        GrayScaleOnDisable = False
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        Preview = False
        ImageId = 0
      end
      object hboxProdutivo: TFHBox
        Left = 0
        Top = 254
        Width = 185
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel1: TFLabel
          Left = 0
          Top = 0
          Width = 65
          Height = 14
          Caption = 'Produtivo:'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
        object FLabel2: TFLabel
          Left = 65
          Top = 0
          Width = 41
          Height = 14
          Caption = 'FLabel2'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          FieldName = 'NOME'
          Table = tbReqAssinaturaTecnico
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
    end
  end
  object tbReqAssinaturaGrid: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{BC5477F8-3F11-4C65-BC16-62F1BE26EEE6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{53AA3A4C-77AC-4B93-BAE6-F3AEAF71ABB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisi'#231#227'o'
        GUID = '{BCF72987-1C83-483C-A112-B449AF1A310C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Entregue'
        GUID = '{9CA33A7E-2F1F-43BB-9498-EC33C3AC3440}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Item'
        GUID = '{E003B85E-28B0-47A7-B8FE-7B8BB6A4C347}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{7AB35B73-4443-491F-AE12-ED38B3E7F9D4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_REQ_ASSINATURA_GRID'
    MaxRowCount = 200
    OnAfterScroll = tbReqAssinaturaGridAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '17609;17601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbReqAssinaturaTecnico: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{D241C94F-415E-4B7A-B3E2-18481A41CA82}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{35CD7569-8829-4A8C-8917-3B926D312C00}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{90920608-D5B2-4722-8BBD-98BD558F584D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REQUISICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Requisi'#231#227'o'
        GUID = '{52D28F08-007C-4A77-AD51-649C7B761ADC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura Produtivo'
        GUID = '{9745DB42-53EF-4A4F-B304-EFF7CD02D019}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        GUID = '{629178F1-932E-4289-8B0C-25AC076A9FC4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{A8B845E3-352B-4F67-B50A-9475BABD4C36}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio'
        GUID = '{EF834C56-3CCE-491E-991E-18589E365DB7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_REQ_ASSINATURA_TECNICO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '17609;17603'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
