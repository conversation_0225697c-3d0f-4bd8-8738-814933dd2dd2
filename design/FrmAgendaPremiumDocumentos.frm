object FrmAgendaPremiumDocumentos: TFForm
  Left = 321
  Top = 163
  ActiveControl = vboxPrincipal
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Agenda Documentos'
  ClientHeight = 209
  ClientWidth = 624
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '489015'
  ShortcutKeys = <>
  InterfaceRN = 'AgendaPremiumDocumentosRN'
  Access = False
  ChangedProp = 
    'FrmAgendaPremiumDocumentos.Width;'#13#10'FrmAgendaPremiumDocumentos.He' +
    'ight;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 624
    Height = 209
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object vboxDocOsAgenda: TFVBox
      Left = 0
      Top = 0
      Width = 614
      Height = 202
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 3
      Padding.Right = 3
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object hBoxDocTitle: TFHBox
        Left = 0
        Top = 0
        Width = 602
        Height = 33
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Color = 15724527
        Padding.Top = 5
        Padding.Left = 5
        Padding.Right = 0
        Padding.Bottom = 0
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object lblDescDocOsAgenda: TFLabel
          Left = 0
          Top = 0
          Width = 242
          Height = 19
          Caption = 'Documento Anexo a Agenda: '
          Font.Charset = DEFAULT_CHARSET
          Font.Color = 13392431
          Font.Height = -16
          Font.Name = 'Tahoma'
          Font.Style = [fsBold]
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = True
          MaskType = mtText
        end
      end
      object hboxControles: TFHBox
        Left = 0
        Top = 34
        Width = 601
        Height = 160
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object gridAnexoAgenda: TFGrid
          Left = 0
          Top = 0
          Width = 475
          Height = 160
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -11
          TitleFont.Name = 'Tahoma'
          TitleFont.Style = []
          Table = tbAgendaDoc
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Paging.Enabled = False
          Paging.PageSize = 0
          Paging.DbPaging = False
          FrozenColumns = 0
          ShowFooter = False
          ShowHeader = True
          MultiSelection = False
          Grouping.Enabled = False
          Grouping.Expanded = False
          Grouping.ShowFooter = False
          Crosstab.Enabled = False
          Crosstab.GroupType = cgtConcat
          EnablePopup = False
          WOwner = FrInterno
          WOrigem = EhNone
          EditionEnabled = False
          AuxColumnHeaders = <>
          NoBorder = False
          ActionButtons.BtnAccept = False
          ActionButtons.BtnView = False
          ActionButtons.BtnEdit = False
          ActionButtons.BtnDelete = False
          ActionButtons.BtnInLineEdit = False
          CustomActionButtons = <>
          ActionColumn.Title = 'A'#231#245'es'
          ActionColumn.Width = 100
          ActionColumn.TextAlign = taCenter
          ActionColumn.Visible = True
          Columns = <
            item
              Expanded = False
              Font = <>
              Width = 50
              Visible = True
              Precision = 0
              TextAlign = taCenter
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <
                item
                  Expression = '*'
                  EvalType = etExpression
                  GUID = '{A3A0ADBA-7536-4F8C-8512-9B840CC1121A}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  ImageId = 310030
                  OnClick = 'deleteDocClick'
                  Color = clBlack
                end>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{73E0E71A-4FF0-4F78-97AC-C69B19B99620}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              FieldName = 'OBSERVACAO'
              Font = <>
              Title.Caption = 'Documento'
              Width = 120
              Visible = True
              Precision = 0
              TextAlign = taLeft
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = True
              Colors = <>
              Images = <>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{DF66F2CE-6377-494D-A59C-1EE4F351B716}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              Font = <>
              Width = 50
              Visible = True
              Precision = 0
              TextAlign = taCenter
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <
                item
                  Expression = '*'
                  Hint = 'Preview Arquivo'
                  EvalType = etExpression
                  GUID = '{D37A53A7-2DE2-4F38-AF76-9556086E1E41}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  ImageId = 27107
                  OnClick = 'PreviewClick'
                  Color = clBlack
                end>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{190A2A71-939C-4FD5-908F-4EA9B700D02C}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end
            item
              Expanded = False
              Font = <>
              Width = 50
              Visible = True
              Precision = 0
              TextAlign = taCenter
              FieldType = ftString
              FlexRatio = 0
              Sort = False
              ImageHeader = 0
              Wrap = False
              Flex = False
              Colors = <>
              Images = <
                item
                  Expression = '*'
                  Hint = 'Download do Arquivo'
                  EvalType = etExpression
                  GUID = '{6451CBE6-707D-4A02-8692-16F1905A065C}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  ImageId = 4600365
                  OnClick = 'DownloadDocClick'
                  Color = clBlack
                end>
              Masks = <>
              CharCase = ccNormal
              BlobConfig.MimeType = bmtText
              BlobConfig.ShowType = btImageViewer
              ShowLabel = True
              Editor.EditType = etTFString
              Editor.Precision = 0
              Editor.Step = 0
              Editor.MaxLength = 100
              Editor.LookupFilterKey = 0
              Editor.LookupFilterDesc = 0
              Editor.PopupHeight = 400
              Editor.PopupWidth = 400
              Editor.CharCase = ccNormal
              Editor.LookupColumns = <>
              Editor.Enabled = False
              Editor.ReadOnly = False
              Editor.Filter = False
              Editor.ShowClearButton = False
              CheckedValue = 'S'
              UncheckedValue = 'N'
              HiperLink = False
              GUID = '{4417D571-BE86-4BD2-9FBC-49EEC6C4999A}'
              WOwner = FrInterno
              WOrigem = EhNone
              EditorConstraint.CheckWhen = cwImmediate
              EditorConstraint.CheckType = ctExpression
              EditorConstraint.FocusOnError = False
              EditorConstraint.EnableUI = True
              EditorConstraint.Enabled = False
              EditorConstraint.FormCheck = True
              Empty = False
              MobileOpts.ShowMobile = False
              MobileOpts.Order = 0
              BoxSize = 0
              ImageSrcType = istSource
              IconReverseDirection = False
              FooterConfig.ColSpan = 0
              FooterConfig.TextAlign = taLeft
              FooterConfig.Enabled = False
              HeaderTextAlign = taLeft
              Priority = 0
            end>
        end
        object vboxBotoes: TFVBox
          Left = 475
          Top = 0
          Width = 63
          Height = 150
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftTrue
          Flex.Hflex = ftMin
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object btnUploadFileOsAgenda: TFButton
            Left = 0
            Top = 0
            Width = 52
            Height = 48
            Hint = 'Adicionar Anexo'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            OnClick = btnUploadFileOsAgendaClick
            PngImage.Data = {
              89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
              F80000015F4944415478DAE5D6CD2B055118C7F1731961231B652979C9D68E58
              90975828C58D85BC64C71EF90FD85859287949575D0B096521B1B2B1B0B27043
              FE07895C2FDFC73C8B713BC6352F0B79EAD3DC3973667EF7CC9CCE4CC27CAD4E
              ACA30809E35F4FB8478B5F27EF454A91C11D8E51EC735E390650817334E713D0
              88231C60E2877F5F873354EAFE3EFAF20D4863FA970152AB988C33E00D0B988B
              22A05AFBD6E6B43F621E4B6103A4D6306669BFC2282EC2061418773234C1F1DC
              3A195D372EC306D86AD9B8D3B7EBFF0648FFF7380316F18AD938025630ACBF37
              311565400ABD78D65B5462DCF567246C4019B6D08A6D7468C02992BA959087A0
              010DD8C32E6670AD01F5C65D77FA756499A0018E5E6447F7BD0152831A9E8DE2
              21DB02227BC87F2BE010E3010336D0F35D80CCED5BDCE0C4D85FFA49DDA62DC7
              E42BA30D35A8C24B6E8054BB71E7BD63EC9F2D596D2FB41C9391C9323264DCD7
              E9677D006B4F871934A2EEF90000000049454E44AE426082}
            ImageId = 4600363
            WOwner = FrInterno
            WOrigem = EhNone
            Color = clBtnFace
            Access = False
            IconReverseDirection = False
          end
          object btnDownloadFileOsAgenda: TFButton
            Left = 0
            Top = 49
            Width = 52
            Height = 48
            Hint = 'Baixar Anexo'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 1
            OnClick = btnDownloadFileOsAgendaClick
            PngImage.Data = {
              89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
              F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
              0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
              1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
              95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
              101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
              1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
              A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
              C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
              482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
              1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
              C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
              8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
            ImageId = 700088
            WOwner = FrInterno
            WOrigem = EhNone
            Color = clBtnFace
            Access = False
            IconReverseDirection = False
          end
        end
      end
    end
  end
  object tbAgendaDoc: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{704BE33D-8924-40E5-8C94-D997D4D6DFE9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda'
        GUID = '{438E53A7-D062-435A-9B58-B7EC85BB3072}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia'
        GUID = '{7EB43B4E-FF77-4A35-B07A-A5B8675BB57C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Arquivo'
        GUID = '{65814DBE-7EFA-452E-8E44-0E137B2A2E8F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXTENSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Extens'#227'o'
        GUID = '{62B81464-CCDA-460F-AB19-3610E326B1D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        GUID = '{9255D933-0EA8-4622-8457-E40FE9A5736E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Arquivo'
        GUID = '{8E8EF213-CB49-469F-8678-1D08F022B588}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIRETORIO_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Diretorio Arquivo'
        GUID = '{CF22D486-7A56-4834-B67C-4A9498D6D0BE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_AGENDA_DOC'
    Cursor = 'OS_AGENDA_DOC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '489015;48901'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 2
    Top = 14
  end
end
