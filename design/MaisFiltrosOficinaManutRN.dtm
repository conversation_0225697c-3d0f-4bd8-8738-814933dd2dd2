object MaisFiltrosOficinaManutRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '242012'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbServicosGrupo: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SERVICOS_GRUPO'
    Cursor = 'SERVICOS_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicosSubGrupo: TFTable
    FieldDefs = <
      item
        Name = 'COD_GRUPO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_GRUPO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Grupo Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SUB_GRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Sub Grupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SUBGRUPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Subgrupo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SERVICOS_SUB_GRUPO'
    Cursor = 'SERVICOS_SUB_GRUPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24203'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicosSetores: TFTable
    FieldDefs = <
      item
        Name = 'COD_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO_PC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Plano Contas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_INTEGRACAO_SETOR_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Integra'#231#227'o Setor Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALTERAR_NA_CODIFICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Alterar Na Codifica'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COR_SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cor Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SETOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Setor'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'SERVICOS_SETORES'
    Cursor = 'SERVICOS_SETORES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24204'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMarcas: TFTable
    FieldDefs = <
      item
        Name = 'COD_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_COD_QUESTIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm C'#243'd. Questionario'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MARCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Marca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_ABRAC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Abrac'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORD_FACIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ford Facil'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_EXTRANET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Extranet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PARCELAR_FINANCEIRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Parcelar Financeiro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTRADA_MULTIPLA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Entrada Multipla'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAO_GERAR_AVISO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#227'o Gerar Aviso Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CALC_CAPLAN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Calc Caplan'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GERAR_TIPO_PGTO_VW'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Gerar Tipo Pagamento Vw'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CALC_FLOORPLAN_DATA_PAGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Calc Floorplan Data Pagto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BONUS_COMO_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'B'#244'nus Como Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENCARGOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Encargos'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIG_TOT_NOTA_FAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Orig Tot Nota Fab'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FLOOR_PLAN_POR_DATA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Floor Plan Por Data Entrada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_VEICULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Ve'#237'culo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CONCESSIONARIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Concessionaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MARCA_BMW'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Marca Bmw'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MARCA2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Marca2'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'MARCAS'
    Cursor = 'MARCAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24205'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProdutosModelos: TFTable
    FieldDefs = <
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_NOTA_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Nota Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_CONTABIL_VEIC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Contabil Veic'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NCM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ncm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ESPECIE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Esp'#233'cie'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIAS_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Dias Reserva'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ano Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_TABELA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Tabela'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRANSITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tr'#226'nsito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CARENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Carencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOD_VER_SERIE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#243'dulo Ver S'#233'rie'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_FIXA_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Fixa Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_FIXA_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Fixa Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Minima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MAXIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Maxima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Minima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MAXIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Maxima'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MINIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Minima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MINIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Minima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_MAXIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Maxima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_MAXIMA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Maxima Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_FORPLAN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Forplan'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MULTA_PERCENT_FORPLAN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Multa Percent Forplan'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Custo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROMOCAO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Promo'#231#227'o Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICA_MARGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica Margem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PATIO_VALOR_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Patio Valor Dia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PATIO_CARENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Patio Carencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_LUCRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Lucro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_LUCRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Lucro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_NOTA_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Nota Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAXA_ALFANDEGARIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Taxa Alfandegaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURO_FRETE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguro Frete'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MIN2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Min2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MIN_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Min Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MIN_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Min Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MIN2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Min2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MIN_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Min Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MAX2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Max2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MAX2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Max2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MAX_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Max Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CG_MAX_PROMO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cg Max Promo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CV_MIN2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cv Min2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_GERENTE_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Gerente Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RENAVAM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Renavam'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OFICINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Oficina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_VEICULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ve'#237'culo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_USADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Usado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_CONSIGNADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Consignado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAIXA_MINIMA_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Faixa Minima Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAIXA_MINIMA_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Faixa Minima Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_INTERNO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Interno2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_VEICULO_ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ve'#237'culo Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_PORTAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Portas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_LUGARES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Lugares'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_TAXI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Taxi'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_DEFICIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Deficiente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRASE_TAXI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frase Taxi'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRASE_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frase Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRASE_DEFICIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frase Deficiente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHASSI_BASICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chassi Basico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDA_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Venda Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_BRUTA_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Bruta Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HOLD_BACK_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Hold Back Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_COMISSAO_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Comiss'#227'o Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIXO_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Fixo Vendedor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FIXO_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Fixo Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_GERENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Gerente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ADM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Adm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ADM_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Adm Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HB_VD_NO_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hb Vd No Custo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO_DIVERSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o Diverso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_HORA_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Hora Garantia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ford'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERFACE_ATUALIZACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Interface Atualiza'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMISSAO_FIXA_VD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Comiss'#227'o Fixa Vd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRODUTO_FORCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Produto Forca'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Ford'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_RENAVAM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Renavam'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRANSMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Transmiss'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ_ALCOOL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido Alcool'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ_GAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido Gas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POT_MAX_LIQ_DIESEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pot Max L'#237'quido Diesel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POPULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Popular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_VEICULO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Veiculo2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMBUSTIVEL_FIPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Combustivel Fipe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_TAXI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Taxi'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_DEFICIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Deficiente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_SIGA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Siga'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_COMI_INTERNET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Comi Internet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_VERSAOANO_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Versaoano Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CAMBIO_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cambio Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_NPORTAS_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Nportas Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IPI_IMPORTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Ipi Importado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORCAR_OPCIONAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forcar Opcional'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_MOLICAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Molicar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO_EXTRANET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo Extranet'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACOES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observac'#245'es'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_ICMS_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Icms Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARCA_H3S_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Marca H3s Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO_H3S_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo H3s Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VERSAO_H3S_HONDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vers'#227'o H3s Honda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CILINDRADAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Cilindradas'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CMT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Cmt'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_USD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Usd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_INTERNACIONAL_BRL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Internacional Brl'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FRETE_INTERNACIONAL_USD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Frete Internacional Usd'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_KIT_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Kit Fabrica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GTIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Gtin'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_IPI_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Ipi Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_COMBUSTIVEL_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Combustivel Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_COR_WEBM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cor Webm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_ATACADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe Atacado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INCIDE_PIS_COFINS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Incide Pis Cofins'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_GARANTIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Garantida'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_GARANTIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Garantida'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_GERENTE_GARANTIDA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Gerente Garantida Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COM_VENDEDOR_GARANTIDA_PROMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'com Vendedor Garantida Promo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAPLAN_GM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Caplan Gm'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MOTOR_BMW'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Motor Bmw'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_BRUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso Bruto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_MEDIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso M'#233'dio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PESO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Peso L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DISTANCIA_EIXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Distancia Eixo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_MODELO_FORMAT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Modelo Format'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORIGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Origem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_IMAGEM_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Imagem Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MESES_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Meses Garantia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_INTERNO3'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Interno3'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cest'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_COMBUSTIVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Combustivel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INCLUSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Inclus'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SAF_ALT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Saf Alt'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SAF_ENVIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Saf Envio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO_ALTERACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Altera'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_FIPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Fipe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO_FABRICA_INTERFACE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo Fabrica Interface'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INCIDE_PIS_FRETE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Incide Pis Frete'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO_COMBO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo Combo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Initcap'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_INITCAP2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Initcap2'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_MODELO_ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Modelo Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_LINHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label Linha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_MVS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label Mvs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LABEL_COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Label C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'PRODUTOS_MODELOS'
    Cursor = 'PRODUTOS_MODELOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24206'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbKitsGrupoVendaCombo: TFTable
    FieldDefs = <
      item
        Name = 'ID_GRUPO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Grupo Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'KITS_GRUPO_VENDA_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24207'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbProdutosModelosSimples: TFTable
    FieldDefs = <
      item
        Name = 'VEIC_FAMILIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Veic Familia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PRODUTOS_MODELOS_SIMPLES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '242012;24208'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
