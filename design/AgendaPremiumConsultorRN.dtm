object AgendaPremiumConsultorRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '430048'
  Height = 299
  Width = 442
  object tbAgendaTmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COR_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cor Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INICIO_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data In'#237'cio Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_FIM_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Fim Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA1_ESQUERDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha1 Esquerda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMAGEM_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imagem Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA2_ESQUERDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha2 Esquerda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA2_DIREITA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha2 Direita'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Placa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_AGENDA_TMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430048;43001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultorRecepcao: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTOR_RECEPCAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430048;43002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultores: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COR_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cor Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INICIO_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data In'#237'cio Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_FIM_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Fim Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA1_ESQUERDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha1 Esquerda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMAGEM_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imagem Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA2_ESQUERDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha2 Esquerda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA2_DIREITA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha2 Direita'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Placa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_AGENDA_TMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430048;43004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbChips: TFTable
    FieldDefs = <
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COR_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cor Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INICIO_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data In'#237'cio Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_FIM_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Fim Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA1_ESQUERDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha1 Esquerda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMAGEM_CHIP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imagem Chip'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA2_ESQUERDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha2 Esquerda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LINHA2_DIREITA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Linha2 Direita'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Time'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Placa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_AGENDA_TMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430048;43005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbAgenda: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_OS_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Os Agenda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_AGENDADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Agendada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PREVISAO_FIM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Previs'#227'o Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROMETIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Prometida'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_PROMETIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Prometida'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_COMBINADO_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Combinado Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR_ENTREGA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor Entrega'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_AGENDA'
    Cursor = 'OS_AGENDA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430048;43006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaComboConsultor: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_CONSULTOR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430048;46008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
