object DadosSintegraRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '5300716'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbDadosCadastraisReceita: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAD_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cad Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAD_PUBLICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cad Publico'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_DADOS_CADASTRAIS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosCadastraisSintegra: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAD_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cad Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAD_PUBLICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cad Publico'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_DADOS_CADASTRAIS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosFisicos: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANIVERSARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Aniversario'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DADOS_FISICOS'
    Cursor = 'DADOS_FISICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientes: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES'
    Cursor = 'CLIENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosJuridicos: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSC_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Insc Estadual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNAE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnae'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DADOS_JURIDICOS'
    Cursor = 'DADOS_JURIDICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsPendenciaCadIntegracao: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CNPJ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cnpj'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_NASCIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Nascimento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEM_PENDENCIAS_ORC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tem Pendencias Orc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAD_ATUALIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cad Atualizado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PENDENCIA_CAD_INTEGRACAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSchemaAtual: TFTable
    FieldDefs = <
      item
        Name = 'SERVICE_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Service Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Schema Name'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SCHEMA_ATUAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPessoaFisica: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_PESSOA_FISICA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEnderecoPessoaJuridica: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNPJ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnpj'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAZAO_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Raz'#227'o Social'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FANTASIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Fantasia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Situa'#231#227'o Cadastral'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNAE_PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnae Principal'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOGRADOURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Logradouro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio Ibge'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ibge'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Ibge'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Endere'#231'o Eletronico'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_PESSOA_JURIDICA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;53009'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEnderecoSintegra: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio Ibge'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ibge'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOGRADOURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Logradouro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FANTASIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Fantasia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNAE_PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnae Principal'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_CADASTRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Cadastro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CNPJ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cnpj'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_RAZAO_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Raz'#227'o Social'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_SINTEGRA_DADOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;530010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEnderecoClienteDiverso: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Endere'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;530011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidades: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;530012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteEnderecoInscricao: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PROPRIEDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Propriedade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_TELEFONE_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Telefone Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CX_POSTAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cx Postal'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ENDERECO_POR_INSCRICAO'
    Cursor = 'CLIENTE_ENDERECO_INSCRICAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300716;530013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
