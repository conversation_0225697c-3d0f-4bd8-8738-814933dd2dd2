object FrmClientesFlags: TFForm
  Left = 44
  Top = 163
  ActiveControl = pgFlags
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Flags - Condi'#231#245'es especiais'
  ClientHeight = 622
  ClientWidth = 1020
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Touch.InteractiveGestures = []
  Touch.InteractiveGestureOptions = []
  Touch.ParentTabletOptions = False
  Touch.TabletOptions = []
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310036'
  ShortcutKeys = <>
  InterfaceRN = 'ClientesFlagsRN'
  Access = False
  ChangedProp = 
    'FrmClientesFlags.Height;'#13#10'FrmClientesFlags.ActiveControl;'#13#10'FrmCl' +
    'ientesFlags_1.Touch.InteractiveGestures;'#13#10'FrmClientesFlags_1.Tou' +
    'ch.InteractiveGestureOptions;'#13#10'FrmClientesFlags_1.Touch.ParentTa' +
    'bletOptions;'#13#10'FrmClientesFlags_1.Touch.TabletOptions;'#13#10'FrmClient' +
    'esFlags.ActiveControlFrmClientesFlags.ActiveControlFrmClientesFl' +
    'ags_1.Touch.InteractiveGestures;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object hBoxLinha01: TFHBox
    Left = 0
    Top = 0
    Width = 1020
    Height = 57
    Align = alTop
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    Padding.Top = 0
    Padding.Left = 5
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftFalse
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    VAlign = tvTop
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object vBoxNome: TFVBox
      Left = 0
      Top = 0
      Width = 630
      Height = 47
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblNomeCliente2: TFLabel
        Left = 0
        Top = 0
        Width = 32
        Height = 13
        Caption = 'Nome'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object lblNomeCliente: TFLabel
        Left = 0
        Top = 14
        Width = 197
        Height = 24
        Caption = 'Antonio Carlos Orione'
        Font.Charset = ANSI_CHARSET
        Font.Color = clNavy
        Font.Height = -19
        Font.Name = 'Trebuchet MS'
        Font.Style = [fsBold]
        ParentFont = False
        FieldName = 'NOME'
        Table = tbClienteDiversoFlag
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
    end
    object hBoxSeparadorHorizontal01: TFHBox
      Left = 630
      Top = 0
      Width = 13
      Height = 47
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object vBoxCodigo: TFVBox
      Left = 643
      Top = 0
      Width = 185
      Height = 47
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblCodigo: TFLabel
        Left = 0
        Top = 0
        Width = 38
        Height = 13
        Caption = 'C'#243'digo'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object lblCodCliente: TFLabel
        Left = 0
        Top = 14
        Width = 142
        Height = 24
        Caption = '993.313.301-20'
        Font.Charset = ANSI_CHARSET
        Font.Color = clNavy
        Font.Height = -19
        Font.Name = 'Trebuchet MS'
        Font.Style = [fsBold]
        ParentFont = False
        FieldName = 'CPF_CGC'
        Table = tbClienteDiversoFlag
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
    end
    object hBoxSeparadorHorizontal02: TFHBox
      Left = 828
      Top = 0
      Width = 12
      Height = 47
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 3
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
    end
    object vBoxCurva: TFVBox
      Left = 840
      Top = 0
      Width = 173
      Height = 47
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 4
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftFalse
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblCurva: TFLabel
        Left = 0
        Top = 0
        Width = 33
        Height = 13
        Caption = 'Curva'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
      object FLabel11: TFLabel
        Left = 0
        Top = 14
        Width = 52
        Height = 24
        Caption = 'Curva'
        Font.Charset = ANSI_CHARSET
        Font.Color = clNavy
        Font.Height = -19
        Font.Name = 'Trebuchet MS'
        Font.Style = [fsBold]
        ParentFont = False
        FieldName = 'CURVA'
        Table = tbClienteDiversoFlag
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
      end
    end
  end
  object pgFlags: TFPageControl
    Left = 0
    Top = 57
    Width = 1020
    Height = 565
    ActivePage = tabDesconto
    Align = alClient
    TabOrder = 1
    TabPosition = tpTop
    OnChange = pgFlagsChange
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    WOwner = FrInterno
    WOrigem = EhNone
    RenderStyle = rsTabbed
    object tabDesconto: TFTabsheet
      Caption = 'Desconto'
      Visible = True
      Closable = False
      WOwner = FrInterno
      WOrigem = EhNone
      object vBoxTabDesconto: TFVBox
        Left = 0
        Top = 0
        Width = 1012
        Height = 537
        Align = alClient
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox1: TFHBox
          Left = 0
          Top = 0
          Width = 1000
          Height = 5
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object hBoxTabDescontoLinha01: TFHBox
          Left = 0
          Top = 6
          Width = 1000
          Height = 60
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox2: TFHBox
            Left = 0
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object vBoxSegmento: TFVBox
            Left = 5
            Top = 0
            Width = 250
            Height = 50
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftMin
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel10: TFLabel
              Left = 0
              Top = 0
              Width = 190
              Height = 13
              Hint = 'Segmento (afeta Desconto Letra)'
              Caption = 'Segmento (afeta Desconto Letra)'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
            object cboSegmento: TFCombo
              Left = 0
              Top = 14
              Width = 240
              Height = 21
              Hint = 
                'Segmento (afeta Desconto Letra)'#13#10#13#10'Exibido para pessoas jur'#237'dica' +
                's'
              Table = tbDadosJuridicosFlag
              LookupTable = tbClienteSegmento
              FieldName = 'ID_SEGMENTO'
              LookupKey = 'ID_SEGMENTO'
              LookupDesc = 'SEGMENTO_DESCRICAO_CODIGO'
              Flex = True
              HelpCaption = 'Segmento (afeta Desconto Letra)'
              ReadOnly = True
              WOwner = FrInterno
              WOrigem = EhNone
              Required = False
              Prompt = 'Segmento (afeta Desconto Letra)'
              Constraint.CheckWhen = cwImmediate
              Constraint.CheckType = ctExpression
              Constraint.FocusOnError = False
              Constraint.EnableUI = True
              Constraint.Enabled = False
              Constraint.FormCheck = True
              ClearOnDelKey = False
              UseClearButton = True
              HideClearButtonOnNullValue = True
              OnChange = cboSegmentoChange
              OnClearClick = cboSegmentoClearClick
              Colors = <>
              Images = <>
              Masks = <>
              Fonts = <>
              MultiSelection = False
              IconReverseDirection = False
            end
          end
          object FHBox5: TFHBox
            Left = 255
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
        object FHBox3: TFHBox
          Left = 0
          Top = 67
          Width = 1000
          Height = 5
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object hBoxTabDescontoLinha02: TFHBox
          Left = 0
          Top = 73
          Width = 1000
          Height = 200
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftMin
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox11: TFHBox
            Left = 0
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object vBoxVendaDescontoPorLetra: TFVBox
            Left = 5
            Top = 0
            Width = 500
            Height = 190
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftMin
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hBoxDecontoPorLetraLinha01: TFHBox
              Left = 0
              Top = 0
              Width = 480
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblDescontoPorLetra: TFLabel
                Left = 0
                Top = 0
                Width = 105
                Height = 13
                Caption = 'Desconto por letra'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object vBoxSeparadorDescontoPorLetra: TFVBox
                Left = 105
                Top = 0
                Width = 5
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object btnIncluirDescontoPorLetra: TFButton
                Left = 110
                Top = 0
                Width = 25
                Height = 25
                Hint = 'Incluir desconto por letra'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 1
                OnClick = btnIncluirDescontoPorLetraClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                  61000000524944415478DA6364C00D7E03310B94ED0FC49BB02962C463C07F24
                  762810AF193500B701C8A14D2AF06744B38954104A1503FC80980D8BE46A2476
                  1F101FC7A2E6E0E08885616C0072EC1C04E2D7D81401005B191F1C89729A2600
                  00000049454E44AE426082}
                ImageId = 7000128
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
            object FHBox17: TFHBox
              Left = 0
              Top = 31
              Width = 480
              Height = 5
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object grdDescontoLetra: TFGrid
              Left = 0
              Top = 37
              Width = 490
              Height = 140
              Hint = 'Desconto por letra'
              TabOrder = 2
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbClientesDescontosFlag
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              OnDoubleClick = grdDescontoLetraDoubleClick
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              ActionColumn.Title = 'A'#231#245'es'
              ActionColumn.Width = 100
              ActionColumn.TextAlign = taCenter
              ActionColumn.Visible = True
              Columns = <
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Alterar desconto por letra'
                      EvalType = etExpression
                      GUID = '{AFF49A70-7039-45B6-AFEC-0F8E5A46B77A}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7
                      OnClick = 'alterarDescontoPorLetra'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{AD65C213-AF1D-4304-8F77-495E3BC7E538}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Excluir desconto por letra'
                      EvalType = etExpression
                      GUID = '{9F06B83F-8620-47DB-AEDC-9A51C81DD380}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 700095
                      OnClick = 'excluirDescontoPorLetra'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{7BEF4800-7BF9-4358-AD46-B921B8CFC43B}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'LETRA'
                  Font = <>
                  Title.Caption = 'Letra'
                  Width = 50
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{2B84746F-14D6-4C9A-BD95-66389CB5B32C}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'DESCONTO'
                  Font = <>
                  Title.Caption = 'Desconto padr'#227'o'
                  Width = 150
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftDecimal
                  FlexRatio = 0
                  Sort = True
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{A55E5A7D-C568-4E27-A35D-47763CE8F804}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = ',##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{C708F690-D599-47E1-8EFA-A6734C3C9868}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Hint = 'Desconto padr'#227'o'
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'PER_DESC'
                  Font = <>
                  Title.Caption = 'Desconto para o cliente'
                  Width = 150
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftDecimal
                  FlexRatio = 0
                  Sort = True
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{BCD053DF-0616-4136-B970-C0161A0E095F}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = ',##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFDecimal
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.Mask = ',##0.00'
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.Prompt = 'Preencha o desconto para o cliente'
                  Editor.LookupColumns = <>
                  Editor.Enabled = True
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{CCF66E94-39A4-4383-A035-31A0C3A95B2E}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Hint = 'Desconto para o cliente'
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctEvent
                  EditorConstraint.FocusOnError = True
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = True
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end>
            end
          end
          object FHBox13: TFHBox
            Left = 505
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object vBoxVendaMargemMinimaMarkup: TFVBox
            Left = 510
            Top = 0
            Width = 490
            Height = 190
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hBoxMargemMinimaMarkupLinha01: TFHBox
              Left = 0
              Top = 0
              Width = 480
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblMargemMinimaMarkup: TFLabel
                Left = 0
                Top = 0
                Width = 139
                Height = 13
                Caption = 'Margem m'#237'nima markup'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object vBoxSeparadorMargemMinimaMarkup1: TFVBox
                Left = 139
                Top = 0
                Width = 5
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object btnIncluirMargemMinimaMarkup: TFButton
                Left = 144
                Top = 0
                Width = 25
                Height = 25
                Hint = 
                  'Incluir margem m'#237'nima markup (s'#243' '#233' permitido 1 registro por clie' +
                  'nte porque vale para todas as empresas)'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 1
                OnClick = btnIncluirMargemMinimaMarkupClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                  61000000524944415478DA6364C00D7E03310B94ED0FC49BB02962C463C07F24
                  762810AF193500B701C8A14D2AF06744B38954104A1503FC80980D8BE46A2476
                  1F101FC7A2E6E0E08885616C0072EC1C04E2D7D81401005B191F1C89729A2600
                  00000049454E44AE426082}
                ImageId = 7000128
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
            object FHBox18: TFHBox
              Left = 0
              Top = 31
              Width = 480
              Height = 5
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object grdMargemMinimaMarkup: TFGrid
              Left = 0
              Top = 37
              Width = 480
              Height = 140
              Hint = 'Margem m'#237'nima markup'
              TabOrder = 2
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbClientesEspeciaisMargem
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              OnDoubleClick = grdMargemMinimaMarkupDoubleClick
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = True
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              ActionColumn.Title = 'A'#231#245'es'
              ActionColumn.Width = 100
              ActionColumn.TextAlign = taCenter
              ActionColumn.Visible = True
              Columns = <
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Alterar margem m'#237'nima markup'
                      EvalType = etExpression
                      GUID = '{3907E166-5E24-4D2E-A39E-8A8810CAD2A0}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7
                      OnClick = 'alterarMargemMinimaMarkup'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{F1A707BE-21D2-4327-9E59-11074E5FD499}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Excluir margem m'#237'nima markup'
                      EvalType = etExpression
                      GUID = '{C38E22F5-34CC-40A4-BF59-325231399A14}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 700095
                      OnClick = 'excluirMargemMinimaMarkup'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{5506FEA6-AC72-4FDD-AB1F-D10B7C7283E4}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'MARGEM_MINIMA'
                  Font = <>
                  Title.Caption = 'Margem M'#237'nima'
                  Width = 100
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{E77EF1AB-FBCE-4884-94B2-48E3AED491C9}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = ',##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{B90F0ED5-9E9F-42CA-83E1-5F658F606E9D}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end>
            end
          end
          object FHBox15: TFHBox
            Left = 1000
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
        object FHBox9: TFHBox
          Left = 0
          Top = 274
          Width = 1000
          Height = 5
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object FHBox8: TFHBox
          Left = 0
          Top = 280
          Width = 1000
          Height = 200
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 5
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox20: TFHBox
            Left = 0
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object vBoxVendaDescontoPorLetraEmpresa: TFVBox
            Left = 5
            Top = 0
            Width = 500
            Height = 190
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftMin
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hBoxDecontoPorLetraEmpresaLinha01: TFHBox
              Left = 0
              Top = 0
              Width = 480
              Height = 31
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblDescontoLetraEmpresa: TFLabel
                Left = 0
                Top = 0
                Width = 161
                Height = 13
                Caption = 'Desconto por letra/empresa'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object vBoxSeparadorDescontoPorLetraEmpresa: TFVBox
                Left = 161
                Top = 0
                Width = 5
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object btnIncluirDescontoPorLetraEmpresa: TFButton
                Left = 166
                Top = 0
                Width = 25
                Height = 25
                Hint = 'Incluir desconto por letra/empresa'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 1
                OnClick = btnIncluirDescontoPorLetraEmpresaClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                  61000000524944415478DA6364C00D7E03310B94ED0FC49BB02962C463C07F24
                  762810AF193500B701C8A14D2AF06744B38954104A1503FC80980D8BE46A2476
                  1F101FC7A2E6E0E08885616C0072EC1C04E2D7D81401005B191F1C89729A2600
                  00000049454E44AE426082}
                ImageId = 7000128
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
            object FHBox24: TFHBox
              Left = 0
              Top = 32
              Width = 480
              Height = 5
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object grdDescontoLetraEmpresa: TFGrid
              Left = 0
              Top = 38
              Width = 490
              Height = 140
              Hint = 'Desconto por letra/empresa'
              TabOrder = 2
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbDescEmp
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              OnDoubleClick = grdDescontoLetraEmpresaDoubleClick
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = False
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              ActionColumn.Title = 'A'#231#245'es'
              ActionColumn.Width = 100
              ActionColumn.TextAlign = taCenter
              ActionColumn.Visible = True
              Columns = <
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Alterar desconto por letra/empresa'
                      EvalType = etExpression
                      GUID = '{5166C233-875C-43F7-ABD6-75CBF2D6933E}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7
                      OnClick = 'alterarDescontoPorLetraEmpresa'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{6A37DE60-29F6-49AE-9EB9-CF0982B6C88C}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Excluir desconto por letra/empresa'
                      EvalType = etExpression
                      GUID = '{695C4168-B051-46C6-B0C5-5DF714F6FD91}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 700095
                      OnClick = 'excluirDescontoPorLetraEmpresa'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{1CE70C77-AD91-4F22-BC02-B1479E06EDBE}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'LETRA'
                  Font = <>
                  Title.Caption = 'Letra'
                  Width = 50
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{F368D6E9-609D-4444-B093-83C1E357C814}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'DESCONTO_LETRA'
                  Font = <>
                  Title.Caption = 'Desconto padr'#227'o'
                  Width = 110
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftDecimal
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      Hint = 'Desconto padr'#227'o'
                      EvalType = etExpression
                      GUID = '{1FECCF68-778A-4C91-A12F-7F849E676B9B}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = ',##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFDecimal
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.Mask = ',##0.00'
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{E6486B3C-4C0E-4551-B832-07142800C506}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Hint = 'Desconto padr'#227'o'
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'PER_DESC'
                  Font = <>
                  Title.Caption = 'Desconto para o cliente'
                  Width = 140
                  Visible = True
                  Precision = 0
                  TextAlign = taRight
                  FieldType = ftDecimal
                  FlexRatio = 0
                  Sort = True
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      Hint = 'Desconto para o cliente'
                      EvalType = etExpression
                      GUID = '{DA6D4E93-846F-4F4D-99C7-A64C1B6CC862}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = ',##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFDecimal
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.Mask = ',##0.00'
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.Prompt = 'Preencha o desconto para o cliente'
                  Editor.LookupColumns = <>
                  Editor.Enabled = True
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{35096EFB-DCB5-4DC6-9962-397CB6D27E8F}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Hint = 'Desconto para o cliente'
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctEvent
                  EditorConstraint.FocusOnError = True
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = True
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'COD_EMPRESA'
                  Font = <>
                  Title.Caption = 'Empresa'
                  Width = 120
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.LookupDesc = 'NOMEECODIGODAEMPRESA'
                  Editor.LookupKey = 'CODIGODAEMPRESA'
                  Editor.LookupTable = tbEmpresasFiliaisSel
                  Editor.EditType = etTFCombo
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.Prompt = 'Selecione a empresa'
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{5A556FA9-5866-40EE-A1A2-DF1BA48E6E8A}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Hint = 'Empresa'
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end>
            end
          end
          object FHBox22: TFHBox
            Left = 505
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object vBoxVendaMargemMinimaMarkupEmpresa: TFVBox
            Left = 510
            Top = 0
            Width = 490
            Height = 190
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hBoxMargemMinimaMarkupEmpresaLinha01: TFHBox
              Left = 0
              Top = 0
              Width = 480
              Height = 30
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftMin
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblMargemMinimaMarkupEmpresa: TFLabel
                Left = 0
                Top = 0
                Width = 195
                Height = 13
                Caption = 'Margem m'#237'nima markup/empresa'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = [fsBold]
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FVBox2: TFVBox
                Left = 195
                Top = 0
                Width = 5
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object btnIncluirMargemMinimaMarkupEmpresa: TFButton
                Left = 200
                Top = 0
                Width = 21
                Height = 25
                Hint = 'Incluir margem m'#237'nima markup/empresa'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                TabOrder = 1
                OnClick = btnIncluirMargemMinimaMarkupEmpresaClick
                PngImage.Data = {
                  89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                  61000000524944415478DA6364C00D7E03310B94ED0FC49BB02962C463C07F24
                  762810AF193500B701C8A14D2AF06744B38954104A1503FC80980D8BE46A2476
                  1F101FC7A2E6E0E08885616C0072EC1C04E2D7D81401005B191F1C89729A2600
                  00000049454E44AE426082}
                ImageId = 7000128
                WOwner = FrInterno
                WOrigem = EhNone
                Color = clBtnFace
                Access = False
                IconReverseDirection = False
              end
            end
            object FHBox25: TFHBox
              Left = 0
              Top = 31
              Width = 480
              Height = 5
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object grdMargemMinimaMarkupEmpresa: TFGrid
              Left = 0
              Top = 37
              Width = 480
              Height = 140
              Hint = 'Margem m'#237'nima markup/empresa'
              TabOrder = 2
              TitleFont.Charset = DEFAULT_CHARSET
              TitleFont.Color = clWindowText
              TitleFont.Height = -11
              TitleFont.Name = 'Tahoma'
              TitleFont.Style = []
              Table = tbCliEspMargEmp
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Paging.Enabled = False
              Paging.PageSize = 0
              Paging.DbPaging = False
              FrozenColumns = 0
              ShowFooter = False
              ShowHeader = True
              MultiSelection = False
              Grouping.Enabled = False
              Grouping.Expanded = False
              Grouping.ShowFooter = False
              Crosstab.Enabled = False
              Crosstab.GroupType = cgtConcat
              OnDoubleClick = grdMargemMinimaMarkupEmpresaDoubleClick
              EnablePopup = False
              WOwner = FrInterno
              WOrigem = EhNone
              EditionEnabled = True
              AuxColumnHeaders = <>
              NoBorder = False
              ActionButtons.BtnAccept = False
              ActionButtons.BtnView = False
              ActionButtons.BtnEdit = False
              ActionButtons.BtnDelete = False
              ActionButtons.BtnInLineEdit = False
              CustomActionButtons = <>
              ActionColumn.Title = 'A'#231#245'es'
              ActionColumn.Width = 100
              ActionColumn.TextAlign = taCenter
              ActionColumn.Visible = True
              Columns = <
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Alterar margem m'#237'nima markup/empresa'
                      EvalType = etExpression
                      GUID = '{14920C84-DF1C-4E16-950E-1D55DEFD5420}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 7
                      OnClick = 'alterarMargemMinimaMarkupEmpresa'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{6C851EDB-C6FA-4793-98A5-B1463C4D0FBA}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  Font = <>
                  Width = 33
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <
                    item
                      Expression = '*'
                      Hint = 'Excluir margem m'#237'nima markup/empresa'
                      EvalType = etExpression
                      GUID = '{B93447E8-A824-4CE9-9850-3E6B3AFFB03D}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      ImageId = 700095
                      OnClick = 'excluirMargemMinimaMarkupEmpresa'
                      Color = clBlack
                    end>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{A1626EE2-C005-491C-BC5D-E4E093786DBB}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'EMPRESA_NOME_CODIGO'
                  Font = <>
                  Title.Caption = 'Empresa'
                  Width = 120
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = True
                  Colors = <>
                  Images = <>
                  Masks = <>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{1B051C64-A75B-4A34-8D3E-770436DBBE6A}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end
                item
                  Expanded = False
                  FieldName = 'MARGEM_MINIMA'
                  Font = <>
                  Title.Caption = 'Margem M'#237'nima'
                  Width = 100
                  Visible = True
                  Precision = 0
                  TextAlign = taLeft
                  FieldType = ftString
                  FlexRatio = 0
                  Sort = False
                  ImageHeader = 0
                  Wrap = False
                  Flex = False
                  Colors = <>
                  Images = <>
                  Masks = <
                    item
                      Expression = '*'
                      EvalType = etExpression
                      GUID = '{43236FCE-DA6F-46CA-9E12-D7D072B0E133}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Mask = ',##0.00'
                      PadLength = 0
                      PadDirection = pdNone
                      MaskType = mtDecimal
                    end>
                  CharCase = ccNormal
                  BlobConfig.MimeType = bmtText
                  BlobConfig.ShowType = btImageViewer
                  ShowLabel = True
                  Editor.EditType = etTFString
                  Editor.Precision = 0
                  Editor.Step = 0
                  Editor.MaxLength = 100
                  Editor.LookupFilterKey = 0
                  Editor.LookupFilterDesc = 0
                  Editor.PopupHeight = 400
                  Editor.PopupWidth = 400
                  Editor.CharCase = ccNormal
                  Editor.LookupColumns = <>
                  Editor.Enabled = False
                  Editor.ReadOnly = False
                  Editor.Filter = False
                  Editor.ShowClearButton = False
                  CheckedValue = 'S'
                  UncheckedValue = 'N'
                  HiperLink = False
                  GUID = '{1BDB8EA8-80DF-4154-A0D6-2A42B4E3BF5D}'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditorConstraint.CheckWhen = cwImmediate
                  EditorConstraint.CheckType = ctExpression
                  EditorConstraint.FocusOnError = False
                  EditorConstraint.EnableUI = True
                  EditorConstraint.Enabled = False
                  EditorConstraint.FormCheck = True
                  Empty = False
                  MobileOpts.ShowMobile = False
                  MobileOpts.Order = 0
                  BoxSize = 0
                  ImageSrcType = istSource
                  IconReverseDirection = False
                  FooterConfig.ColSpan = 0
                  FooterConfig.TextAlign = taLeft
                  FooterConfig.Enabled = False
                  HeaderTextAlign = taLeft
                  Priority = 0
                end>
            end
          end
          object FHBox39: TFHBox
            Left = 1000
            Top = 0
            Width = 5
            Height = 20
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
        object FHBox19: TFHBox
          Left = 0
          Top = 481
          Width = 1000
          Height = 5
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 6
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
    end
    object tabVenda: TFTabsheet
      Caption = 'Venda'
      Visible = True
      Closable = False
      WOwner = FrInterno
      WOrigem = EhNone
      object vBoxTabVenda: TFVBox
        Left = 0
        Top = 0
        Width = 1012
        Height = 537
        Align = alClient
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox26: TFHBox
          Left = 0
          Top = 0
          Width = 1000
          Height = 5
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object pgFlagsDiverso: TFPageControl
          Left = 0
          Top = 6
          Width = 1006
          Height = 429
          ActivePage = tabNegociacao
          TabOrder = 1
          TabPosition = tpTop
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          WOwner = FrInterno
          WOrigem = EhNone
          RenderStyle = rsTabbed
          object tabNegociacao: TFTabsheet
            Caption = 'Negocia'#231#227'o'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            Visible = True
            Closable = False
            WOwner = FrInterno
            WOrigem = EhNone
            object vBoxTabNegociacao: TFVBox
              Left = 0
              Top = 0
              Width = 998
              Height = 401
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 5
              Margin.Left = 5
              Margin.Right = 5
              Margin.Bottom = 5
              Spacing = 5
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox35: TFHBox
                Left = 0
                Top = 0
                Width = 1000
                Height = 5
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object vBoxPrecosPraticadosNaVenda: TFVBox
                Left = 0
                Top = 6
                Width = 260
                Height = 60
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel1: TFLabel
                  Left = 0
                  Top = 0
                  Width = 156
                  Height = 13
                  Caption = 'Pre'#231'os praticados na venda'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object chkUsaPrecoFabrica: TFCheckBox
                  Left = 0
                  Top = 14
                  Width = 110
                  Height = 21
                  Hint = 'Usa pre'#231'o f'#225'brica'
                  Caption = 'Usa pre'#231'o f'#225'brica'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  Table = tbClienteDiversoFlag
                  FieldName = 'CLIENTE_PRECO_FABRICA'
                  OnCheck = chkUsaPrecoFabricaCheck
                  HelpCaption = 'Usa pre'#231'o f'#225'brica'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
                object chkUsaPrecoGarantia: TFCheckBox
                  Left = 0
                  Top = 36
                  Width = 109
                  Height = 17
                  Hint = 'Usa pre'#231'o garantia'
                  Caption = 'Usa pre'#231'o garantia'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  Table = tbClienteDiversoFlag
                  FieldName = 'CLIENTE_GARANTIA'
                  OnCheck = chkUsaPrecoGarantiaCheck
                  HelpCaption = 'Usa pre'#231'o garantia'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
              object vBoxPrePedido: TFVBox
                Left = 0
                Top = 67
                Width = 260
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 5
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel2: TFLabel
                  Left = 0
                  Top = 0
                  Width = 62
                  Height = 13
                  Caption = 'Pr'#233'-Pedido'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object chkAprovaAuto: TFCheckBox
                  Left = 0
                  Top = 14
                  Width = 120
                  Height = 21
                  Hint = 'Aprova autom'#225'tico'
                  Caption = 'Aprova autom'#225'tico'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  Table = tbClienteDiversoFlag
                  FieldName = 'VP_AUTOMATICA'
                  OnCheck = chkAprovaAutoCheck
                  HelpCaption = 'Aprova autom'#225'tico'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
              object vBoxReservaDaPeca: TFVBox
                Left = 0
                Top = 108
                Width = 260
                Height = 50
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 5
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 3
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FLabel3: TFLabel
                  Left = 0
                  Top = 0
                  Width = 94
                  Height = 13
                  Caption = 'Reserva da Pe'#231'a'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object hBoxReservaDaPeca: TFHBox
                  Left = 0
                  Top = 14
                  Width = 250
                  Height = 30
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 5
                  Flex.Vflex = ftMin
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object chkReservaAuto: TFCheckBox
                    Left = 0
                    Top = 0
                    Width = 80
                    Height = 21
                    Hint = 'Autom'#225'tica'
                    Caption = 'Autom'#225'tica'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 0
                    Table = tbClienteDiversoFlag
                    FieldName = 'RESERVA_AUTOMATICA'
                    OnCheck = chkReservaAutoCheck
                    HelpCaption = 'Autom'#225'tica'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taAlignTop
                  end
                  object lblTempoAdicional: TFLabel
                    Left = 80
                    Top = 0
                    Width = 77
                    Height = 13
                    Caption = 'Tempo Adicional'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object intTempoReserva: TFInteger
                    Left = 157
                    Top = 0
                    Width = 39
                    Height = 24
                    Hint = 'Tempo Adicional (Horas)'
                    Table = tbClienteDiversoFlag
                    FieldName = 'TEMPO_RESERVA_ADICIONAL'
                    HelpCaption = 'Tempo Adicional (Horas)'
                    TabOrder = 0
                    AccessLevel = 0
                    Flex = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Required = False
                    Prompt = 'Tempo Adicional (Horas)'
                    Constraint.CheckWhen = cwImmediate
                    Constraint.CheckType = ctExpression
                    Constraint.FocusOnError = False
                    Constraint.EnableUI = True
                    Constraint.Enabled = False
                    Constraint.FormCheck = True
                    IconDirection = idLeft
                    Maxlength = 0
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -13
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    Alignment = taRightJustify
                    OnExit = intTempoReservaExit
                  end
                  object lblHoras: TFLabel
                    Left = 196
                    Top = 0
                    Width = 28
                    Height = 13
                    Caption = 'Horas'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
              end
              object FVBox8: TFVBox
                Left = 0
                Top = 159
                Width = 260
                Height = 40
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 4
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object lblClienteAtacado: TFLabel
                  Left = 0
                  Top = 0
                  Width = 89
                  Height = 13
                  Caption = 'Cliente Atacado'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object chkAtacadista: TFCheckBox
                  Left = 0
                  Top = 14
                  Width = 90
                  Height = 17
                  Hint = 'Atacadista'
                  Caption = 'Atacadista'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 0
                  Table = tbClienteDiversoFlag
                  FieldName = 'EH_ATACADISTA'
                  OnCheck = chkAtacadistaCheck
                  HelpCaption = 'Atacadista'
                  ReadOnly = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taAlignTop
                end
              end
            end
          end
          object tabVendedor: TFTabsheet
            Caption = 'Vendedor'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            Visible = True
            Closable = False
            WOwner = FrInterno
            WOrigem = EhNone
            ExplicitLeft = 0
            ExplicitTop = 0
            ExplicitWidth = 0
            ExplicitHeight = 0
            object vBoxVendedor: TFVBox
              Left = 0
              Top = 0
              Width = 998
              Height = 401
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox37: TFHBox
                Left = 0
                Top = 0
                Width = 1000
                Height = 140
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftMin
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox38: TFHBox
                  Left = 0
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox5: TFVBox
                  Left = 5
                  Top = 0
                  Width = 465
                  Height = 130
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox44: TFHBox
                    Left = 0
                    Top = 0
                    Width = 61
                    Height = 5
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxRepresentanteAssociadoAoCliente: TFVBox
                    Left = 0
                    Top = 6
                    Width = 250
                    Height = 50
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FLabel16: TFLabel
                      Left = 0
                      Top = 0
                      Width = 202
                      Height = 13
                      Hint = 'Representante associado ao cliente'
                      Caption = 'Representante associado ao cliente'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = [fsBold]
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object cboRepresentante: TFCombo
                      Left = 0
                      Top = 14
                      Width = 240
                      Height = 22
                      Hint = 'Representante associado ao cliente'
                      Table = tbClienteDiversoFlag
                      LookupTable = tbBuscaRepresentanteCliente
                      FieldName = 'REPRESENTANTE'
                      LookupKey = 'NOME'
                      LookupDesc = 'NOME_LOGIN'
                      Flex = True
                      HelpCaption = 'Representante associado ao cliente'
                      ReadOnly = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Prompt = 'Representante associado ao cliente'
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      ClearOnDelKey = True
                      UseClearButton = True
                      HideClearButtonOnNullValue = False
                      OnChange = cboRepresentanteChange
                      OnClearClick = cboRepresentanteClearClick
                      Colors = <>
                      Images = <>
                      Masks = <>
                      Fonts = <>
                      MultiSelection = False
                      IconReverseDirection = False
                    end
                  end
                  object FHBox46: TFHBox
                    Left = 0
                    Top = 57
                    Width = 61
                    Height = 5
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxVendedorAssociadoAoCliente: TFVBox
                    Left = 0
                    Top = 63
                    Width = 250
                    Height = 50
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 3
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FLabel15: TFLabel
                      Left = 0
                      Top = 0
                      Width = 171
                      Height = 13
                      Hint = 'Vendedor associado ao cliente'
                      Caption = 'Vendedor associado ao cliente'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = [fsBold]
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object cboVendedor: TFCombo
                      Left = 0
                      Top = 14
                      Width = 240
                      Height = 22
                      Hint = 'Vendedor associado ao cliente'
                      Table = tbClienteDiversoFlag
                      LookupTable = tbVendedorRespFlag
                      FieldName = 'VENDEDOR_RESPONSAVEL'
                      LookupKey = 'NOME'
                      LookupDesc = 'NOME_COMPLETO'
                      Flex = True
                      HelpCaption = 'Vendedor associado ao cliente'
                      ReadOnly = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Prompt = 'Vendedor associado ao cliente'
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      ClearOnDelKey = False
                      UseClearButton = True
                      HideClearButtonOnNullValue = False
                      OnChange = cboVendedorChange
                      OnExit = cboVendedorExit
                      OnClearClick = cboVendedorClearClick
                      Colors = <>
                      Images = <>
                      Masks = <>
                      Fonts = <>
                      MultiSelection = False
                      IconReverseDirection = False
                    end
                  end
                  object FHBox40: TFHBox
                    Left = 0
                    Top = 114
                    Width = 61
                    Height = 5
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 4
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
              end
              object FHBox42: TFHBox
                Left = 0
                Top = 141
                Width = 989
                Height = 252
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox48: TFHBox
                  Left = 0
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object FVBox6: TFVBox
                  Left = 5
                  Top = 0
                  Width = 946
                  Height = 247
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object FHBox50: TFHBox
                    Left = 0
                    Top = 0
                    Width = 655
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblVendedorTitulo: TFLabel
                      Left = 0
                      Top = 0
                      Width = 249
                      Height = 13
                      Caption = 'Vendedor associado ao cliente por empresa '
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = [fsBold]
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object FVBox7: TFVBox
                      Left = 249
                      Top = 0
                      Width = 5
                      Height = 20
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnVincularVendedor: TFButton
                      Left = 254
                      Top = 0
                      Width = 25
                      Height = 25
                      Hint = 'Vincular Vendedor'#13#10#13#10'Acesso "K1030"'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 1
                      OnClick = btnVincularVendedorClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000000394944415478DA63FC0F040C34048CA316906D01232323C98661336A
                        E02D202604F1A91DB560D482510B462D1812169002069705D40243DF02002B2B
                        B9B9B6418D210000000049454E44AE426082}
                      ImageId = 0
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconClass = 'external-link'
                      IconReverseDirection = False
                    end
                  end
                  object grdVendedoresAssociados: TFGrid
                    Left = 0
                    Top = 32
                    Width = 829
                    Height = 194
                    Align = alClient
                    TabOrder = 1
                    TitleFont.Charset = DEFAULT_CHARSET
                    TitleFont.Color = clWindowText
                    TitleFont.Height = -12
                    TitleFont.Name = 'Tahoma'
                    TitleFont.Style = []
                    Table = tbListaClienteResponsavel
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Paging.Enabled = False
                    Paging.PageSize = 0
                    Paging.DbPaging = False
                    FrozenColumns = 0
                    ShowFooter = False
                    ShowHeader = True
                    MultiSelection = False
                    Grouping.Enabled = False
                    Grouping.Expanded = False
                    Grouping.ShowFooter = False
                    Crosstab.Enabled = False
                    Crosstab.GroupType = cgtConcat
                    EnablePopup = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditionEnabled = False
                    AuxColumnHeaders = <>
                    NoBorder = False
                    ActionButtons.BtnAccept = False
                    ActionButtons.BtnView = False
                    ActionButtons.BtnEdit = False
                    ActionButtons.BtnDelete = False
                    ActionButtons.BtnInLineEdit = False
                    CustomActionButtons = <>
                    ActionColumn.Title = 'A'#231#245'es'
                    ActionColumn.Width = 100
                    ActionColumn.TextAlign = taCenter
                    ActionColumn.Visible = True
                    Columns = <
                      item
                        Expanded = False
                        FieldName = 'EMPRESAS_NOME_INITCAP_COD'
                        Font = <>
                        Title.Caption = 'Empresa'
                        Width = 200
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{FDEAA2BF-33E1-4251-840C-BF48E7B65EFB}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'COD_CLIENTE'
                        Font = <>
                        Title.Caption = 'C'#243'd. Cliente'
                        Width = 136
                        Visible = False
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{404B1841-62CC-466E-A9E3-4404A08271D6}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'SISTEMA'
                        Font = <>
                        Title.Caption = 'Sistema'
                        Width = 91
                        Visible = False
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{917CC0A4-89CB-4513-A0BE-69627676CF8F}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'PROCESSO'
                        Font = <>
                        Title.Caption = 'Processo'
                        Width = 92
                        Visible = False
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{A5D8B753-E4ED-47CC-BEB2-6DBCCD6A6846}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'TEMPERATURA'
                        Font = <>
                        Title.Caption = 'Temperatura'
                        Width = 108
                        Visible = False
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{158A6EDE-BFB9-4E0E-BCE7-94D624CC2770}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'SISTEMA_STR'
                        Font = <>
                        Title.Caption = 'Sistema'
                        Width = 85
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{3E150DE3-F14F-4AB3-A0C3-9C6AB2D8132A}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'PROCESSO_STR'
                        Font = <>
                        Title.Caption = 'Processo'
                        Width = 132
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{7DBDB539-5817-4AEA-B22D-195934D6161B}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'TEMPERATURA_STR'
                        Font = <>
                        Title.Caption = 'Temperatura'
                        Width = 118
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{D51ADCBB-E498-42CB-913F-AD373FC0C0B2}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'RESPONSAVEL'
                        Font = <>
                        Title.Caption = 'Responsavel'
                        Width = 86
                        Visible = False
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{26637729-D8B6-4E7B-BF91-7845C096F2EC}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'VENDEDOR'
                        Font = <>
                        Title.Caption = 'Vendedor'
                        Width = 230
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = True
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{7440ECDF-4875-4010-A22A-5C32732490BB}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end>
                  end
                  object FHBox32: TFHBox
                    Left = 0
                    Top = 227
                    Width = 185
                    Height = 17
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object FHBox51: TFHBox
                  Left = 951
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
          object tabTransportadora: TFTabsheet
            Caption = 'Transportadora'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            Visible = True
            Closable = False
            WOwner = FrInterno
            WOrigem = EhNone
            ExplicitLeft = 0
            ExplicitTop = 0
            ExplicitWidth = 0
            ExplicitHeight = 0
            object vBoxTransportadora: TFVBox
              Left = 0
              Top = 0
              Width = 998
              Height = 401
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hBoxVendaLinha02: TFHBox
                Left = 0
                Top = 0
                Width = 989
                Height = 378
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox45: TFHBox
                  Left = 0
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object vBoxVendaTransportadora: TFVBox
                  Left = 5
                  Top = 0
                  Width = 946
                  Height = 370
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object hBoxTransportadoraLinha01: TFHBox
                    Left = 0
                    Top = 0
                    Width = 655
                    Height = 31
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FLabel7: TFLabel
                      Left = 0
                      Top = 0
                      Width = 89
                      Height = 13
                      Caption = 'Transportadora'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = [fsBold]
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object FVBox4: TFVBox
                      Left = 89
                      Top = 0
                      Width = 5
                      Height = 20
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnIncluirTransportadora: TFButton
                      Left = 94
                      Top = 0
                      Width = 25
                      Height = 25
                      Hint = 'Incluir transportadora'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 1
                      OnClick = btnIncluirTransportadoraClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                        61000000524944415478DA6364C00D7E03310B94ED0FC49BB02962C463C07F24
                        762810AF193500B701C8A14D2AF06744B38954104A1503FC80980D8BE46A2476
                        1F101FC7A2E6E0E08885616C0072EC1C04E2D7D81401005B191F1C89729A2600
                        00000049454E44AE426082}
                      ImageId = 7000128
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconReverseDirection = False
                    end
                  end
                  object grdClienteTransp: TFGrid
                    Left = 0
                    Top = 32
                    Width = 760
                    Height = 288
                    Hint = 'Transportadora'
                    TabOrder = 1
                    TitleFont.Charset = DEFAULT_CHARSET
                    TitleFont.Color = clWindowText
                    TitleFont.Height = -12
                    TitleFont.Name = 'Tahoma'
                    TitleFont.Style = []
                    Table = tbClienteTransportadoraFlag
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftFalse
                    Paging.Enabled = False
                    Paging.PageSize = 0
                    Paging.DbPaging = False
                    FrozenColumns = 0
                    ShowFooter = False
                    ShowHeader = True
                    MultiSelection = False
                    Grouping.Enabled = False
                    Grouping.Expanded = False
                    Grouping.ShowFooter = False
                    Crosstab.Enabled = False
                    Crosstab.GroupType = cgtConcat
                    EnablePopup = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditionEnabled = True
                    AuxColumnHeaders = <>
                    NoBorder = False
                    ActionButtons.BtnAccept = False
                    ActionButtons.BtnView = False
                    ActionButtons.BtnEdit = False
                    ActionButtons.BtnDelete = False
                    ActionButtons.BtnInLineEdit = False
                    CustomActionButtons = <>
                    ActionColumn.Title = 'A'#231#245'es'
                    ActionColumn.Width = 100
                    ActionColumn.TextAlign = taCenter
                    ActionColumn.Visible = True
                    Columns = <
                      item
                        Expanded = False
                        Font = <>
                        Title.Caption = ' '
                        Width = 33
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <
                          item
                            Expression = '*'
                            Hint = 'Excluir transportadora do cliente'
                            EvalType = etExpression
                            GUID = '{C1490879-55E3-41A7-9C6B-A892AD3D15BC}'
                            WOwner = FrInterno
                            WOrigem = EhNone
                            ImageId = 700095
                            OnClick = 'excluirTransportadora'
                            Color = clBlack
                          end>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = False
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{DF743A81-014F-4754-9E0D-************}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'DESCRICAO_COD_TRANSPORTADORA'
                        Font = <>
                        Title.Caption = 'Nome transportadora'
                        Width = 406
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{C24FFB89-33D1-4CE3-8626-054B27F1CB17}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'CPF_CGC'
                        Font = <>
                        Title.Caption = 'CNPJ/CPF'
                        Width = 193
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = True
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{6D8176BA-8233-4534-8CE8-30945ABC8D4F}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end>
                  end
                  object FHBox49: TFHBox
                    Left = 0
                    Top = 321
                    Width = 185
                    Height = 17
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object FHBox47: TFHBox
                  Left = 951
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
        end
      end
    end
    object tabFinanceiro: TFTabsheet
      Caption = 'Financeiro'
      Visible = True
      Closable = False
      WOwner = FrInterno
      WOrigem = EhNone
      ExplicitLeft = 0
      ExplicitTop = 0
      ExplicitWidth = 0
      ExplicitHeight = 0
      object vBoxFinanceiro: TFVBox
        Left = 0
        Top = 0
        Width = 1012
        Height = 537
        Align = alClient
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 3
        Padding.Right = 3
        Padding.Bottom = 10
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object pgFinanceiro: TFPageControl
          Left = 0
          Top = 0
          Width = 1000
          Height = 520
          ActivePage = tabFormaCondicaoPagamento
          Align = alClient
          TabOrder = 0
          TabPosition = tpTop
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          WOwner = FrInterno
          WOrigem = EhNone
          RenderStyle = rsTabbed
          object tabFormaCondicaoPagamento: TFTabsheet
            Caption = 'Forma/Condi'#231#227'o de pagamento'
            Visible = True
            Closable = False
            WOwner = FrInterno
            WOrigem = EhNone
            ExplicitLeft = 0
            ExplicitTop = 0
            ExplicitWidth = 0
            ExplicitHeight = 0
            object vBoxFormaCondicaoPagamento: TFVBox
              Left = 0
              Top = 0
              Width = 992
              Height = 492
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox29: TFHBox
                Left = 0
                Top = 0
                Width = 980
                Height = 5
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object hBoxFaturamentoLivreLinha01: TFHBox
                Left = 0
                Top = 6
                Width = 980
                Height = 470
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox52: TFHBox
                  Left = 0
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object vBoxFinanceiroLinha01Coluna02: TFVBox
                  Left = 5
                  Top = 0
                  Width = 960
                  Height = 460
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 4
                  Padding.Right = 4
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object vBoxFormaCobranca: TFVBox
                    Left = 0
                    Top = 0
                    Width = 950
                    Height = 50
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftMin
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object FLabel6: TFLabel
                      Left = 0
                      Top = 0
                      Width = 114
                      Height = 13
                      Caption = '  Forma de cobran'#231'a'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = [fsBold]
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object cboFormaCob: TFCombo
                      Left = 0
                      Top = 14
                      Width = 940
                      Height = 21
                      Hint = 'Forma de cobran'#231'a'
                      Table = tbClienteDiversoFlag
                      LookupTable = tbFormaCobrancaFlag
                      FieldName = 'CHAVE_COB'
                      LookupKey = 'CHAVE_COB'
                      LookupDesc = 'DESCRICAO'
                      Flex = True
                      HelpCaption = 'Forma de cobran'#231'a'
                      ReadOnly = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Prompt = 'Forma de cobran'#231'a'
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      ClearOnDelKey = False
                      UseClearButton = True
                      HideClearButtonOnNullValue = False
                      OnChange = cboFormaCobChange
                      OnExit = cboFormaCobExit
                      OnClearClick = cboFormaCobClearClick
                      Colors = <>
                      Images = <>
                      Masks = <>
                      Fonts = <>
                      MultiSelection = False
                      IconReverseDirection = False
                    end
                  end
                  object FHBox4: TFHBox
                    Left = 0
                    Top = 51
                    Width = 950
                    Height = 5
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxFormaPgto: TFVBox
                    Left = 0
                    Top = 57
                    Width = 950
                    Height = 160
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object hBoxCaptionFormaPgto: TFHBox
                      Left = 0
                      Top = 0
                      Width = 940
                      Height = 25
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object lblFormasDePagamento: TFLabel
                        Left = 0
                        Top = 0
                        Width = 133
                        Height = 13
                        Align = alLeft
                        Caption = '  Formas de pagamento'
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = [fsBold]
                        ParentFont = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        VerticalAlignment = taAlignBottom
                        WordBreak = False
                        MaskType = mtText
                      end
                      object FVBox1: TFVBox
                        Left = 133
                        Top = 0
                        Width = 5
                        Height = 17
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        FlowStyle = fsTopBottomLeftRight
                        Padding.Top = 0
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                      end
                      object btnAddFormaPgto: TFButton
                        Left = 138
                        Top = 0
                        Width = 22
                        Height = 22
                        Hint = 'Incluir forma de pagameto'
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = []
                        ParentFont = False
                        TabOrder = 1
                        OnClick = btnAddFormaPgtoClick
                        PngImage.Data = {
                          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                          61000000524944415478DA6364C00D7E03310B94ED0FC49BB02962C463C07F24
                          762810AF193500B701C8A14D2AF06744B38954104A1503FC80980D8BE46A2476
                          1F101FC7A2E6E0E08885616C0072EC1C04E2D7D81401005B191F1C89729A2600
                          00000049454E44AE426082}
                        ImageId = 7000128
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Color = clBtnFace
                        Access = False
                        IconReverseDirection = False
                      end
                    end
                    object FHBox12: TFHBox
                      Left = 0
                      Top = 26
                      Width = 940
                      Height = 5
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object grdFormasDePagamento: TFGrid
                      Left = 0
                      Top = 32
                      Width = 940
                      Height = 120
                      Hint = 'Formas de pagamento'
                      TabOrder = 2
                      TitleFont.Charset = DEFAULT_CHARSET
                      TitleFont.Color = clWindowText
                      TitleFont.Height = -11
                      TitleFont.Name = 'Tahoma'
                      TitleFont.Style = []
                      Table = tbListaClienteFormasPagamento
                      Flex.Vflex = ftTrue
                      Flex.Hflex = ftTrue
                      Paging.Enabled = False
                      Paging.PageSize = 0
                      Paging.DbPaging = False
                      FrozenColumns = 0
                      ShowFooter = False
                      ShowHeader = True
                      MultiSelection = False
                      Grouping.Enabled = False
                      Grouping.Expanded = False
                      Grouping.ShowFooter = False
                      Crosstab.Enabled = False
                      Crosstab.GroupType = cgtConcat
                      OnDoubleClick = grdFormasDePagamentoDoubleClick
                      EnablePopup = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditionEnabled = False
                      AuxColumnHeaders = <>
                      NoBorder = False
                      ActionButtons.BtnAccept = False
                      ActionButtons.BtnView = False
                      ActionButtons.BtnEdit = False
                      ActionButtons.BtnDelete = False
                      ActionButtons.BtnInLineEdit = False
                      CustomActionButtons = <>
                      ActionColumn.Title = 'A'#231#245'es'
                      ActionColumn.Width = 100
                      ActionColumn.TextAlign = taCenter
                      ActionColumn.Visible = True
                      Columns = <
                        item
                          Expanded = False
                          Font = <>
                          Width = 30
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = 'FORMA_PGTO_CODIGO IS NULL'
                              Hint = 'Incluir forma de pagamento'
                              EvalType = etExpression
                              GUID = '{06DDBBC0-48C8-4B7F-A033-EC747CA0CBF0}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 7000128
                              Color = clBlack
                            end
                            item
                              Expression = 'FORMA_PGTO_CODIGO > 0'
                              Hint = 'Excluir forma de pagamento'
                              EvalType = etExpression
                              GUID = '{06E8D2FB-41EC-44ED-8891-4EE59AFA6BD5}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 700095
                              OnClick = 'excluirFormaDePagamento'
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{1ED24D87-C3B0-4394-BE5B-3378473CDC65}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Selecionar'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'EMPRESA_NOME_CODIGO'
                          Font = <>
                          Title.Caption = 'Empresa'
                          Width = 200
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = True
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{B01AEAD6-FD01-45DF-B140-745D6055014F}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Empresa'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'DEPARTAMENTO_DESCRICAO_CODIGO'
                          Font = <>
                          Title.Caption = 'Departamento'
                          Width = 200
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{A11199B8-C1ED-4754-B03A-00DA72094074}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Departamento'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'FORMA_PGTO_DESCRICAO_CODIGO'
                          Font = <>
                          Title.Caption = 'Forma de pagamento'
                          Width = 200
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{8760494F-8C1A-40EA-A4AF-B68CDEDD28FB}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Forma de pagamento'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'FORMA_PGTO_EXCLUSIVA'
                          Font = <>
                          Title.Caption = 'Forma exclusiva'
                          Width = 130
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = 'FORMA_PGTO_EXCLUSIVA = '#39'S'#39
                              Hint = 'Sim'
                              EvalType = etExpression
                              GUID = '{BC58C1D3-2B46-4DDE-BC58-07F2AE3A4D3B}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 4300107
                              Color = clBlack
                            end
                            item
                              Expression = 'FORMA_PGTO_EXCLUSIVA = '#39'N'#39
                              Hint = 'N'#227'o'
                              EvalType = etExpression
                              GUID = '{E0108922-37DC-4E04-A6E7-5FB26D116CC5}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 4300106
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = False
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{B8037925-71D4-4FB8-8C23-EE70A8263041}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Forma exclusiva'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end>
                    end
                  end
                  object FHBox7: TFHBox
                    Left = 0
                    Top = 218
                    Width = 950
                    Height = 5
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 3
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxCondicaoPagamento: TFVBox
                    Left = 0
                    Top = 224
                    Width = 950
                    Height = 210
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 4
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object hBoxCaptionCondPgto: TFHBox
                      Left = 0
                      Top = 0
                      Width = 940
                      Height = 27
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                      object lblCondicoesPgto: TFLabel
                        Left = 0
                        Top = 0
                        Width = 148
                        Height = 13
                        Caption = '  Condi'#231#245'es de pagamento'
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = [fsBold]
                        ParentFont = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        VerticalAlignment = taAlignBottom
                        WordBreak = False
                        MaskType = mtText
                      end
                      object FVBox3: TFVBox
                        Left = 148
                        Top = 0
                        Width = 5
                        Height = 23
                        AutoWrap = False
                        BevelKind = bkTile
                        BevelOuter = bvNone
                        BorderStyle = stNone
                        Caption = ' '
                        FlowStyle = fsTopBottomLeftRight
                        Padding.Top = 0
                        Padding.Left = 0
                        Padding.Right = 0
                        Padding.Bottom = 0
                        TabOrder = 0
                        Margin.Top = 0
                        Margin.Left = 0
                        Margin.Right = 0
                        Margin.Bottom = 0
                        Spacing = 1
                        Flex.Vflex = ftFalse
                        Flex.Hflex = ftFalse
                        Scrollable = False
                        WOwner = FrInterno
                        WOrigem = EhNone
                        BoxShadowConfig.HorizontalLength = 10
                        BoxShadowConfig.VerticalLength = 10
                        BoxShadowConfig.BlurRadius = 5
                        BoxShadowConfig.SpreadRadius = 0
                        BoxShadowConfig.ShadowColor = clBlack
                        BoxShadowConfig.Opacity = 75
                        BorderRadius.TopLeft = 0
                        BorderRadius.TopRight = 0
                        BorderRadius.BottomRight = 0
                        BorderRadius.BottomLeft = 0
                      end
                      object btnAddCondicaoPgto: TFButton
                        Left = 153
                        Top = 0
                        Width = 22
                        Height = 22
                        Hint = 'Incluir condi'#231#227'o de pagameto'
                        Font.Charset = DEFAULT_CHARSET
                        Font.Color = clWindowText
                        Font.Height = -11
                        Font.Name = 'Tahoma'
                        Font.Style = []
                        ParentFont = False
                        TabOrder = 1
                        OnClick = btnAddCondicaoPgtoClick
                        PngImage.Data = {
                          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                          61000000524944415478DA6364C00D7E03310B94ED0FC49BB02962C463C07F24
                          762810AF193500B701C8A14D2AF06744B38954104A1503FC80980D8BE46A2476
                          1F101FC7A2E6E0E08885616C0072EC1C04E2D7D81401005B191F1C89729A2600
                          00000049454E44AE426082}
                        ImageId = 7000128
                        WOwner = FrInterno
                        WOrigem = EhNone
                        Color = clBtnFace
                        Access = False
                        IconReverseDirection = False
                      end
                    end
                    object FHBox14: TFHBox
                      Left = 0
                      Top = 28
                      Width = 940
                      Height = 5
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 1
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      VAlign = tvTop
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object grdCondPgto: TFGrid
                      Left = 0
                      Top = 34
                      Width = 940
                      Height = 160
                      Hint = 'Condi'#231#245'es de pagamento'
                      TabOrder = 2
                      TitleFont.Charset = DEFAULT_CHARSET
                      TitleFont.Color = clWindowText
                      TitleFont.Height = -11
                      TitleFont.Name = 'Tahoma'
                      TitleFont.Style = []
                      Table = tbClienteFormaPgtoFlag
                      Flex.Vflex = ftTrue
                      Flex.Hflex = ftTrue
                      Paging.Enabled = False
                      Paging.PageSize = 0
                      Paging.DbPaging = False
                      FrozenColumns = 0
                      ShowFooter = False
                      ShowHeader = True
                      MultiSelection = False
                      Grouping.Enabled = False
                      Grouping.Expanded = False
                      Grouping.ShowFooter = False
                      Crosstab.Enabled = False
                      Crosstab.GroupType = cgtConcat
                      OnDoubleClick = grdCondPgtoDoubleClick
                      EnablePopup = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditionEnabled = False
                      AuxColumnHeaders = <>
                      NoBorder = False
                      ActionButtons.BtnAccept = False
                      ActionButtons.BtnView = False
                      ActionButtons.BtnEdit = False
                      ActionButtons.BtnDelete = False
                      ActionButtons.BtnInLineEdit = False
                      CustomActionButtons = <>
                      ActionColumn.Title = 'A'#231#245'es'
                      ActionColumn.Width = 100
                      ActionColumn.TextAlign = taCenter
                      ActionColumn.Visible = True
                      Columns = <
                        item
                          Expanded = False
                          Font = <>
                          Width = 30
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = 'COD_FORMA_PGTO IS NULL'
                              Hint = 'Incluir condi'#231#227'o de pagamento'
                              EvalType = etExpression
                              GUID = '{036D1457-9E76-4C24-AF5D-BDB1BA13E675}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 7000128
                              Color = clBlack
                            end
                            item
                              Expression = 'COD_FORMA_PGTO > 0'
                              Hint = 'Excluir condi'#231#227'o de pagamento'
                              EvalType = etExpression
                              GUID = '{2FC33FDC-8DA3-4666-B394-62A6900CC9AC}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 700095
                              OnClick = 'excluirCondicaoDePagamento'
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{F5DC280F-5E52-4DE5-8A12-373E061EDEEF}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Selecionar'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'NOME_CODIGO_EMPRESA'
                          Font = <>
                          Title.Caption = 'Empresa'
                          Width = 200
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = True
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{EB09A6EA-D322-436F-B2CF-DF7BAC338430}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Empresa'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'NOME_CODIGO_DEPARTAMENTO'
                          Font = <>
                          Title.Caption = 'Departamento'
                          Width = 200
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{F400CC9E-D2D2-48EA-8CC1-ADFE8366C73D}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Departamento'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'DESCRICAO_CODIGO_FORMA_PGTO'
                          Font = <>
                          Title.Caption = 'Forma de pagamento'
                          Width = 200
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{E4970F90-46B4-4DA6-90CB-C6BE050E1098}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Forma de pagamento'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'DESCRICAO_CODIGO_CONDICAO_PGTO'
                          Font = <>
                          Title.Caption = 'Condi'#231#227'o de pagamento'
                          Width = 200
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{F7E80FA0-336D-46B2-91B6-962E1794DF15}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Condi'#231#227'o de pagamento'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end
                        item
                          Expanded = False
                          FieldName = 'CONDICAO_PGTO_EXCLUSIVA'
                          Font = <>
                          Title.Caption = 'Condi'#231#227'o exclusiva'
                          Width = 140
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = 'CONDICAO_PGTO_EXCLUSIVA = '#39'S'#39
                              Hint = 'Sim'
                              EvalType = etExpression
                              GUID = '{5BDBB92F-2ED7-4371-A300-C4F0570E3A9F}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 4300107
                              Color = clBlack
                            end
                            item
                              Expression = 'CONDICAO_PGTO_EXCLUSIVA = '#39'N'#39
                              Hint = 'N'#227'o'
                              EvalType = etExpression
                              GUID = '{9E5FEE61-E1B0-420A-816B-D3E1CE90DCB2}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 4300106
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = False
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          Editor.Filter = False
                          Editor.ShowClearButton = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{D9F5E10A-0990-4FC8-871A-7D25602C97A1}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          Hint = 'Condi'#231#227'o exclusiva'
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                          Priority = 0
                        end>
                    end
                  end
                  object FHBox33: TFHBox
                    Left = 0
                    Top = 435
                    Width = 950
                    Height = 5
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 5
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object FHBox31: TFHBox
                  Left = 965
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
          object tabFaturamentoLivre: TFTabsheet
            Caption = 'Faturamento livre'
            Visible = True
            Closable = False
            WOwner = FrInterno
            WOrigem = EhNone
            ExplicitLeft = 0
            ExplicitTop = 0
            ExplicitWidth = 0
            ExplicitHeight = 0
            object vBoxFaturamentoLivre: TFVBox
              Left = 0
              Top = 0
              Width = 992
              Height = 492
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FHBox16: TFHBox
                Left = 0
                Top = 0
                Width = 980
                Height = 5
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object hBoxFaturamentoLivreLinha001: TFHBox
                Left = 0
                Top = 6
                Width = 980
                Height = 470
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object FHBox21: TFHBox
                  Left = 0
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object vBoxFaturamentoLivre02: TFVBox
                  Left = 5
                  Top = 0
                  Width = 960
                  Height = 460
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object chkFatLivre: TFCheckBox
                    Left = 0
                    Top = 0
                    Width = 257
                    Height = 17
                    Hint = 'Faturamento livre (todas empresas)'
                    Caption = 'Faturamento livre (todas empresas)'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = [fsBold]
                    ParentFont = False
                    TabOrder = 0
                    Table = tbClienteDiversoFlag
                    FieldName = 'FATURAMENTO_LIVRE'
                    OnCheck = chkFatLivreCheck
                    HelpCaption = 'Faturamento livre (todas empresas)'
                    ReadOnly = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taAlignTop
                  end
                  object grdClienteFatLivre: TFGrid
                    Left = 0
                    Top = 18
                    Width = 950
                    Height = 430
                    Hint = 'Faturamento livre (todas empresas)'
                    TabOrder = 1
                    TitleFont.Charset = DEFAULT_CHARSET
                    TitleFont.Color = clWindowText
                    TitleFont.Height = -11
                    TitleFont.Name = 'Tahoma'
                    TitleFont.Style = []
                    Table = tbClientesParamEmpFlag
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Paging.Enabled = False
                    Paging.PageSize = 0
                    Paging.DbPaging = False
                    FrozenColumns = 0
                    ShowFooter = False
                    ShowHeader = True
                    MultiSelection = False
                    Grouping.Enabled = False
                    Grouping.Expanded = False
                    Grouping.ShowFooter = False
                    Crosstab.Enabled = False
                    Crosstab.GroupType = cgtConcat
                    EnablePopup = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditionEnabled = False
                    AuxColumnHeaders = <>
                    NoBorder = False
                    ActionButtons.BtnAccept = False
                    ActionButtons.BtnView = False
                    ActionButtons.BtnEdit = False
                    ActionButtons.BtnDelete = False
                    ActionButtons.BtnInLineEdit = False
                    CustomActionButtons = <>
                    ActionColumn.Title = 'A'#231#245'es'
                    ActionColumn.Width = 100
                    ActionColumn.TextAlign = taCenter
                    ActionColumn.Visible = True
                    Columns = <
                      item
                        Expanded = False
                        FieldName = 'COD_EMPRESA'
                        Font = <>
                        Title.Caption = 'CE'
                        Width = 30
                        Visible = True
                        Precision = 0
                        TextAlign = taRight
                        FieldType = ftInteger
                        FlexRatio = 0
                        Sort = True
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{48AB9D09-1B3B-42AE-A4AB-550DA83D22F3}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'NOME'
                        Font = <>
                        Title.Caption = 'Empresa'
                        Width = 120
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = True
                        ImageHeader = 0
                        Wrap = False
                        Flex = True
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{2BFECDC4-B3F2-4440-8065-B5C61D0A2446}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        FieldName = 'CGC'
                        Font = <>
                        Title.Caption = 'CNPJ'
                        Width = 140
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = True
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{4110F8D4-2804-4A98-9C86-5D9B6373D7B4}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end
                      item
                        Expanded = False
                        Font = <>
                        Title.Caption = 'Fat. Livre'
                        Width = 60
                        Visible = True
                        Precision = 0
                        TextAlign = taLeft
                        FieldType = ftString
                        FlexRatio = 0
                        Sort = False
                        ImageHeader = 0
                        Wrap = False
                        Flex = False
                        Colors = <>
                        Images = <
                          item
                            Expression = 'FATURAMENTO_LIVRE ='#39'N'#39
                            EvalType = etExpression
                            GUID = '{E588119C-947F-4120-8337-A9B5BFA9544C}'
                            WOwner = FrInterno
                            WOrigem = EhNone
                            ImageId = 310011
                            OnClick = 'setFaturamentoLivre'
                            Color = clBlack
                          end
                          item
                            Expression = 'FATURAMENTO_LIVRE='#39'S'#39
                            EvalType = etExpression
                            GUID = '{C0EDAA99-BD90-4EA8-9CB8-4E065A86223E}'
                            WOwner = FrInterno
                            WOrigem = EhNone
                            ImageId = 310010
                            OnClick = 'setFaturamentoLivre'
                            Color = clBlack
                          end>
                        Masks = <>
                        CharCase = ccNormal
                        BlobConfig.MimeType = bmtText
                        BlobConfig.ShowType = btImageViewer
                        ShowLabel = True
                        Editor.EditType = etTFString
                        Editor.Precision = 0
                        Editor.Step = 0
                        Editor.MaxLength = 100
                        Editor.LookupFilterKey = 0
                        Editor.LookupFilterDesc = 0
                        Editor.PopupHeight = 400
                        Editor.PopupWidth = 400
                        Editor.CharCase = ccNormal
                        Editor.LookupColumns = <>
                        Editor.Enabled = False
                        Editor.ReadOnly = False
                        Editor.Filter = False
                        Editor.ShowClearButton = False
                        CheckedValue = 'S'
                        UncheckedValue = 'N'
                        HiperLink = False
                        GUID = '{03338C10-21FE-4236-97D4-EB740E1C8163}'
                        WOwner = FrInterno
                        WOrigem = EhNone
                        EditorConstraint.CheckWhen = cwImmediate
                        EditorConstraint.CheckType = ctExpression
                        EditorConstraint.FocusOnError = False
                        EditorConstraint.EnableUI = True
                        EditorConstraint.Enabled = False
                        EditorConstraint.FormCheck = True
                        Empty = False
                        MobileOpts.ShowMobile = False
                        MobileOpts.Order = 0
                        BoxSize = 0
                        ImageSrcType = istSource
                        IconReverseDirection = False
                        FooterConfig.ColSpan = 0
                        FooterConfig.TextAlign = taLeft
                        FooterConfig.Enabled = False
                        HeaderTextAlign = taLeft
                        Priority = 0
                      end>
                  end
                end
                object FHBox23: TFHBox
                  Left = 965
                  Top = 0
                  Width = 5
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
        end
      end
    end
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbClienteDiversoFlag
        GUID = '{05F57241-1AEA-40AF-B2E1-036C755378AF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClienteDiversoFlag: TFTable
    FieldDefs = <
      item
        Name = 'FATURAMENTO_LIVRE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Faturamento Livre'
        GUID = '{80629BC3-F894-4553-AF71-82E597092E50}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_PRECO_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Pre'#231'o Fabrica'
        GUID = '{88FD7E0C-67A4-4BA3-B685-6DA878D093E2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Garantia'
        GUID = '{2D386679-9BE1-45BF-BC1C-A594C52201AC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VP_AUTOMATICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vp Automatica'
        GUID = '{D9876E18-0D60-41F8-B78A-66141AC92556}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_AUTOMATICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Reserva Automatica'
        GUID = '{2F7F4312-B235-465E-898E-EDD17D03B637}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RESERVA_ADICIONAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Reserva Adicional'
        GUID = '{11FC158A-EC8F-4B03-9EFE-864298ADB2BF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_FC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Fc'
        GUID = '{E1374CB2-DCC5-4292-B5EA-629B6682E253}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Cobranca'
        GUID = '{A72259DC-E670-45E6-ACA5-5DB01D688DD7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{A06B0F50-6EB3-4293-A036-A68EE6E6CF86}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cgc'
        GUID = '{C2E3AA62-9D74-414A-8846-36C697FCC017}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_COB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Cob'
        GUID = '{2436E779-0F25-4B38-8859-832D2784E578}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{E3015980-8606-4FAE-B55E-5F3AF2EA1E6A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDEDOR_RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vendedor Respons'#225'vel'
        GUID = '{FBCA627E-B2C4-4A97-B706-336EEB99F105}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CURVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Curva'
        GUID = '{6F054BA6-B0A2-443C-ADE1-A86718CF2327}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REPRESENTANTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Representante'
        GUID = '{1BE75823-FFB3-4B3D-A340-47AE84CE6F81}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_ATACADISTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Atacadista'
        GUID = '{3B66462D-B091-4480-8B66-DC56323C63D4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_DIVERSO'
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31006'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesParamEmpFlag: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{CB4BA068-8348-44A2-AB23-8AEAB427F10A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{93AE5766-72AB-4D75-A1DB-F168DBA34299}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc'
        GUID = '{8D30D578-2DBA-47DF-927E-264C5AFF979F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FATURAMENTO_LIVRE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Faturamento Livre'
        GUID = '{55EBAC70-2161-45DD-BFE9-B90CAE9C5250}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CLIENTES_PARAM_EMP_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFormaCobrancaFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{12DE78BC-AE1B-4CB7-98AC-4A7FD2D0A8A5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Cobranca'
        GUID = '{4F4AB4AA-D713-4988-B4CA-4F88B53AE20A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{0FB89642-CC3D-4B1A-B1F2-E33DD0BE96EF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_COB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Cob'
        GUID = '{9572D90E-9B78-4580-A2A4-2837A25A55B0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_COBRANCA_DESCR_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Cobranca Descri'#231#227'o Codigo'
        GUID = '{76CF8517-58FC-407A-96A4-B8ECFE2118A8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'FORMA_COBRANCA_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Top = 2
  end
  object tbClienteTransportadoraFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_TRANSPORTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Transportadora'
        GUID = '{B062B5C5-0C13-47B7-872A-321C72E52C9B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{069EA7D3-3D9E-4C25-A725-338CEB73A45A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cgc'
        GUID = '{2411AEEB-5D98-4C4F-B676-AB0085F85EE2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{FAE81383-5463-4D2C-B96C-47556243CD7E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Initcap'
        GUID = '{0D8123A3-BD5E-4E92-ADD9-81560E2BA763}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_COD_TRANSPORTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o C'#243'd. Transportadora'
        GUID = '{89E739F2-74A3-4206-B006-B45EDE71F07F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_TRANSPORTADORA'
    Cursor = 'CLIENTE_TRANSPORTADORA_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31009'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Top = 2
  end
  object tbTransportadorasFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_TRANSPORTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Transportadora'
        GUID = '{87369C71-4938-4091-9CC1-20BFD26F4475}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{B5F27CC6-BA97-4275-A3FA-0AEF73AEA61C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cgc'
        GUID = '{3C41F0CA-C930-43BC-AE9A-4AB323103903}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'TRANSPORTADORAS_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 2
  end
  object tbClienteFormaPgtoFlag: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{8B628834-3D85-4053-B255-7E51CA9CFA5B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{BE59B555-727A-4F6E-AFF7-71CD59141177}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        GUID = '{6B25F551-9A9B-4ED7-B110-2685C112F749}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        GUID = '{FE11F489-5661-4440-83F7-F494FFA88779}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Departamento'
        GUID = '{834B1568-A3DF-4A8F-B0D6-37F31E640258}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        GUID = '{80FA4890-295F-45FC-AE36-35F8FF1DD926}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        GUID = '{EE7C2516-889F-449D-8667-6CF524E651CB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento'
        GUID = '{A6A3204E-8FB7-4CBE-A06D-9B2247FFE21F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CODIGO_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Codigo Empresa'
        GUID = '{E521FE3B-9909-44F2-8794-D6295F044B2C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CODIGO_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Codigo Departamento'
        GUID = '{50C84BC9-A45F-4C47-A8F0-598DAFC1B525}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo Forma Pagamento'
        GUID = '{B9629DA3-319F-4C50-887E-8ADD5BB12DDC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO_CONDICAO_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo Condi'#231#227'o Pagamento'
        GUID = '{1B25B967-7F70-4D83-BB32-7D1DE35650CB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Exclusiva'
        GUID = '{5DA058E7-127A-4E98-8C42-C870FAA01E37}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento Exclusiva'
        GUID = '{60CA608B-57C3-4BC5-BE92-F6BE887E6D08}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMA_PGTO_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesDescontosFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{7C59F42A-3584-4860-880E-0D28DB2CD1F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Letra'
        GUID = '{B7CB9003-27B8-4313-8AED-F3612C9F1F9D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PER_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Per Desconto'
        GUID = '{928BAB32-B3C4-44C6-B51A-D121909D2D6A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{C92F3F4F-A5F7-43CC-B144-7EC0FAB99087}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_DESCONTOS'
    Cursor = 'CLIENTES_DESCONTOS_FLAG'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensPerDescFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_MAX_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Max Desconto'
        GUID = '{D10FFFAD-4095-4BFC-87E0-8D2E694AEB22}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{48D3E024-F033-4DA2-B9F1-7204642AB7A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ITENS_PER_DESC_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310016'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbVendedorRespFlag: TFTable
    FieldDefs = <
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{F09EF7F1-AF33-4611-8C77-6FA8E3010E03}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{3564B660-4965-49CD-B92B-BC831F22F5E9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAB_PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tab Pre'#231'o'
        GUID = '{138FD4AB-1C7B-441B-8837-D4ED9673D6E0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'VENDEDOR_RESP_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310017'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosJuridicosFlag: TFTable
    FieldDefs = <
      item
        Name = 'ID_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Segmento'
        GUID = '{03140CB1-8070-48C4-AFDD-695828CDFDA7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{84856C68-1D8D-4D20-A3EC-0B6A1A2A0135}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{3D286008-EB91-4ADA-A2E8-30027F800028}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGMENTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Segmento Descri'#231#227'o Codigo'
        GUID = '{CFEB0FAB-CE04-49D6-86BA-BA8C4C9F588D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DADOS_JURIDICOS'
    Cursor = 'DADOS_JURIDICOS_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310018'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbBuscaRepresentanteCliente: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{74445757-96D6-483F-9766-D803F71AC749}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Login'
        GUID = '{E6FF6DAA-AC2F-4D7B-9C5A-54A5A001398B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_REPRESENTANTE_CLIENTE'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaClienteFormasPagamento: TFTable
    FieldDefs = <
      item
        Name = 'FORMA_PGTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        GUID = '{CF0C74B9-8694-4024-91FC-7FA4CBFABF00}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Descri'#231#227'o Codigo'
        GUID = '{40D58BBF-A803-4301-96CE-1BA3C73FCCAF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{909EC7A1-AEDE-481A-91FD-48396024322F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        GUID = '{4D9A4BE9-4628-4B97-926A-333B51E83FD7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        GUID = '{A0990F9F-EBBF-4E4C-AAED-A868E4FB0018}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Departamento Descri'#231#227'o Codigo'
        GUID = '{E44BA548-9E75-4260-897E-146CA59BAA1D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{1C2112E8-37AC-45CE-B1E5-CEB39D01C28A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Exclusiva'
        GUID = '{5E91BB81-B8BA-485A-BC4A-D2C90ABE7043}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_CLIENTE_FORMAS_PAGAMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Top = 2
  end
  object tbClienteFormasPgto: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{C31F5003-5A37-47AD-88D6-A2163E8E8F7B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        GUID = '{D2DA0D22-F09E-44AB-A720-B6AF0F647CC2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        GUID = '{6A091B5A-FE48-4B5F-97CE-D941987DB8EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{A466044C-3A7A-4757-A4A8-475599DE6D59}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        GUID = '{1FD9F457-8EA7-4866-ACA9-97A9EF73CC8A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMAS_PGTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{493260F6-8593-4811-B68B-AF8500631856}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Letra'
        GUID = '{DE606748-6DC5-4C83-8AAD-EFFD602BD197}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{509F78B9-0FC8-42C0-B935-BB5F70226E06}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PER_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Per Desconto'
        GUID = '{F4C069F1-C367-4A90-B3DA-E44C9C4FB00C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        GUID = '{5D9F2731-EAB3-4983-9CAC-412ED3410C50}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{BC6C3C4C-DFDF-42DC-8DD6-95CE33D2CDAD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_DESCONTOS_EMPRESA'
    Cursor = 'CLI_DESC_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFiliaisSel: TFTable
    FieldDefs = <
      item
        Name = 'CODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{3D5C5DF8-A4E5-4D4E-A85D-6BA4B71867BF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOMEECODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nomeecodigodaempresa'
        GUID = '{32ECEF11-DFC8-4F5C-AFFA-0EE0E58E7237}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESA'
    Cursor = 'EMPRESAS_FILIAIS_SEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;530012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesEspeciaisMargem: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{6E26A8D3-AF8E-46EE-85FA-FC23D2142352}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        GUID = '{3A6782C3-F161-45A1-891B-B41341F14128}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_ESPECIAIS_MARGEM'
    Cursor = 'CLIENTES_ESPECIAIS_MARGEM'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;530013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCliEspMargEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{AD8B2B63-29AB-4A2F-83CE-F92D477BF797}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{9E7E3680-47A4-4E79-A9E9-19A0021EE568}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        GUID = '{E919FB4E-4F1A-4D17-93CD-C1A19883393D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        GUID = '{EE017090-FDBC-4624-B28B-953994D62B72}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_ESPECIAIS_MARGEM_EMP'
    Cursor = 'BUSCA_CLI_ESP_MARG_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;161014'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaClienteResponsavel: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{33C9FA37-ACB6-4697-AA16-3AEA36C2097B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{FA2D1BB5-8F16-400E-AF34-AD641728E265}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SISTEMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Sistema'
        GUID = '{A4C5419F-7E1B-4D34-A25E-D766592F3A58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROCESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Processo'
        GUID = '{BBA4DA53-B854-49C0-8E6C-171F96EB69E3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPERATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Temperatura'
        GUID = '{AE3BCC06-F294-4D6B-8907-B69E79C91046}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel'
        GUID = '{3621DCA5-583C-4EF3-9CA1-F10944953F4A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SISTEMA_STR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sistema Str'
        GUID = '{700E2B48-38B2-4F14-BBA9-DA2243B9315A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROCESSO_STR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Processo Str'
        GUID = '{9DFC2B00-15A1-4B61-A418-6EF8B0E020A2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPERATURA_STR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Temperatura Str'
        GUID = '{399E4F85-DCCC-4BCC-B2C8-EB680F4F0EF4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vendedor'
        GUID = '{5FB73D3B-7BB2-4209-BF54-AB1E874846AE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESAS_NOME_INITCAP_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresas Nome Initcap C'#243'd.'
        GUID = '{575DFBBC-9476-465F-ADE9-862A5C4B8055}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_CLIENTE_RESPONSAVEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;28401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteSegmento: TFTable
    FieldDefs = <
      item
        Name = 'ID_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Segmento'
        GUID = '{92D69161-D3EB-46FC-A0FA-AB4D18E03268}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{*************-4FB6-843D-EAB5B58E3F90}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGMENTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Segmento Descri'#231#227'o Codigo'
        GUID = '{2462B7EF-1D93-4DCF-856B-4A76551C7A4B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_SEGMENTO'
    Cursor = 'BUSCA_CLIENTE_SEGMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;507015'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
