object AlterarDadosEmailRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '430090'
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbEmpresasUsuarios
        GUID = '{8C4529D0-2A46-4678-9F10-59E558968361}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_SENHA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Email Senha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        Caption = 'Email'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'EMPRESAS_USUARIOS'
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '430090;43001'
    DeltaMode = dmAll
    RatioBatchSize = 20
  end
end
