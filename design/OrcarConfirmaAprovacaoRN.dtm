object OrcarConfirmaAprovacaoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600559'
  Height = 299
  Width = 442
  object tbOrcarOsAbertasVeic: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Emiss'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_ATUAL_VEICULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Atual Ve'#237'culo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORCAR_OS_ABERTAS_VEIC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600559;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcarOrcamentos: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Emiss'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENCERRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Encerrada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXTENDIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Extendida'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGURADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seguradora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CADASTRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cadastro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ORCAR_ORCAMENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600559;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServOrc: TFTable
    FieldDefs = <
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_SERV_ORC'
    Cursor = 'OS_SERV_ORC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600559;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOrcamentosItens: TFTable
    FieldDefs = <
      item
        Name = 'COD_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_ORCAMENTOS_ITENS'
    Cursor = 'OS_ORCAMENTOS_ITENS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600559;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
