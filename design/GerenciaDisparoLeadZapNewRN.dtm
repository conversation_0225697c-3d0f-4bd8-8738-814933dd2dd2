object GerenciaDisparoLeadZapNewRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600268'
  Height = 299
  Width = 442
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Estado'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600268;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbQueryGraficoDisparoLz200: TFTable
    FieldDefs = <
      item
        Name = 'ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tot'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'QUERY_GRAFICO_DISPARO_LZ'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600268;46009'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbQueryGraficoDisparoLz201: TFTable
    FieldDefs = <
      item
        Name = 'ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tot'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'QUERY_GRAFICO_DISPARO_LZ'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600268;460010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbQueryGraficoDisparoLz202: TFTable
    FieldDefs = <
      item
        Name = 'ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tot'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'QUERY_GRAFICO_DISPARO_LZ'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600268;460011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbQueryGraficoDisparoLz208: TFTable
    FieldDefs = <
      item
        Name = 'ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tot'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'QUERY_GRAFICO_DISPARO_LZ'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600268;460013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbQueryTotalDisparosLz: TFTable
    FieldDefs = <
      item
        Name = 'TOTAL_DISPAROS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Disparos'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_CONTATOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Contatos'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO_ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Contato'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_SUCESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Total Sucesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SUCESSO_ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sucesso Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERC_SUCESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Perc Sucesso'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'QUERY_TOTAL_DISPAROS_LZ'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600268;460012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
end
