object FrmOficinaManutAlteraTempoTrab: TFForm
  Left = 50
  Top = 50
  ActiveControl = FVBox1
  Caption = 'Alterar Tempo Trabalhado'
  ClientHeight = 257
  ClientWidth = 449
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600633'
  ShortcutKeys = <>
  InterfaceRN = 'OficinaManutAlteraTempoTrabRN'
  Access = False
  ChangedProp = 
    'FrmOficinaManutAlteraTempoTrab.Width;'#13#10#13#10'FrmOficinaManutAlteraTe' +
    'mpoTrab.ActiveControlFrmOficinaManutAlteraTempoTrab.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox1: TFVBox
    Left = 0
    Top = 0
    Width = 449
    Height = 257
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitWidth = 434
    object hBoxBtnTopo: TFHBox
      Left = 0
      Top = 0
      Width = 420
      Height = 65
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 3
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Voltar'
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 59
        Hint = 'Aceitar'
        Caption = 'Aceitar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D494844520000001E0000001E08060000003B30AE
          A20000050A4944415478DAB5976B4C5C4514C7FF73F7FD2ECF428552406C8B25
          D894B6A056B4155F4841FDA4C4A8A9B104134DAD49DDA67C6915F143435A039A
          DA0F1634FD52A36D6309222628102C2DC48A292D48A1506C80E5B1BBECB28F7B
          AFE72E65C302CB2E154F3259EECC30BF73E6CC39738689A2885072B899A9E8E7
          796A4FABE4BA6CADDC98A6511875D298D363B53BBCD65E9777BA8D3E7FA65657
          FEB8E80AB5265B0E4CC058FA3910A18E7F2FD1F8B03656BB011EC125C1E0F4DA
          7D7334723D3523649C1C63CE410C5ABB1C1333774FD250252930B26230414BF4
          8AC8CACDD1BBD4A2C863D0F617461D03104461E9851843B42611098674524281
          EEB19619BB67FC00C1BF0C0B4C400D449C5D6FDA5218AB4DC68DF116D8DCE321
          DD315F740A131E8ACC81C53984DB535DE7C1F02A29E00C0A26A816027EDD189D
          B3CDC53BD03FF5C78A800B25C1B0195A52E2E658DB55707882E08E4560823282
          3624ADC9D863F74CF8B45D0D8950C7C1A08CC6EDC9AE4682E7115C0C0437B123
          51FA84633226C788A37F55A07312A35DEF3B1B16FB505979AEF8B11F4CD62671
          90F5ADD5A570FF4CF7AC2A744EE274A91899EE1704F02964F5C02CF8177621CA
          F840C1A47B04BCE0F95FC072991C26C55A58AC772E96EF16F732F36F8860222C
          7A5514B3B92DAB0E5431231ED117A3D35E03B542039B6B4C1419A298B9018754
          3A4D854770931FF85585A6AB8A90F7E0517CD3FD0A2C7C0F3826838253C235ED
          FC88997F428752A7DEEA1666560D6812135090F4393625BE88AFAFBC805E5783
          7F4CC9A9E19E9EE964E646583995CC2073ABC0CBC96AE6BD6F201338641B4AF1
          CC964FA0521871E9CF0FD03C55193087631C04976063E626F0F4370791C37391
          9FC1C15B7079E2149CB29565AB783E132FA59F4242CC0EDF7747DF199C1B7E73
          B172D428B204E970F953974C50E2ADB43A24C664A3B3AF06ADC3273122BB3E3B
          3B88283C5AEC8E29C3AEF483E028474B3238DA86AF6E3C092F825F5292C522D9
          EB17356FC23B194D888BCC947443CF503D5A064EA0C7530F513E2FAFD35D91C6
          9E45516635228C29FE6E9B6318559DDB611587836B2B48E046F050CE4703467E
          1D4AB6B5628D3EC9DF373AD98DD69B27D039550B25D3213FB91299A9AF05ACE7
          E56770AA3D1743DECBCBFB453A4AE63A58A18761E1588CB009FB773643AB8A0A
          E877BA667DAF51452E5AEFDCD537D0E1AC097D20ECA0C3F53D3A108DAD4B8D27
          E131ECCB69A0ACA309B95673F7715C1AFB30345492315038D5E21036A022D89C
          7459218AB3BFA38B5E16749D9E3BF538732B9F5C176602EA0725902A44208574
          D007FA79BEEC50EF4751D6928504E5DE1E545FDB092726C283DA49BF3E44CF5E
          12DFB20BB4AF05CBCDCF331DC5531965017D2EF714BE68CFC188783D3CA82403
          B8585E4C97840F5CCD929088BFC9F6E0FB4921F072FC6964A5EDF37D8AD451FB
          FB5E747B7E0C1F3A41BE18446A79E9BD6B5192C3A7D911DAF263145A4185E3E5
          783DE5076C4CCC47FD35339AAC156112218510688BCBCADF9E5708F8C0D554FA
          68D08064EC813CF8FF2B791D1E35BD8F26FBA76475E89ADC2752FABF85463A08
          79646D60E9730FAE257813F93B6B39CB572492A503B842D05C822E2EF6E6C135
          50E02CD6A110A6FF089DA2368CF3F050795BBA4C79BB408112CA67C711076917
          562612E22E1CB0E12001C32BE817C07D4F18E8F02E596FA058A75B24C864A98E
          B0FBACB4611A55909E30A5F7F18459A080FFD14669663BF93F8D026F36BFF394
          EBDDE8A5E86AC7DCA3AD34F4A3ED5F176212D08960780E0000000049454E44AE
          426082}
        ImageId = 4600188
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object FVBox2: TFVBox
      Left = 0
      Top = 66
      Width = 440
      Height = 177
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 2
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox1: TFHBox
        Left = 0
        Top = 0
        Width = 437
        Height = 109
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox3: TFVBox
          Left = 0
          Top = 0
          Width = 50
          Height = 103
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FHBox2: TFHBox
            Left = 0
            Top = 0
            Width = 50
            Height = 46
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 27
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel1: TFLabel
              Left = 0
              Top = 0
              Width = 38
              Height = 13
              Caption = 'Entrada'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
          object FHBox3: TFHBox
            Left = 0
            Top = 47
            Width = 50
            Height = 41
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 20
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FLabel2: TFLabel
              Left = 0
              Top = 0
              Width = 26
              Height = 13
              Caption = 'Sa'#237'da'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
              MaskType = mtText
            end
          end
        end
        object FVBox4: TFVBox
          Left = 50
          Top = 0
          Width = 146
          Height = 103
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 3
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FLabel3: TFLabel
            Left = 0
            Top = 0
            Width = 23
            Height = 13
            Caption = 'Data'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDataEntrada: TFDate
            Left = 0
            Top = 14
            Width = 135
            Height = 24
            Table = tbTemposExecutados
            FieldName = 'DATA_ENTRADA'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Format = 'dd/MM/yyyy'
            ShowCheckBox = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            OnExit = edtDataEntradaExit
            ShowTime = False
          end
          object edtDataSaida: TFDate
            Left = 0
            Top = 39
            Width = 135
            Height = 24
            Table = tbTemposExecutados
            FieldName = 'DATA_SAIDA'
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Format = 'dd/MM/yyyy'
            ShowCheckBox = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            OnExit = edtDataSaidaExit
            ShowTime = False
          end
        end
        object FVBox5: TFVBox
          Left = 196
          Top = 0
          Width = 93
          Height = 100
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 3
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FLabel4: TFLabel
            Left = 0
            Top = 0
            Width = 23
            Height = 13
            Caption = 'Hora'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtHoraEntrada: TFTime
            Left = 0
            Top = 14
            Width = 92
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = True
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = False
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Format = 'HH:mm'
            Align = alLeft
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            OnExit = edtHoraEntradaExit
            HourFormat = hf24
            TimeSeparator = ':'
            ShowSeconds = False
            StepHour = 1
            StepMinute = 1
            StepSecond = 1
          end
          object edtHoraSaida: TFTime
            Left = 0
            Top = 39
            Width = 92
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = True
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = False
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Format = 'HH:mm'
            Align = alLeft
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            OnExit = edtHoraSaidaExit
            HourFormat = hf24
            TimeSeparator = ':'
            ShowSeconds = False
            StepHour = 1
            StepMinute = 1
            StepSecond = 1
          end
        end
        object FVBox6: TFVBox
          Left = 289
          Top = 0
          Width = 40
          Height = 103
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 45
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxIconReflesh: TFVBox
            Left = 0
            Top = 0
            Width = 43
            Height = 34
            Align = alLeft
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 5
            Padding.Left = 11
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            OnClick = vBoxIconRefleshClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object iconReflesh: TFImage
              Left = 0
              Top = 0
              Width = 16
              Height = 18
              Align = alClient
              Stretch = False
              OnClick = iconRefleshClick
              ImageSrc = '/images/crmservice220020.png'
              WOwner = FrInterno
              WOrigem = EhNone
              BoxSize = 0
              GrayScaleOnDisable = False
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              Preview = False
            end
          end
        end
        object FVBox7: TFVBox
          Left = 329
          Top = 0
          Width = 105
          Height = 102
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 20
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FLabel5: TFLabel
            Left = 0
            Top = 0
            Width = 89
            Height = 13
            Caption = 'Tempo Trabalhado'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object FLabel6: TFLabel
            Left = 0
            Top = 14
            Width = 36
            Height = 13
            Caption = '(Horas)'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtTempoTrabalhado: TFDecimal
            Left = 0
            Top = 28
            Width = 93
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = False
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Maxlength = 0
            Precision = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            Alignment = taRightJustify
            Mode = dmDecimal
          end
        end
      end
      object FHBox4: TFHBox
        Left = 0
        Top = 110
        Width = 417
        Height = 61
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FHBox5: TFHBox
          Left = 0
          Top = 0
          Width = 50
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 6
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FLabel7: TFLabel
            Left = 0
            Top = 0
            Width = 32
            Height = 13
            Caption = 'Motivo'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
        end
        object edtMotivo: TFString
          Left = 50
          Top = 0
          Width = 121
          Height = 24
          TabOrder = 0
          AccessLevel = 0
          Flex = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          IconDirection = idLeft
          CharCase = ccNormal
          Pwd = False
          Maxlength = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          SaveLiteralCharacter = False
          TextAlign = taLeft
        end
      end
    end
  end
  object tbTemposExecutados: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TECNICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tecnico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Entrada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Entrada'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Sa'#237'da'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Sa'#237'da'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PAGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Pago'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALTERADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Alterado'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_TEMPOS_EXECUTADOS'
    Cursor = 'OS_TEMPOS_EXECUTADOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600633;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConflitoOsTemposExecutados: TFTable
    FieldDefs = <>
    Cursor = 'CONFLITO_OS_TEMPOS_EXECUTADOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600633;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbServicos: TFTable
    FieldDefs = <
      item
        Name = 'ALIQ_COFINS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Cofins Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_COFINS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Cofins Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_CSLL_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Csll Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_ISS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Iss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_ISS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Iss Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_PIS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Pis'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_PIS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Pis Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_PIS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Pis Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_COFINS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Cofins Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_COFINS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Cofins Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_CSLL_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Csll Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_ISS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Iss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_ISS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Iss Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_PIS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Pis'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_PIS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Pis Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_PIS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Pis Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CAMPANHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Campanha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONTROLE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Controle'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_COMPRA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Compra'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EXTERNO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Externo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FAIXA_DESC_BMW'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Faixa Desconto Bmw'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GWM_CAUSA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Gwm Causa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MOTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Motivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_RETORNO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Retorno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMO_COBRAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Como Cobrar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTADOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Contador'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CST_COFINS_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cst Cofins Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CST_PIS_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cst Pis Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_TERCEIRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Terceiro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEFEITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Defeito'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_ISS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Iss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_SERV_DES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Servi'#231'o Des'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_RATEADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Rateado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DT_INCLUSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Inclus'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HASH_DAV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hash Dav'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HASH_DAV_PV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hash Dav Pv'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HASH_DAV_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hash Dav Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_JOB_BMW_ISPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Job Bmw Ispa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOG_TRIBUTOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Log Tributos'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NFE_NUMR_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Nfe Numr Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_CUPOM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Cupom'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_REVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Revis'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_ALIQ_COFINS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Al'#237'quota Cofins'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_ALIQ_CSLL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Al'#237'quota Csll'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_ALIQ_IRRF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Al'#237'quota Irrf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_ALIQ_PIS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Al'#237'quota Pis'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_BASE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Base'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_VALOR_COFINS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Valor Cofins'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_VALOR_CSLL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Valor Csll'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_VALOR_IRRF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Valor Irrf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OP_VALOR_PIS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Op Valor Pis'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OUTROS_ACRESCIMOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Outros Acrescimos'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERCENTUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Percentual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRE_AUTORIZACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pr'#233' Autoriza'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_CONVERSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Convers'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_CUSTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Custo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_LIQUIDO_FINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o L'#237'quido Final'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_ORIG'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Orig'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_TOTAL_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Total Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_ALTEROU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Alterou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_LIBEROU_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Liberou Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ_NUM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq N'#250'mero'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ_PAF_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq Paf Item'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA_CUPOM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia Cupom'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA_DAV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia Dav'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO_NAO_TRIBUTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o N'#227'o Tributado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_DAV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Dav'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_PAF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Paf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_RETORNO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Retorno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_MMC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Mmc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTALIZADOR_ECF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Totalizador Ecf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRIBUTACAO_DAV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tributa'#231#227'o Dav'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ut'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_COFINS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Cofins Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_COFINS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Cofins Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_CSLL_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Csll Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESCONTO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desconto Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_HORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Hora'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_ISS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Iss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_ISS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Iss Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_JUROS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Juros'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PIS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Pis'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PIS_RETIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Pis Retido'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_PIS_SERVICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Pis Servi'#231'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VLR_TOT_TRIB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Vlr Tot Trib'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TESTADOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Testador'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO_OLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o Old'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_FUNILARIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Funilaria'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PRODUTIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Produtivo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_APV_QUALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Apv Qualidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_APV_QUALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Apv Qualidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_REP_QUALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Rep Qualidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_REP_QUALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Rep Qualidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_QUALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Qualidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENT_ETAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Ent Etapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBEROU_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberou Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_SERV_LIB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Servi'#231'o Lib'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CCC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Ccc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VLR_TOT_TRIB_FEDERAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Vlr Tot Trib Federal'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VLR_TOT_TRIB_MUNICIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Vlr Tot Trib Municipal'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_RETORNO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Retorno'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_IRRF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Irrf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_IRRF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Irrf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_IRRF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Irrf'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_DIAG'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Diag'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_DTC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Dtc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SEQ_XENTRY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Seq Xentry'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BASE_INSS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Base Inss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_INSS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Inss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_INSS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Inss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO_AUX'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o Aux'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRECO_VENDA_ORI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Pre'#231'o Venda Ori'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_RATEADO_ORI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Rateado Ori'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESCONTO_SERV_ORI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desconto Servi'#231'o Ori'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_POR_SERV_ORI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Por Servi'#231'o Ori'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOTAL_LIQUIDO_ORI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Total L'#237'quido Ori'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO_ORI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o Ori'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TERCEIROS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Terceiros'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RETEM_INSS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Retem Inss'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTROLE_REQ_SERV_TERC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Controle Req Servi'#231'o Terc'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SOLICITACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Solicita'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_APROVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Aprova'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_AUTORIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Autorizado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Garantia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVICO_AGRUPADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Servi'#231'o Agrupado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ITEM_AGRUPADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Item Agrupado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_ADICIONAL_STARCLASS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Adicional Starclass'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_ADICIONAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Adicional'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_ADICIONAL_OBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Adicional Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BOX_AGENDADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Box Agendado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTD_IMPRESSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Impress'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AGRUPADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Agrupado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_SERVICO_B'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Servi'#231'o B'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SERVICO_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Servi'#231'o Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS_SERVICOS'
    Cursor = 'OS_SERVICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600633;10301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
