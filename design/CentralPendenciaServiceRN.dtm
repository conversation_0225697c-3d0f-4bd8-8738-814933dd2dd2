object CentralPendenciaServiceRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600480'
  Height = 299
  Width = 442
  object tbConsultaComboEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaPendenciaOrcGrid: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'St'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DDD_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ddd Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS_ORIGINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os Original'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESEJADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desejado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_FIM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VALOR_APROVADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Mostrar Valor Aprovado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIXAR_MANUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Baixar Manual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL_ESPECIFICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel Especifico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_ESPECIFICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o Especifica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_REQUISITOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Requisitou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_QUEM_REQUISITOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_PENDENCIA_ORC_GRID'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbPendenciaEvento
        GUID = '{CC0E75AE-3FCA-4C71-9938-F34FF55BF2AB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbPendenciaEvento: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_REQUISITOU'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Requisitou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_ENCERROU'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Encerrou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INICIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data In'#237'cio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_FIM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_INICIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o In'#237'cio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_FIM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESEJADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desejado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_APROVADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Aprovado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_PENDENCIA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_PENDENCIA_EVENTO'
    TableName = 'CRM_PENDENCIA_EVENTO'
    Cursor = 'CRM_PENDENCIA_EVENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46004'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsResponsavel: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_RESPONSAVEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
