object PesquisaFormaPgtoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '5300550'
  Left = 321
  Top = 162
  Height = 299
  Width = 442
  object tbListaFormasPgto: TFTable
    FieldDefs = <
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMP_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMP_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_TIPO_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Tipo Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_EXCLUSIVA_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Exclusiva Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_TIPO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Tipo Descri'#231#227'o Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_FORMAS_PGTO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300550;53001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFiliaisSel: TFTable
    FieldDefs = <
      item
        Name = 'CODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOMEECODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nomeecodigodaempresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESA'
    Cursor = 'EMPRESAS_FILIAIS_SEL'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300550;53002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaEmpresasDepartamentos: TFTable
    FieldDefs = <
      item
        Name = 'EMP_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dpto Descri'#231#227'o Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_EMPRESAS_DEPARTAMENTOS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300550;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaFormasPgto1: TFTable
    FieldDefs = <
      item
        Name = 'EMP_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_DESCRICAO_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Descri'#231#227'o C'#243'd.'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_FORMAS_PGTO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300550;53004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaTipoPgtoNbs: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO_PGTO_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Pagamento Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_COD_TIPO_PGTO_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto C'#243'd. Tipo Pagamento Nbs'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'TIPO_PGTO_NBS'
    Cursor = 'LISTA_TIPO_PGTO_NBS'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '5300550;53005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
