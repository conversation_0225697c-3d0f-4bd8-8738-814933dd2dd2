object FrmFrameChatQR: TFForm
  Left = 321
  Top = 163
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Chat QRCode'
  ClientHeight = 369
  ClientWidth = 395
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '513017'
  ShortcutKeys = <>
  InterfaceRN = 'FrameChatQRRN'
  Access = False
  ChangedProp = 'FrmFrameChatQR.Width;'#13#10'FrmFrameChatQR.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object frameChat: TFFrame
    Left = 0
    Top = 0
    Width = 395
    Height = 369
    Align = alClient
    AutoWrap = False
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    TabOrder = 0
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    WOwner = FrInterno
    WOrigem = EhNone
  end
  object tbCadastroWhatsapp: TFTable
    FieldDefs = <
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        GUID = '{13541061-D035-42B2-8D22-4F9252F7CD8B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{26C75170-EB3C-476B-AD9F-679D2CFB9B7A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{9368FEFF-DC5E-4509-8EDC-09BB939169A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        GUID = '{8EF7EDBF-53B7-45D4-8D74-93BDCF848731}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Time'
        GUID = '{693F8C08-354A-403A-9E46-6085F11C9A13}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Evento'
        GUID = '{C8D22DFA-78F9-4CD2-9382-1ED64136D418}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        GUID = '{AFCCB9B0-80C1-4D58-8B9C-2E0A7434773B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERESSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Interesse'
        GUID = '{F8E10B1E-0278-4C63-A1E1-B8FD8A08C4F7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Whatsapp Id.'
        GUID = '{E90AA80B-7FB4-4227-87BF-3E3B57B3A410}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        GUID = '{24CBDF3D-5B30-4EF9-83C7-F05E1E729418}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Imagem'
        GUID = '{C8D95925-BF74-40AE-B10A-6289EB6B179A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PHONE_NUMBER_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Phone Number Token'
        GUID = '{8BA714BF-AC82-48BB-BA7A-0798D34AAB24}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_WHATS_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Template Whats Default'
        GUID = '{30BEACF8-48D3-4BFA-85A4-F7EE788E5988}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ULTIMA_MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data '#218'ltima Mensagem'
        GUID = '{C1E78F8F-C919-4066-B55F-2A583FD3344D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SICRONIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sicronizado'
        GUID = '{36884F06-A2A4-41F1-9BCA-6DEDA8DCDEA6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR_AVISO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular Aviso'
        GUID = '{29EDFCD9-44DD-4B68-BCDC-91B11F7D3D9E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_LEADZAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Leadzap'
        GUID = '{C9402F0B-D5B1-45F0-9C68-307C30A83059}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{21CB1C10-5CD1-464F-A7B7-C6982D29C53E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{B33A2C45-B809-4F90-B89D-764B99991989}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Dia'
        GUID = '{8E0550C7-1C55-4AAD-8344-6EFC43FCAB96}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'X_API_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'X Api Token'
        GUID = '{05B2E3E9-D42A-450D-8984-DB0775ACB007}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_API'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Api'
        GUID = '{11902236-F513-46AB-91E2-EA02E1036D0B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOKEN_API'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tokapi'
        GUID = '{A5E0B2AC-7AC3-4A7E-A494-75119B90497E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_PAINEL_WEB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Painel Web'
        GUID = '{EBBB742C-3667-4A28-B0DF-4CE9AC7BB311}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Usu'#225'rio'
        GUID = '{D6DF0327-5278-4BC5-A06D-618986EBF47F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_SENHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Senha'
        GUID = '{046CF968-CBE0-49A4-B474-4B595073E839}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_EMAIL_NOTIFICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Email Notifica'#231#227'o'
        GUID = '{07150DD4-4CE1-43E8-8A32-F9A7175B9688}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_INSTANCE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Instance'
        GUID = '{0098B330-FBB4-412D-BA8E-22F72D9E02EB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_INSTANCE_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Instance Token'
        GUID = '{4D9CF110-A114-47A1-B30A-2E8105D3D23E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_CLIENT_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Client Token'
        GUID = '{FE9AE294-7721-49EF-BE8F-4220D37FD30B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_API_URL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Api Url'
        GUID = '{6A842AD1-E0A5-4137-9C00-5A04A3E4965A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Tipo'
        GUID = '{5FACE23B-B796-4D35-96B1-60C0ACEB388B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_CONECTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Conectado'
        GUID = '{E907E5D5-6141-4369-8FCD-0E284EAD3BEB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_CADASTRO_WHATSAPP'
    Cursor = 'CRM_CADASTRO_WHATSAPP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '513017;51301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
