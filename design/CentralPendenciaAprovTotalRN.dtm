object CentralPendenciaAprovTotalRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '511023'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbOs: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{2332C4F5-02D3-4A82-B960-1FB8F58F1C94}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{88735BC5-DF79-45AC-888D-7611707E2DEA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{22CF03CE-BB2B-462B-865A-110DE4B3E212}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DESC_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Desconto Item'
        GUID = '{0F806C1A-6CB6-44C9-9BD1-0C6E235AE1A8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO_DESC_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento Desconto Item'
        GUID = '{12E779CD-3DE5-418F-8A9B-6A3CDBF29C93}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PENDENCIA_DESC_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pendencia Desconto Item'
        GUID = '{ECB4C8D1-2D7D-43B4-A414-CEB83CBFBCB6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DESC_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Desconto Servi'#231'o'
        GUID = '{F2110BA0-8904-444F-ADAF-7610A8B644B7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO_DESC_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento Desconto Servi'#231'o'
        GUID = '{BFC9C0D8-AEDA-4EB9-9A5E-FA4B4CE370D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PENDENCIA_DESC_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pendencia Desconto Servi'#231'o'
        GUID = '{E68FE5EE-FAA2-455D-B0F6-513675BE6324}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESCONTO_ITEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desconto Item'
        GUID = '{A8541B8C-834E-4650-927A-522BDADFAF18}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESCONTO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desconto Servi'#231'o'
        GUID = '{C63F3C57-BDB1-4F34-A79A-E897ACC15FE5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTOS_ITENS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Descontos Itens'
        GUID = '{27B1E58A-E9F4-4C77-B021-A5E555767013}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTOS_SERVICOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Descontos Servi'#231'os'
        GUID = '{8DD61B1A-E795-4F8F-A2EF-D6FD3F5D46AA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS'
    Cursor = 'OS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '511023;51101'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPendenciaEvento: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{415AC9CA-F49C-4E1A-BF44-427EEB688D84}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        GUID = '{8E0AA16E-CE77-43D2-B932-CA0BAA51D76B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pendencia'
        GUID = '{2E5E6DCB-9442-4F7C-9185-42FF72A6503E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_REQUISITOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Requisitou'
        GUID = '{DB08E366-C2B4-4E76-A43E-8A4119FE705B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INICIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data In'#237'cio'
        GUID = '{88E9B94E-2157-4E19-BF5C-78DF90FB0176}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESEJADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desejado'
        GUID = '{77E2329C-2A87-4E70-B557-2450E34601D4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_APROVADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Aprovado'
        GUID = '{2239C992-2EEE-4C29-BBE1-C098BA4CDEB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        GUID = '{DEE6A31C-CFAC-4EB2-A8C3-945725E54A66}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Pendencia'
        GUID = '{871230F3-6A22-4830-A333-AD32268CF3B9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PENDENCIA_EVENTO'
    Cursor = 'CRM_PENDENCIA_EVENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '511023;51102'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
