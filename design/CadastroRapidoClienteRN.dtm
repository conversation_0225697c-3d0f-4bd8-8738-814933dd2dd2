object CadastroRapidoClienteRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '7000195'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbClientesProfissaoInclusao: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{79FE4F51-7B42-4D82-8D3E-59C457FCD480}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PROFISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Profiss'#227'o'
        GUID = '{03A4B660-96F2-4136-860A-4E2F2B097091}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        GUID = '{3545F712-933A-4DAC-8B4F-289F41B5A4DE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Descri'#231#227'o'
        GUID = '{83B45E77-2D34-4576-B341-F7E6A450C845}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_RAMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Ramo'
        GUID = '{57E1653A-06EC-42B1-96CC-61B3A06E881B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PROFISSISAO_ITAU_CREDLINE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Profissis'#227'o Itau Credline'
        GUID = '{4448B62C-3F56-4174-9978-DDF5E56BFB8F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CLIENTES_PROFISSAO_INCLUSAO'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;44001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbUf1: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{69FB7534-289F-495C-8C1F-0FC65A28C022}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{A1D7C8FC-88EE-4D41-9A43-B50F90D8CA50}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'UF'
    Cursor = 'UF'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbUf2: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{32D20321-72CE-4BDF-BB37-EF0476C02572}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{73227C4E-E1C3-48F1-A17F-5BC26769A150}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'UF'
    Cursor = 'UF'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbUf3: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{5370BA2E-C16C-417D-8476-501C39CE7CA5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{833437EC-2AD5-47D9-9AD8-8AA1DB84DF88}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'UF'
    Cursor = 'UF'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidades1: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{47CF03FB-1066-47F1-B249-6B315D54A982}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{263C358D-AB20-4D0A-B042-B4943D7BB503}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{14F9763D-4288-48FE-AC31-587228D7C88A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_POR_LOG_NVL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Por Log Nvl'
        GUID = '{53628E9C-DC45-4084-A65C-F77823727ABB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'UF'
    DetailFilters = 'UF'
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 0
    MasterTable = tbUf1
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440015'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidades2: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{15B11D9B-6A9A-46BE-8601-EF349CF91337}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{8BE6D37B-4383-4AE0-8D44-F82A339C1EC1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{43AAF6F5-2B98-4D57-B75B-8E6D921AB090}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_POR_LOG_NVL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Por Log Nvl'
        GUID = '{719C8EE4-6B7C-4EEE-B947-B1F1B528DF2A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'UF'
    DetailFilters = 'UF'
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 0
    MasterTable = tbUf2
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440016'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidades3: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{C4B92963-2E59-4D19-B2E2-0275C45F0420}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{249E1FD1-7B3F-4E76-9E4A-0042FE6C590C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{AC689966-610B-476D-AC5F-A852A5013807}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_POR_LOG_NVL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Por Log Nvl'
        GUID = '{B438E20B-641E-4D7D-B34C-57C35D53604A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'UF'
    DetailFilters = 'UF'
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 0
    MasterTable = tbUf3
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440017'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNacionalidade: TFTable
    FieldDefs = <
      item
        Name = 'COD_NACIONALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Nacionalidade'
        GUID = '{E3D192E0-FB14-4DEB-9F6B-75EEBB67239A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_NACIONALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Nacionalidade'
        GUID = '{AC458284-037F-4EC0-A8D0-7168D85BD0C5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PAIS_BC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pais Base C'#225'lculo'
        GUID = '{A902D19A-21F5-4CCD-8BC9-80892892198B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'NACIONALIDADE'
    Cursor = 'NACIONALIDADE'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440019'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEstadoCivil: TFTable
    FieldDefs = <
      item
        Name = 'COD_ESTADO_CIVIL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Estado Civil'
        GUID = '{E7DA595C-29FA-42B0-BE05-34B59A20EAAC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{15D073A2-1B76-46D7-B963-45EA13DAD202}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ESTADO_CIVIL'
    Cursor = 'ESTADO_CIVIL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440020'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesRamo: TFTable
    FieldDefs = <
      item
        Name = 'COD_RAMO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Ramo'
        GUID = '{00E6B68D-96A5-49A5-A60E-614B1FDD6F22}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{0C362457-EA8A-4764-B5F8-5EF53D34A6EE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_RAMO'
    Cursor = 'CLIENTES_RAMO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440021'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosJuridicos: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{22593C48-34EA-4918-9B7B-A2B2AC9E5979}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Segmento'
        GUID = '{FEEB8037-5AC0-4B6E-BC62-5D7CDC228AD1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_RAMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Ramo'
        GUID = '{1BFBE797-BFE8-4D5F-A2BC-DCE606AC47CF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc'
        GUID = '{2BC76350-B7BF-45CA-BBB4-0D5CF2171F9E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSC_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Insc Estadual'
        GUID = '{0A3DB542-**************-01863CCD12E5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSC_MUNICIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Insc Municipal'
        GUID = '{A28B45BA-BCDB-4344-B25B-235A1FA50F8B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FANTASIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Fantasia'
        GUID = '{81121B43-3429-4A70-903E-3EA849E601B1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_FUNDACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Funda'#231#227'o'
        GUID = '{05E9719A-7207-410B-B3D7-F34F9AEA8E62}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FATURAMENTO_MENSAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Faturamento Mensal'
        GUID = '{F12FA5DF-9950-4136-994A-5EC357E4AC26}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PORTE_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Porte Empresa'
        GUID = '{58A91E6F-A316-4F2B-851B-8A7A1F0A9F1B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC_SUCESSORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc Sucessora'
        GUID = '{6205BF50-1D4C-4037-AD99-A298715CAABB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_SUCESSORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Sucessora'
        GUID = '{E0F86221-A0C1-417F-9070-17E73FE55C2B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMOVEL_ALU_PRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Imovel Alu Pro'
        GUID = '{96392DB0-C485-493F-95AD-514BCED196D5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAPITAL_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Capital Social'
        GUID = '{01957E0A-DC18-4B8A-8AC0-07795ABF9C71}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_FUNCIONARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Funcion'#225'rio'
        GUID = '{788DCDFD-7D9A-4598-9A7A-74D74834C6D4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ULTIMA_ALTERACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = #218'ltima Altera'#231#227'o'
        GUID = '{24C05101-058C-4CA0-B81B-E56DF8140203}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Estoque'
        GUID = '{6F79DCF2-95CA-451D-8F95-109EF0EFE3FA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNAE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnae'
        GUID = '{3742751E-B199-4EEC-9B78-FA4A7624C15E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DADOS_JURIDICOS'
    Cursor = 'DADOS_JURIDICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440022'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosFisicos: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{23588C10-9327-4CDA-8D1E-3F62337D8A25}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_CLIENTES_SAUDACOES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Clientes Saudac'#245'es'
        GUID = '{CF2446C2-68E3-45DD-8BD2-1C9F3448A7D8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_CLIENTES_TITULOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Clientes Titulos'
        GUID = '{4566686C-ADDC-41CD-AFC3-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SISTEMA_IDIOMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Sistema Idioma'
        GUID = '{826D3370-879F-4178-B94D-A1C049BEF90B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sexo'
        GUID = '{2284648E-3BE8-4CAC-9B5E-5E5A86C4178D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ESTADO_CIVIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Estado Civil'
        GUID = '{3F7F1697-5DCA-42E1-9C78-1E8F81DD9847}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANIVERSARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Aniversario'
        GUID = '{56A2B08D-4659-4E76-A87C-E176DBDF3DB8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pai'
        GUID = '{4631C662-F28E-4026-A515-BA8327F7A6D9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Mae'
        GUID = '{3F628C0B-B01D-436F-B99D-8B2237D75ADF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        GUID = '{072CBED0-846F-4682-81F3-08ECA1842DEE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RG_NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rg N'#250'mero'
        GUID = '{9B38EF80-58C1-4836-AE0F-B79FB0B77A7F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RG_EMISSOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rg Emissor'
        GUID = '{4B621F1E-8F8F-460C-AD62-A171D2A943FE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RG_DATA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Rg Data Emiss'#227'o'
        GUID = '{5C44D672-F398-4E58-8B87-F67B16EAEEDD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSC_MUNICIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Insc Municipal'
        GUID = '{20FD7EA5-BB1F-4AEC-8EB2-9716BBD96165}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESCOLARIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Escolaridade'
        GUID = '{*************-48EC-BE10-F8EF7DBC3D66}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RG_NUMERO2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rg Numero2'
        GUID = '{0F694ACB-5888-479B-9C6E-C86694BBB19F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNH'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnh'
        GUID = '{ABE198FE-4419-4ED4-8809-2722F362FA55}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CNH'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Cnh'
        GUID = '{E018ADD9-5000-401A-9E3D-BC4CE9960F4C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HOBBY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hobby'
        GUID = '{E49FB457-1080-4E7A-8B22-5BE1FCA8E4B6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AUTORIZA_CORRESPONDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Autoriza Correspondencia'
        GUID = '{82225BBC-93C4-4A96-88E3-D1D5C51BED57}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{C46C5190-989A-4624-B183-63EDDBE13F86}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_NATURALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Naturalidade'
        GUID = '{75DCA25A-5325-4D9B-AB35-2DA173D67F6D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VENC_CNH'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Vencimento Cnh'
        GUID = '{790F649C-2227-400B-AB5B-33086577AD11}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_PREVIDENCIA_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Previdencia Social'
        GUID = '{89AEBFEC-0986-4871-A88A-79E32A67C900}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SOCIAL_SECUTITY_NUMBER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Social Secutity Number'
        GUID = '{E0679D6A-5353-48B3-92DB-9160C420DC71}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DADOS_FISICOS'
    Cursor = 'DADOS_FISICOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440023'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientes: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{0E95D2D2-12E9-4758-9A90-8F6C00494719}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{AEB31A07-4875-4274-B387-FD13E36215AF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Endere'#231'o Eletronico'
        GUID = '{B7F9DD57-B214-4195-B467-8BAACE6D7644}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Res'
        GUID = '{3BEE0B60-C2E2-4D2B-A3BB-338363562D95}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Res'
        GUID = '{0367E89F-618C-43B1-B24D-646FB9C08910}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo com'
        GUID = '{5AFB78DB-E483-491C-ACAC-ADED4C6C40FA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone com'
        GUID = '{1C614A02-6647-4394-8C09-10B76251AA8C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Cel'
        GUID = '{1E9CEACA-FF59-4DB6-8440-9CD8F6038B28}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Cel'
        GUID = '{1EEEFEF3-808B-46E1-A6A9-8B1F2CC77FF0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_NFE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Nfe'
        GUID = '{6D5DAA42-F2B3-45FC-B139-74496D3F20ED}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_NACIONALIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Nacionalidade'
        GUID = '{44F634A3-D92D-4F48-886F-7C69BD97C99E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Classe'
        GUID = '{EC0E2958-203C-4318-9534-53882327BA5F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo'
        GUID = '{D4557DB0-7B69-4FA7-A9DC-233F891C3335}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PROFISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Profiss'#227'o'
        GUID = '{8D5DA358-40AF-4611-AA19-76CCC710FF39}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Res'
        GUID = '{1E0E0E13-4537-470C-8216-322F6917F83B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid Res'
        GUID = '{97210C27-2967-4962-9A3A-6F546DC531A5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf com'
        GUID = '{5026131D-6035-44E7-A562-173657F3A091}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid com'
        GUID = '{744E0FF3-6529-40D1-AE95-80C13FA87C5F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Cobranca'
        GUID = '{3B341C7D-A9CD-4E76-9699-B3D71C24DF94}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CID_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cid Cobranca'
        GUID = '{961497C5-CE80-45C1-94DA-81336FA536CD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO_ECONOMICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Economico'
        GUID = '{CA6067A0-9F4B-4A70-A1A6-E24E5450CBE7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO_CADASTROU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Cadastrou'
        GUID = '{577E2DD4-D8D2-4282-86B6-62B4A2B20F38}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AVISA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Avisa Cobranca'
        GUID = '{4892EC0A-EB01-4BC2-9256-762471299F75}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMITE_FATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Emite Fatura'
        GUID = '{6FDDAA4C-4542-4B96-839E-44E68136616A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Res'
        GUID = '{28786F1C-9DFF-4156-9398-986F0EA90808}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Res'
        GUID = '{0C081FD3-C99F-4757-AB5A-21C2F3EE4EFC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Res'
        GUID = '{F3B2D735-0635-4101-AD5E-DF6562D1A6CC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento Res'
        GUID = '{5065DC2D-3698-4F00-A664-21BF83888342}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada Res'
        GUID = '{57B6ABC3-8673-4F6C-B4C5-A9C46DFA6D54}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato Res'
        GUID = '{4D84D13E-0098-4C5B-91A6-9A84B82217A9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro com'
        GUID = '{710F5875-8888-4B40-825A-8D41858B0036}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua com'
        GUID = '{12FCAC6C-693B-4633-83B9-DD2CB9FBE113}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep com'
        GUID = '{304477E2-FE71-4C16-9B54-08DFB74FC103}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento com'
        GUID = '{121C888D-88AB-4F83-9839-4E4AAC52591A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada com'
        GUID = '{4296D106-192B-4684-BAC4-DF78D14A2492}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato com'
        GUID = '{0A3A74C4-8832-47DA-A884-88182F7A7E95}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro Cobranca'
        GUID = '{4CF90373-6FD0-4081-B46D-D7F71F9DC899}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RUA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rua Cobranca'
        GUID = '{2D86947D-5937-4693-B97E-00B773F9A37A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep Cobranca'
        GUID = '{D36748E5-B40A-4C9A-8617-7820D9AD1582}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento Cobranca'
        GUID = '{03A4D9D7-9C7C-40F7-999F-F196E527149B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada Cobranca'
        GUID = '{EAA7D29C-60BA-412A-A5C6-4D05B3F4EC4B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato Cobranca'
        GUID = '{26C2C949-788D-4961-8D68-AA4BB5B1E6EE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Res'
        GUID = '{A7B537A8-9EAB-4095-B429-3FFD8642182A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAMAL_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ramal com'
        GUID = '{9285B49A-65E7-4A52-8C0B-7A5377C41763}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO_COM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o com'
        GUID = '{F7E1519D-D6E7-4C40-954E-EA7FC068E6C5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_FAX'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Fax'
        GUID = '{60465377-78EB-423D-A688-AFC47311B846}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_FAX'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Fax'
        GUID = '{7F02D68D-ECA0-415B-8E29-FBD565FA0978}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAMAL_FAX'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ramal Fax'
        GUID = '{ADCA10C4-4A7C-4079-8F09-50633CCA108D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO_FAX'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Fax'
        GUID = '{B6B51EBE-2051-42FC-90C7-4AEC1404EFAE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Cel'
        GUID = '{FD5392E1-5237-4C96-8C0D-D11CFAEA682A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BANCO_CR_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Banco Cr Fornecedor'
        GUID = '{6DB8ABE6-BD32-47E0-B400-3AE4A75A8B8A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AGENCIA_CR_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ag'#234'ncia Cr Fornecedor'
        GUID = '{827002C7-C4D0-4ED6-A10A-C40C27ABA8E7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTA_CR_FORNECEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Conta Cr Fornecedor'
        GUID = '{A78F6A53-C8FA-4987-9251-A0162C68834C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MOTIVO_BLOQUEIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Motivo Bloqueio'
        GUID = '{DE7C7167-C6A3-4D94-9FD5-42AC4B316AEC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_COMERCIAL_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone Comercial Tipo'
        GUID = '{51C549D6-9310-484C-AF87-7E24B48863C6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_RES_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone Res Tipo'
        GUID = '{B5B2EDC3-2C22-49B5-ABA0-E05088C64271}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_COBRANCA_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone Cobranca Tipo'
        GUID = '{F4397804-618C-4B01-A432-0566D931569E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_PAI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Pai'
        GUID = '{A5507FA0-3891-4169-AF9A-4EC4FA1DEE1E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_MAE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Mae'
        GUID = '{0999CE73-38A2-48F5-9C1C-09765C37657E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa Trab'
        GUID = '{7442C177-DFD8-4612-A5D1-B81768D0760A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC_EMPRESA_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc Empresa Trab'
        GUID = '{1E8E9E4D-F945-4FDF-8E49-55B406630C52}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_EMPRESA_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Empresa Trab'
        GUID = '{A120D2E3-EEA6-499E-8923-73CDDDA8EB8C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CATEGORIA_PROFISSIONAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Categoria Profissional'
        GUID = '{B9417136-99D9-43C0-9A9D-801EE05565D3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_ATIVIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Atividade'
        GUID = '{D65D42FE-0A99-483B-A8B4-FBAC2D22F4F7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ADMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Admiss'#227'o'
        GUID = '{0D0B21B8-BD12-4452-BE71-9CA7EB0E2962}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_ANTERIOR_TRAB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Anterior Trab'
        GUID = '{8707295E-02E4-453B-BD99-4FBC50EBCC38}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAPITAL_INICIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Capital Inicial'
        GUID = '{033F3691-9E04-4ABF-8C9F-BCDF2E6DD3E4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PARTICIPACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Participa'#231#227'o'
        GUID = '{E3412A3E-2CDD-42A5-A417-61C0A51D1FF0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_RESIDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Residencia'
        GUID = '{B56D80BD-DB90-4153-8BE7-09F0192117E3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RESIDENCIA_ATUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Residencia Atual'
        GUID = '{B173D091-3BC8-481D-8636-99BE2BF9C512}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RESIDENCIA_ANTERIOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Residencia Anterior'
        GUID = '{7436DC30-F3FB-4A08-8C8E-F166303706BC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAIXA_POSTAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Caixa Postal'
        GUID = '{093A5307-37C1-4444-94D5-EE6CA33E0AA8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_ANTERIOR_ENDERECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Anterior Endere'#231'o'
        GUID = '{136B0F98-015A-4BE9-A4EA-0D9C28923F45}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_DEPENDENTES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Dependentes'
        GUID = '{826D168C-7EF1-48C0-B796-25DF228CE996}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDEDOR_RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vendedor Respons'#225'vel'
        GUID = '{DEADB79A-84BB-4144-94A7-1A1D36E4CCD2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_EMPRESA_ANTERIOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Empresa Anterior'
        GUID = '{718FECED-B002-454A-9EF8-40E215A482F9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ULTIMA_VISITA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data '#218'ltima Visita'
        GUID = '{8AFBA47F-BB7D-438A-8CB3-D5BD861C5815}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROXIMA_VISITA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Proxima Visita'
        GUID = '{B0DA2C81-2590-490C-8DDE-6C02BD818A4D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_ROTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Rota'
        GUID = '{DB18C51D-9430-4C1F-8543-6EF357957D8F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ROTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Rota'
        GUID = '{B2AA3E33-277E-4B46-9718-CE681180E369}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_PDV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Pdv'
        GUID = '{4169C35A-DA03-4629-BA60-FD75633C5625}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RES_ATUAL_MESES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Res Atual Meses'
        GUID = '{D944E1A6-9982-40B8-A2B2-0F205A2FEA88}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RES_ANTERIOR_MESES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Res Anterior Meses'
        GUID = '{A4CB02F5-AAB9-4C5E-B16D-DF3AF35387D3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_ATIVIDADE_MESES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Atividade Meses'
        GUID = '{0A1447FA-4893-48E1-8232-BB86AF18677E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PGTO_DIAS_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pagamento Dias Entrada'
        GUID = '{2586F30D-BF61-41B1-8D18-E850238AFA24}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PGTO_DIAS_INTERVALO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pagamento Dias Intervalo'
        GUID = '{6BDED4AB-37B6-41B1-B264-748C0C272A25}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PGTO_PARCELAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Pagamento Parcelas'
        GUID = '{37D77F32-A2F3-429F-BAF9-7A28D80BCFCC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INICIO_RES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data In'#237'cio Res'
        GUID = '{D0DFDF72-83B7-4B1D-9E1C-E1E3E3785A4D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_SEQUENCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Sequencial'
        GUID = '{1DDAF54D-56B1-4377-9D2D-AF540759826F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_DEPENDENTES_MAIORES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Dependentes Maiores'
        GUID = '{03EB570E-A45D-4804-93A2-53498A50489F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPRA_MINIMO_AVISTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Compra M'#237'nimo Avista'
        GUID = '{410BDC1C-D693-46D8-A3A8-198A6BD40FCD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPRA_MINIMO_FATURAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Compra M'#237'nimo Faturamento'
        GUID = '{960EE3D4-B8E5-4D2B-A6BB-F839D1CF3C4A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROXIMA_LIGACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Proxima Liga'#231#227'o'
        GUID = '{4C205BBD-05B1-49B4-B4E0-DDA063200D2B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIA_SEMANA_LIGAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dia Semana Ligar'
        GUID = '{BCD74A87-1624-435A-AC77-A3D337967D85}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_INICIAL_LIGAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Inicial Ligar'
        GUID = '{A6E9CADC-0B9B-40DB-A37E-BD885A498C58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_FINAL_LIGAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Final Ligar'
        GUID = '{F6ABBDC1-4B8A-4373-B4ED-0CFD2AD1A0EF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email2'
        GUID = '{7F7C44B4-BAAA-42FB-AA49-99E2D6FA9A7B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACEBOOK'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Facebook'
        GUID = '{33963EC4-021F-4B85-8E1E-784D29BBD945}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TWITTER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Twitter'
        GUID = '{D4C9A59C-7F1C-4896-9C66-FF2DAA63FAE0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_VENDA_MICHELIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Canal Venda Michelin'
        GUID = '{46C07D9E-ACAA-43DB-AC8E-4F135F79BBE6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o Trabalho'
        GUID = '{92060AE1-65FC-4AE3-A48D-6ADCAAE5A2D7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SETOR_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Setor Trabalho'
        GUID = '{43F666E8-0A01-44D7-9E82-5631CE4868CB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ADMISSAO_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Admiss'#227'o Trabalho'
        GUID = '{50273E45-9EE6-4CAB-BB95-CD12FA7AEE96}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RENDA_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Renda Trabalho'
        GUID = '{A2694115-42DB-4299-ABD1-DCDF5BD2627A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Trabalho'
        GUID = '{BA54E0BE-49CE-414B-BCAD-39A026F91423}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Trabalho'
        GUID = '{0B61BD41-8D8B-4550-9977-7E90AEC05866}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CONTADOR_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Contador Trabalho'
        GUID = '{E7D1DF35-1C25-4935-AC59-62DD77884130}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CONTADOR_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Contador Trabalho'
        GUID = '{14AE91B3-25FD-4BBE-9604-03DBDF77E898}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_CONTADOR_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email Contador Trabalho'
        GUID = '{56EDDF0E-CF0E-4E0A-83A1-568EC7E46956}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RADIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Radio'
        GUID = '{D50B6C60-9026-4E89-AFE5-25990C4887E2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FLAG_EH_SHOW_ROOM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Flag '#201' Show Room'
        GUID = '{515F3F6B-DCCC-44E1-A395-E4BA1D5249FC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_SHOW_ROOM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Show Room'
        GUID = '{8206C89F-7F31-465B-A25B-1E889EB6DD56}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_SHOW_ROOM_FILIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Show Room Filial'
        GUID = '{CE835B41-59AF-4770-8C4C-B93795F33E94}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IDENT_ESTRANGEIRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ident Estrangeiro'
        GUID = '{629C654D-A615-4D25-A650-EA5B3E5AC3DA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAX_TRABALHO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fax Trabalho'
        GUID = '{9058E68B-F95B-4AE8-B387-97074BDF1C5D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES'
    Cursor = 'CLIENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440024'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object scClienteDiverso: TFSchema
    Tables = <
      item
        Table = tbClienteDiverso
        GUID = '{551BCE04-8F53-48D2-A552-FF2C6B2CF9F9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClienteDiverso: TFTable
    FieldDefs = <
      item
        Name = 'AGENCIA_FORNECEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Ag'#234'ncia Fornecedor'
        GUID = '{7A2402F1-1A74-4E26-8B3F-4B7F566CED97}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AGENCIA_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Ag'#234'ncia Terceiro'
        GUID = '{7CDDA504-9FAB-4822-BCB4-BC5AF369F1DB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_CARGA_MEDIA_ICMS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Carga M'#233'dia Icms'
        GUID = '{D7F66C88-58AF-4CEC-8CB2-BD88E8A2D5B3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ALIQ_RED_ICMS_TRANSP'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Al'#237'quota Redu'#231#227'o Icms Transportadora'
        GUID = '{51C84464-45B6-4F05-B216-53797E3C97CF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        GUID = '{0BD098D3-BEF3-4A98-AC06-D079D7C7303B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BCO_FORNECEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Bco Fornecedor'
        GUID = '{CAB76147-0AE0-4DD1-9A93-73A8FBD2D0E2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado'
        GUID = '{C9C39F61-F3F3-4816-BE0A-47A2B3FBC5BD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO_CPAGAR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado Contas a Pagar'
        GUID = '{4F471FBD-64E2-4F2F-B851-65727439DBDE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BLOQUEADO_ENTRADAADIANTAMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bloqueado Entradaadiantamento'
        GUID = '{6A25EC92-C540-4837-A69D-6A9B921C9A3E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CARTAO_DOTZ'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cart'#227'o Dotz'
        GUID = '{C5EA56B4-F273-4670-AA4D-0C8B0DC6ACB2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        GUID = '{DEDDD899-E6B8-4885-A8D4-4AB9F7E6FE39}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc'
        GUID = '{36B07EF8-4E81-4DC6-B804-CB1BCAC5AF13}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        GUID = '{80F0BEDA-CB86-4195-BD47-A653B2E2CAC8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLASSE_GERAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Classe Geral'
        GUID = '{1AA5690E-6D9E-417F-96BA-73A6F9E0FB72}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLASSE_PECAS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Classe Pecas'
        GUID = '{2BB8CF79-3C83-4F86-BCA5-C8600E660730}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLASSE_SERVICOS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Classe Servi'#231'os'
        GUID = '{58E55EE6-E448-4D81-99DA-B4A7AB9DFF30}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLASSE_VEICULOS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Classe Veiculos'
        GUID = '{240C7B49-C598-40AB-9E31-6DB6BE6EF986}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_ESPECIAL_GRUPO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Especial Grupo'
        GUID = '{C07B80FF-F85D-4BB4-8768-333F45A8A4A5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_FABRICA_LOCAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Fabrica Local'
        GUID = '{CE311A98-E598-4190-80E2-7E5BC302272C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_GARANTIA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Garantia'
        GUID = '{9270DC42-B4E8-4C62-8ED9-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_ISENTO_PIS_COFINS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Isento Pis Cofins'
        GUID = '{6F7874B4-90A7-4B9A-AD4B-CA6F58F4AAB7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_NAO_RETEM_PCC'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente N'#227'o Retem Pcc'
        GUID = '{F54CB13B-2907-4CBA-ABD9-950A9C6C8048}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_ORGAO_PUBLICO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Org'#227'o Publico'
        GUID = '{06ACE205-A8B4-4A62-ACC0-96D9875B78D3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_PRECO_FABRICA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Pre'#231'o Fabrica'
        GUID = '{86094D54-7A1C-4EFF-BAD4-EEBCFE86575B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_RELATO_SERASA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Relato Serasa'
        GUID = '{FDF92ECD-3292-41FC-965D-2AE9A7DE33D7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_REVENDEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Revendedor'
        GUID = '{ADE2CA0A-29FE-4F93-8767-D505DB8CAFCE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_SUBSTITUICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Substitui'#231#227'o'
        GUID = '{62F7914A-6182-48BC-AD19-A39DEA03A520}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_TRANSPORTADORA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Transportadora'
        GUID = '{D30DB27B-D317-42A9-8315-0CCB1E69A3DA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_BANCO_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Banco Terceiro'
        GUID = '{5E42E007-9E63-433B-9CF3-778C67640C93}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{BAF6314A-B9D5-4547-B713-9B247312F905}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{14F5224F-3760-4EEE-9AD8-D16098FC8177}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE_HONDA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente Honda'
        GUID = '{6734DADC-2C66-49D2-B9C8-8168596C8BA2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente Terceiro'
        GUID = '{6D0B6AB0-C346-4964-807D-3215478A5517}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONCEITO_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Conceito Cliente'
        GUID = '{B7EF4F78-7190-4793-97F9-511D2DD552B8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONTABIL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Contabil'
        GUID = '{4E0B5255-2925-4220-BC99-619A38EF9AAE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{D3644D7A-7CD5-4E84-AC00-6918A7375EC2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_FC'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Fc'
        GUID = '{E9C92FF2-8DD1-4C10-8B29-F6FB3E499285}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FABRICA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fabrica'
        GUID = '{149C532C-C6FA-4606-9D3F-FB703CDA4E09}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Cobranca'
        GUID = '{2CFE4A63-A2C5-46C7-A7B4-CDA0DFF5E98F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORNECEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fornecedor'
        GUID = '{67F006E0-1095-4D33-93A0-0FF96EE0D393}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_GRUPO_PC'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Grupo Plano Contas'
        GUID = '{A74C2CC0-BCA2-46B0-951E-86F1574006ED}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_NACIONALIDADE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Nacionalidade'
        GUID = '{A8FAE233-7BBF-4EFF-A2C5-276182EB6F77}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SACADO_AG_BANSICREDI'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sacado Ag'#234'ncia Bansicredi'
        GUID = '{9CF3D8C0-BED9-4D20-BE2C-0079E56E17CA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_SUB_TRIB_ISS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sub Trib Iss'
        GUID = '{EBB88D7B-01FC-41F5-9769-02269DBC5BDF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo'
        GUID = '{F8ADB94B-FDB7-434F-924C-DD6575EE95D7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo Cliente'
        GUID = '{CB880C96-1C16-4759-8567-9AEEACE78AB9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        GUID = '{D1CD5797-485C-402B-85A8-12489D2A309E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONCESSIONARIA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Concessionaria'
        GUID = '{*************-4C17-9767-F39146E14072}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSUMIDOR_FINAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consumidor Final'
        GUID = '{9B5062E8-C4D0-4318-AB9D-D37CA362B4C7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTA_CONTABIL_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Conta Contabil Cliente'
        GUID = '{17BF0ED7-2204-4758-A766-BABCE55F6353}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTA_CORRENTE_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Conta Corrente Terceiro'
        GUID = '{A001E65E-0CD8-4A64-AB00-A20376045153}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTA_FORNECEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Conta Fornecedor'
        GUID = '{6BC44572-505D-4DFC-A67B-2385F63BA2C8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        GUID = '{28922B27-A090-43A9-A56A-DB073792BFB8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTRIBUINTE_IPI'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contribuinte Ipi'
        GUID = '{D9678A4C-C5FF-4F39-8617-123E1DDF8DBC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COV_CLASSE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cov Classe'
        GUID = '{2DDC7B85-2CF2-4962-9C99-070CF0CF57E1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        GUID = '{C9F7ACC9-AEE7-4AFE-8346-CCA7568D752A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_EMAIL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm Email'
        GUID = '{0D98665B-5E4D-47A6-8988-014EC4E4FD50}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_FONE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm Fone'
        GUID = '{D82328E8-F783-4C73-8ED2-AE0481786C02}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_MALA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm Mala'
        GUID = '{B8A1F928-80E4-4399-948C-3622F4208AB6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRM_SMS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Crm Sms'
        GUID = '{64597E0D-629F-491F-AF3E-0C9245A9C39C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_CADASTRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Cadastro'
        GUID = '{FF4D395A-47EC-489B-AB07-BFA9F2C0231A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ENVIO_H3S_HONDA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Envio H3s Honda'
        GUID = '{0A1C7FF0-1CA5-4A2F-B291-784C1BFAD869}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_LIB_BLOQ'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Lib Bloqueio'
        GUID = '{0322E8FF-5826-4CB1-A8DC-F7AEBF34EFD5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_LIMITE_CREDITO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Limite Cr'#233'dito'
        GUID = '{C68A1144-FCAA-4618-BD57-D520B5B0C9D5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_PROXIMO_FAT_CLI'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Proximo Faturamento Cli'
        GUID = '{42179640-604E-4E92-9183-832384D5EDD9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ULTIMA_ATUALIZACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data '#218'ltima Atualiza'#231#227'o'
        GUID = '{8D9F14CF-095A-4173-ADAA-DD3856B24851}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VALIDADE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Validade'
        GUID = '{D298F6DA-A732-4BC0-912E-E9BF4B296C60}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_VALIDADE_SERASA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Validade Serasa'
        GUID = '{BE857599-7A3F-4533-B4DD-EAA4A7B35E8C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEBILITACAO_FISICA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Debilita'#231#227'o Fisica'
        GUID = '{70F681B7-AE57-415D-B631-45405DAC8BDB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DGTO_AGENCIA_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dgto Ag'#234'ncia Terceiro'
        GUID = '{E0A1242D-B6DD-4F4C-8EF4-D9B8299441B2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DGTO_CC_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dgto Cc Terceiro'
        GUID = '{5CC47BE7-6728-49C2-8C2C-8BBF90D6B379}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIAS_PROTESTO_CLI'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Dias Protesto Cli'
        GUID = '{31A112E9-A189-4D39-953D-7F998AB01B55}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIGITO_AGENCIA_FORNECEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Digito Ag'#234'ncia Fornecedor'
        GUID = '{A9BDA948-EA61-40DC-B7DF-846337AACA16}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIGITO_CONTA_FORNECEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Digito Conta Fornecedor'
        GUID = '{534ECC5C-56C6-4929-A7EB-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_FILIAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Filial'
        GUID = '{A1D7721E-EC97-475A-A751-ECED3F3A7B20}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMITIR_AVISO_PAGAMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Emitir Aviso Pagamento'
        GUID = '{66B72A10-D470-4190-A420-057D4685A770}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_SITE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Site'
        GUID = '{2B48D9EF-37A4-4A1E-8BAE-143A13C47CD2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Endere'#231'o'
        GUID = '{B9C65E9F-7900-476F-A2C7-7FB786B7AD98}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESPECIAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Especial'
        GUID = '{CDAB96AF-7274-4270-A8F5-864D0D064EF7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FABRICA_NOVOS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fabrica Novos'
        GUID = '{C3C84AB6-2949-4571-80E6-15E3405C7499}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FACHADA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fachada'
        GUID = '{17E7CDD2-02EF-4BD8-BD98-D89700A1AC5E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FANTASIA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fantasia'
        GUID = '{BEFAA6F6-160A-4069-9043-17658602A4BE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FATURAMENTO_LIVRE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Faturamento Livre'
        GUID = '{A249D3E7-B6E8-49DD-83DC-1B5A7518A8B3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone Contato'
        GUID = '{C8A83060-13B5-4243-B5DB-16A8C51911E5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORNEC_SUB_ICMS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fornec Sub Icms'
        GUID = '{179EE366-BD4F-40D5-88E6-84A88F7A7597}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORNECEDOR_BLOQUEADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fornecedor Bloqueado'
        GUID = '{9E38818D-DF27-456F-850C-8F0C11A2D5E3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORNECEDOR_NAO_GERA_PRECO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fornecedor N'#227'o Gera Pre'#231'o'
        GUID = '{5B5550EF-9603-40F1-9CE1-AC63A311C052}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FROTISTA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Frotista'
        GUID = '{90B5844E-8D52-4E23-AAAA-55D4EBDE4C4F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GERA_DREC_SUBSTITUICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Gera Drec Substitui'#231#227'o'
        GUID = '{47AE154A-BEA5-4AE2-BE72-D806C7317134}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ICMS_RETIDO_NA_VENDA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Icms Retido Na Venda'
        GUID = '{6AFAC6DA-7E40-49C6-AE81-023F2E2FBEEB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ICMS_VIRA_DESCONTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Icms Vira Desconto'
        GUID = '{01A6B692-B599-4FDB-BFAC-D5692B4FCE83}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IDOSO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Idoso'
        GUID = '{6B027C4D-3F21-4530-B259-9A66CBF8CBB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IGNORAR_ACESSORIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ignorar Acess'#243'rio'
        GUID = '{E60A6837-0266-42D5-9C5F-FF2E3A757864}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INF_BANCO_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inf Banco Terceiro'
        GUID = '{5D6156A7-F773-4601-9B6E-F408AC55F573}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        GUID = '{CD2811FB-CCF1-40DD-BF0B-596001828B19}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERNET_SENHA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Internet Senha'
        GUID = '{0950AE2C-774D-4374-88BB-D2460FA277C1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERNET_USUARIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Internet Usu'#225'rio'
        GUID = '{1B044EDA-9F66-49C5-881F-912F41712AE0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ISENTO_ICMS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Isento Icms'
        GUID = '{92F4AB82-CD83-4E22-B3E0-DC2D43C51F47}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ISENTO_ISS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Isento Iss'
        GUID = '{E5E54B20-005F-49E6-BBD2-5AA9FCF97CF1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ISPA_MATCHCODE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ispa Matchcode'
        GUID = '{52BBA65F-BA9C-4214-A119-C5939A49CA41}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'JURIDICO_ACEITAR_CRM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Juridico Aceitar Crm'
        GUID = '{281EB667-E5CA-4689-B8BE-75F8B234F0C9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBERAR_DESPESA_COBRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Liberar Despesa Cobranca'
        GUID = '{B36885DA-496E-4CD2-9E4C-89B6FD9A650D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_ADIANTAMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Adiantamento'
        GUID = '{A3045DB7-9328-43FB-9BFB-6E3C679B264A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMITE_CREDITO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Limite Cr'#233'dito'
        GUID = '{6760D786-AB17-49A9-AA73-9514F68C7084}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_ITENS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto Itens'
        GUID = '{82F2D725-688F-444A-9E6C-3B2478801330}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_DESC_SERVICO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Max Desconto Servi'#231'o'
        GUID = '{47AE9354-D6CA-48B1-B225-F768E8FE4811}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MEDIA_ATRAZO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#233'dia Atrazo'
        GUID = '{A6BD4F46-9BDB-4DAA-83A9-F9F036B01C59}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MICRO_EMPRESA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Micro Empresa'
        GUID = '{93829A3B-3E23-4011-AD5E-DEC074767A95}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAO_ATUALIZA_CUSTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#227'o Atualiza Custo'
        GUID = '{1D65650A-3785-4DF7-85EB-61C5F9CFEEBB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAO_BLOQ_PROCESSAMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#227'o Bloqueio Processamento'
        GUID = '{DC811E97-5958-4D50-9AFA-E56B33875CA5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAO_CONTRIBUINTE_ICMS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#227'o Contribuinte Icms'
        GUID = '{2D76F482-EEA4-4402-A9B2-3075E5E7E004}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{164012D3-117A-4D6F-8249-A1FA7401C3A2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CHAMADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Chamado'
        GUID = '{FE06EE39-C3AB-417A-9B6B-A824583B40A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CLIENTE_TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Cliente Terceiro'
        GUID = '{86A9D550-8EA3-4A7F-8A54-366C4656A693}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NRO_SUFRAMA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Nro Suframa'
        GUID = '{DC64F0BC-FD2D-4437-B6A5-1C248F9C9E0E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_VENDA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Venda'
        GUID = '{748315D3-9AAC-4330-9CAC-AD82CFA00DD4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPTANTE_SIMPLES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Optante Simples'
        GUID = '{6E7EC6B9-B332-4F17-ACFE-3BD6B65792E3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPV_NUMCLI_CITROEN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Opv Numcli Citroen'
        GUID = '{57F31360-FD45-40B0-B6A4-9223FB091FCC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OPV_NUMCLI_PEUGEOT'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Opv Numcli Peugeot'
        GUID = '{7EED69A5-4259-4E58-8E8D-E2B09C1CAF6E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORDEM_COMPRA_BALCAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ordem Compra Balc'#227'o'
        GUID = '{66247413-2C25-4D62-A225-0C0C0869A019}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORGAO_EMISSOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Org'#227'o Emissor'
        GUID = '{8122F8CF-3DBC-4886-8617-4021DCABA063}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POLITICAMENTE_EXPOSTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Politicamente Exposto'
        GUID = '{49C9056E-FE97-4564-9745-D86C25EF486C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'POSTO_EXTERNO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Posto Externo'
        GUID = '{BEA899B3-EBC5-4E8B-9A3B-2E641380BDB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_FONE_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Fone Contato'
        GUID = '{F0E91453-33E1-4A09-9D5C-258F4FC2B172}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROCON'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Procon'
        GUID = '{6CB7605F-D417-4BD9-8A1E-B9C814805F4B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRODUTOR_RURAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Produtor Rural'
        GUID = '{B783BFBE-A63B-4013-8AED-3FD5C96CCF1F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_DIAS_BLOQUEIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Dias Bloqueio'
        GUID = '{B9EB118B-8F7D-48F0-A71E-FBB41E8270DD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_DIAS_FATURAMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Dias Faturamento'
        GUID = '{1FDFFAA9-6CA4-465D-96D6-6CE37D58BCA3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RECEBER_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Receber Contato'
        GUID = '{1C955F82-829F-44FD-8997-E691E136872C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REDUCAO_ALIQ'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Redu'#231#227'o Al'#237'quota'
        GUID = '{78AB3E84-13C8-42A8-BB5E-B58B7720121C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REJEITAR_NA_CLASSE_DO_CRN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rejeitar Na Classe Do Crn'
        GUID = '{795B5794-BC8A-4B81-BAF7-5D7F2EA2BF96}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REJEITAR_NA_MALA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rejeitar Na Mala'
        GUID = '{A2502A36-42A8-4F0C-A574-A1C39EBBBEB7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REPRES_COMERCIAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Repres Comercial'
        GUID = '{7846F403-E86B-4A4D-805B-D7F8E67C62BD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RETEM_ICMS_FONTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Retem Icms Fonte'
        GUID = '{4B316D88-20AF-41E1-8188-10DF32AF0A08}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REVENDE_VEICULOS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Revende Veiculos'
        GUID = '{72D8C6D2-DFA6-4352-9C2E-56A35FD3ADA6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RG'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rg'
        GUID = '{1F46A39C-68D3-4B1E-BC56-8CBA11742AB0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SERVIDOR_PUBLICO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Servidor Publico'
        GUID = '{84CB9D0A-9D51-45F8-A76E-3E6849D80881}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SOBRENOME'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sobrenome'
        GUID = '{25C35CFE-AB5E-4C88-AFD8-4B2BB6F10572}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELE_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tele Contato'
        GUID = '{AA680FBC-A27D-4D7D-9C2B-DD8F23BC056F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELE_HORARIO_CONTATO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tele Hor'#225'rio Contato'
        GUID = '{5D0F1B49-1B4A-4E3A-8670-8ABA58D81FC1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_PADRAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Padr'#227'o'
        GUID = '{23465AC9-7A52-4DB2-956D-FA4042105032}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TERCEIRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Terceiro'
        GUID = '{349C50E4-5782-4A55-93C5-522C258C7D53}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CONTA_FORNECEDOR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Conta Fornecedor'
        GUID = '{1A7E023C-B11E-40B2-9C2E-1FFACBAD5FD7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CONTA_FORNECEDOR1'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Conta Fornecedor1'
        GUID = '{3397DA33-7AC7-43EC-B6D4-8C41965AF312}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TRATAMENTO_CLIENTE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tratamento Cliente'
        GUID = '{FC1B3A91-0168-4AAA-842F-2C4FE71A5B8B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCA_CARRO_MES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Troca Carro M'#234's'
        GUID = '{5F8E2934-0D57-4DB4-9075-C87F185A5352}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{5803AAB0-FD30-445A-8FE2-13BDBD5DCFF9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO_ALT_SERASA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Alt Serasa'
        GUID = '{1BA5E7A6-D81A-4C11-9220-AA320EE18B25}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO_LIB_BLOQ'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Lib Bloqueio'
        GUID = '{0298286F-3B7E-4890-96C1-7AEBC591BC8D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO_RESPONSAVEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Respons'#225'vel'
        GUID = '{2C0B93E5-A5CC-4B73-BA85-A82F021CDB01}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_COMPL_LIMITE_CREDITO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Complemento Limite Cr'#233'dito'
        GUID = '{32500464-5ECA-4345-A70A-2264C77DDBC3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_MINIMO_FATURA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor M'#237'nimo Fatura'
        GUID = '{8233E772-2E4A-4F6A-A681-1F8CBC931734}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENC_CART_MOTORISTA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Vencimento Cart Motorista'
        GUID = '{CEE12466-0303-4FEE-A94D-3E91AFCD7B0E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDA_COM_REQUISICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Venda com Requisi'#231#227'o'
        GUID = '{08446481-522E-4807-84E0-9CC6D912B3B4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDEDOR_RESPONSAVEL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vendedor Respons'#225'vel'
        GUID = '{E26C1193-E9F6-4F46-93BB-59C7EE4DE003}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZONA_FRANCA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zona Franca'
        GUID = '{7AD24EEC-ED9E-44E8-9F81-E031572B2BF8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Endere'#231'o Eletronico'
        GUID = '{F9230D69-7DBB-4502-8462-842F17C7F814}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_AUTOMATICA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Reserva Automatica'
        GUID = '{D384FC48-68B6-4CC5-B240-FC998FD28AAE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RESERVA_ADICIONAL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Reserva Adicional'
        GUID = '{5CDB71E5-8DA8-4E58-8A8E-17C6402B7841}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VP_AUTOMATICA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vp Automatica'
        GUID = '{1CEB840F-F167-4458-A0A2-252DEE1C12AA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_LIBERADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cau'#231#227'o Liberado'
        GUID = '{FDD7623A-BBE6-4A68-8DF6-FB956A86378B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_CNO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Cno'
        GUID = '{A9415454-5840-4894-A2B2-065924368E53}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_REG_ESPECIAL_TRIBUT'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Reg Especial Tribut'
        GUID = '{5E086023-0980-4797-9BF3-7081B69D26E2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_BP'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Bp'
        GUID = '{A9CBDB2E-33DD-41BB-898B-0A5F4DD8424F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DT_CONSULTA_CRIVO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Consulta Crivo'
        GUID = '{4E37621E-F62E-420B-8959-27E009A51479}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MIDIA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Midia'
        GUID = '{2A6335B0-D749-45FA-B71B-CFEA9642AEF2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_DIVERSO'
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;440025'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbParmSys: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{A5D9C7F7-C9E0-4256-B0A7-E3E50D6DF491}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{DD34D486-6F90-4A45-8B05-BBBE20693A11}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{B1F7A5AB-440F-4060-AC9A-8E5C2C9DF2B4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'PARM_SYS'
    Cursor = 'PARM_SYS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;43002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesSexo: TFTable
    FieldDefs = <
      item
        Name = 'COD_SEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Sexo'
        GUID = '{D5E77138-0D2B-4D8C-A255-E71DCA9C09C3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{EF173AC6-460C-4165-988E-77FC1B0AC871}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_SEXO'
    Cursor = 'CLIENTES_SEXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;43003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRegimeEspecialTributacao: TFTable
    FieldDefs = <
      item
        Name = 'ID_REGIME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Regime'
        GUID = '{5EAD6126-8397-48E2-8FD2-F83006A3720F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{482DE639-CC23-4D27-80C5-B60AA7F49230}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'REGIME_ESPECIAL_TRIBUTACAO'
    Cursor = 'REGIME_ESPECIAL_TRIBUTACAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbRapCliContato: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{9120BE4D-33D9-437C-918F-F5EED0124452}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        GUID = '{46D9393A-0B63-4FE0-8393-3356FEF103CA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Fone'
        GUID = '{291BBE74-9668-4287-A65A-3ACE12CEE9AB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_COMPRADOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Comprador'
        GUID = '{*************-49F4-929D-03F76E8029F2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CAD_RAP_CLI_CONTATO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaNbsPessoaJuridica: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        GUID = '{88C50F90-5BBD-41DA-AAB5-06B5A5AC0EA0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_RFB_DADOS_JURIDICOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Rfb Dados Juridicos'
        GUID = '{94C95A38-97EF-4DAA-8D0C-B8DF72F59B6C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RFB_PATH'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rfb Path'
        GUID = '{24AEDC40-AEBE-49AA-8EC0-BFC72CF4196D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNPJ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnpj'
        GUID = '{32729205-66B1-4D94-8CFD-00309A05D9FA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RFB_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Rfb Code'
        GUID = '{CE956845-0D50-405E-B913-9E470397AC80}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RAZAO_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Raz'#227'o Social'
        GUID = '{197FB68C-CBBC-48EA-B8F6-8FE9B79CFDF4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FANTASIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Fantasia'
        GUID = '{DC8F27FB-653B-44D0-9CD9-832803FBF0A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MATRIZ_FILIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Matriz Filial'
        GUID = '{07E261CC-48EC-413C-AAF3-FCDFAAF9E929}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Situa'#231#227'o Cadastral'
        GUID = '{933C32F6-D758-4426-AEAC-675D6448EB49}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Situa'#231#227'o Cadastral'
        GUID = '{930EA7F2-813C-486C-8C2F-84B02B5DD4EF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Motivo Situa'#231#227'o Cadastral'
        GUID = '{F92B9E4C-F534-4CB5-9C20-BB02886C81B9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SITUACAO_ESPECIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Situa'#231#227'o Especial'
        GUID = '{972C99FA-A02B-4F9E-8827-AE333D707224}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SITUACAO_ESPECIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Situa'#231#227'o Especial'
        GUID = '{EC3FD58B-2D84-4D03-B961-1925B7549223}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNAE_PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnae Principal'
        GUID = '{34525F1F-2DBE-430E-9DC1-76272E50C29D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NATUREZA_JURIDICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Natureza Juridica'
        GUID = '{D0735FE9-9425-4D3C-82E6-22FF3AB04CF4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ABERTURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Abertura'
        GUID = '{D19AAE64-527F-4A73-8AA8-5EE080D76DB9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        GUID = '{06C00AC8-5581-4173-B1D9-93190EBC5C85}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOGRADOURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Logradouro'
        GUID = '{BB2F6AFF-619E-4A70-8DAC-69613F0639E6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        GUID = '{5BB797A5-63FB-4371-9B8D-6CABAF79D56A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        GUID = '{8EE08995-BB91-45E6-9C59-0AD3FDAC88DC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        GUID = '{C5C8A088-0DF7-45DC-A4C9-C3430BE10E7A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio'
        GUID = '{E4BDF0ED-03EE-4AF4-8DA5-E0C220DAFAEE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{FC4571D5-A5B1-49E5-97BD-4DA2D71C6ED1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio Ibge'
        GUID = '{56272B2A-5510-428B-B173-E22DD0DCEBDD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ibge'
        GUID = '{2B22FE90-EF2B-4833-8113-E3AD770623AE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Ibge'
        GUID = '{23D6D0DD-73DB-409F-A1B3-CF9B7C47D5FA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone'
        GUID = '{B6C2389A-DD0F-46C3-95DF-0DAC9E14D302}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENDERECO_ELETRONICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Endere'#231'o Eletronico'
        GUID = '{7DED1760-D310-481E-B5A5-26037C000FBE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTE_FEDERATIVO_RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ente Federativo Respons'#225'vel'
        GUID = '{85D18139-4E24-456B-9EBC-870A5ACEC6A4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PORTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Porte'
        GUID = '{E653D376-6C92-4F22-B5A1-DF5F98845196}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QSA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qsa'
        GUID = '{AE17B356-DA9F-4895-924C-29219E25DB7A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QSA_MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qsa Mensagem'
        GUID = '{FD9C2709-E656-4161-831E-1ED74A4A5C17}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QSA_CAPITAL_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qsa Capital Social'
        GUID = '{8FEEBDF0-31AD-4B4E-BE4F-F6C262FFE11E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QSA_CAPITAL_SOCIAL_EXTENSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qsa Capital Social Extenso'
        GUID = '{13EE8C4E-A310-445F-85C1-BC962CD3848F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Emiss'#227'o'
        GUID = '{13BEF898-D191-4757-91A6-35F96396C0D8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Emiss'#227'o'
        GUID = '{8E7306A2-1843-4974-8056-828F9F684C2A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTINGENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contingencia'
        GUID = '{8732D151-75E4-4FE3-9574-818F67443C84}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_PESSOA_JURIDICA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;53006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaNbsPessoaFisica: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        GUID = '{E1B02118-5DA1-4F4F-8D09-12E6A3446858}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{96455EB0-8A3F-48FA-A844-74FD3C337539}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_RFB_DADOS_FISICOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Rfb Dados Fisicos'
        GUID = '{3969683A-3432-4454-B0FE-9B81F81B7FC4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RFB_PATH'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Rfb Path'
        GUID = '{845A0A21-A1A0-42A3-BF92-8D387045111B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RFB_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Rfb Code'
        GUID = '{72F3F922-ACAA-4048-9DAD-3A6A2E125E46}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        GUID = '{83E8DAC4-21C7-41D8-9551-3120BD1516A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CIVIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Civil'
        GUID = '{940CFEAB-7C96-4FB9-B723-02047042B590}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_NASCIMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Nascimento'
        GUID = '{D1CA46A9-7EB8-40F3-867F-E48F3825B8B2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Situa'#231#227'o Cadastral'
        GUID = '{562319B8-6EC6-43EF-9A0F-48A170E07D58}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INSCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Inscri'#231#227'o'
        GUID = '{3B50A9D0-8A9F-40A0-83C4-01502759E9D7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIGITO_VERIFICADOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Digito Verificador'
        GUID = '{0FF65B5C-9674-406C-8FA8-F9E01A641ECF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANO_OBITO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ano Obito'
        GUID = '{4D6C6276-2024-4E44-8084-D09151C72414}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Emiss'#227'o'
        GUID = '{DB80D2B8-C98E-4248-A5A7-1CD07CCE4691}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORA_EMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Hora Emiss'#227'o'
        GUID = '{208CF1A0-FA1F-435D-A6D7-102630CD3D22}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPROVANTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Comprovante'
        GUID = '{BE23F285-BBA4-4086-B95B-60FFA4C365AD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AUTENTICIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Autenticidade'
        GUID = '{38133D22-E763-43F5-A58A-8F4AB3824A19}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTINGENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contingencia'
        GUID = '{9FA3D427-F5A2-43B3-9AFB-C2BA025C31F8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_PESSOA_FISICA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;53007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaNbsSintegraSimples: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        GUID = '{F388DD6E-A65D-4C70-A7B1-D678C31561AD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SINTEGRA_SIMPLIFICADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Sintegra Simplificado'
        GUID = '{1F5F87A1-B2E8-4FC3-88EC-3D6CC3AC88DF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SINTEGRA_PATH'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sintegra Path'
        GUID = '{15528D5C-E0A4-49A5-A4A1-DBC625CC2722}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SINTEGRA_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Sintegra Code'
        GUID = '{56B1581B-F4A1-4D96-AEB4-B457A0245CE3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        GUID = '{B31C3F40-E46E-43A3-A0EA-C119092AD11A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Situa'#231#227'o Cadastral'
        GUID = '{CFD9E0D9-EC8E-4853-B730-D62D1FA5B134}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{0EEF07CA-A2E7-4540-A3EC-972B8C35BE64}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_FONTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Fonte'
        GUID = '{FC569A68-FB15-4F32-A718-7A6A1DFEA0FD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_SINTEGRA_SIMPLES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;53008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaNbsSintegraDados: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        GUID = '{7BC6051B-85D5-4054-B17A-9F8543863D63}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_SINTEGRA_DADOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Sintegra Dados'
        GUID = '{C5C05BA0-7CC8-41A7-9517-E642C19EF0F7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SINTEGRA_PATH'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sintegra Path'
        GUID = '{147F6991-456E-4067-AB57-6F4872BF8803}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        GUID = '{0B8E667A-1336-4096-8DBA-93AD587ED9D1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SINTEGRA_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Sintegra Code'
        GUID = '{03B855FF-D3C0-4C72-A937-9E5C4C3C21EC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Situa'#231#227'o Cadastral'
        GUID = '{F6C2B977-758C-4915-942C-54315E26619F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio Ibge'
        GUID = '{B421BB6B-48F2-4A10-B446-DFDD141B0682}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO_IBGE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo Ibge'
        GUID = '{DFA41317-4ABA-4284-8300-6426DE096E26}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SITUACAO_CADASTRAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Situa'#231#227'o Cadastral'
        GUID = '{1153957D-B9D6-4E67-BD69-E1A31BF2FC24}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COMPLEMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Complemento'
        GUID = '{84A54DC5-3D5A-4079-8CAD-541058A7928F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_BAIXA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Baixa'
        GUID = '{6C2BF7A8-B40B-4300-B6EF-2847CF703084}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        GUID = '{97A1F4AC-123C-4442-8C65-8CBC8865DD15}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LOGRADOURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Logradouro'
        GUID = '{4C6B3CDF-2C9D-4790-A40A-CCF35CADC6CF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_CADASTRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Cadastro'
        GUID = '{63890436-D7E0-4CE2-95DA-32CBF3627295}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CEP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cep'
        GUID = '{0E171AC4-0296-49F6-86F6-D5723B6F6240}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MUNICIPIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Municipio'
        GUID = '{F3A232DA-DA4D-4996-8B9A-D3568503EF42}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_IE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Ie'
        GUID = '{416CC6DC-39B3-4AF0-9F78-684DDAAB0BB4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_FANTASIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Fantasia'
        GUID = '{F0830A2A-1B2F-4C01-95F2-313DAC3A3A7B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNAE_PRINCIPAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnae Principal'
        GUID = '{E8EFFD27-0248-438A-834B-B2B4DE9A7D3B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INCLUSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Inclus'#227'o'
        GUID = '{F7B22F3A-6467-4536-A4A5-946A97705CC8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REGIME_APURACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Regime Apura'#231#227'o'
        GUID = '{E7496103-EEF9-4F12-8394-26A4331B06E0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{6FC392D3-ACC0-4D62-B29E-6C2C63CDDFA0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF_FONTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf Fonte'
        GUID = '{B870252F-D71A-41F6-911B-C53D11D92DE9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        GUID = '{7A739AEF-CAB5-4DDF-94B0-A88108CECCFB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_SINTEGRA_DADOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;53009'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaNbsSocios: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        GUID = '{E77E4A1E-AEAF-45A2-8E10-7516CE2BF71D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{D9A0D6CA-B966-407F-9EEB-71CD48AC8C20}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_RFB_DADOS_JURIDICOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Rfb Dados Juridicos'
        GUID = '{B9295429-CB03-4DFE-B403-91F33CA2E298}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_HIGIENIZADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Higienizado'
        GUID = '{6BE4DCFB-250E-4457-90C9-B8A232B0DC37}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUALIFICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qualifica'#231#227'o'
        GUID = '{03EBAD3F-2FA5-4104-9CE3-C343BD7666EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REPRESENTANTE_NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Representante Nome'
        GUID = '{9F38D430-82DF-45FA-B263-29DA0D9EDC0B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REPRESENTANTE_QUALIFICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Representante Qualifica'#231#227'o'
        GUID = '{CF16400E-342C-4355-8F50-3F06EFB2B935}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PAIS_ORIGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pais Origem'
        GUID = '{0384143F-C80B-45C1-8B28-6B924750A3A8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_SOCIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;530010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaNbsOutrasAtividades: TFTable
    FieldDefs = <
      item
        Name = 'ID_CONSULTA_NBS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Consulta Nbs'
        GUID = '{FB20F0DA-BA2D-4C6D-BB87-E45F97141601}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_RFB_DADOS_JURIDICOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Rfb Dados Juridicos'
        GUID = '{756782FD-EDA0-4408-A4D0-750DCC814777}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_RFB_OUTRAS_ATIVIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Rfb Outras Atividades'
        GUID = '{D603A72E-6769-47CD-B340-D47073DE4CEB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CNAE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cnae'
        GUID = '{5381F5D9-6A9A-472D-A18F-00CCF7CCFA5F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CONSULTA_NBS'
    Cursor = 'CONSULTA_NBS_OUTRAS_ATIVIDADES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;530011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteEnderecoInscricao: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{DAAC7488-0637-4624-BF36-A75C816F39BC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INSCRICAO_ESTADUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Inscri'#231#227'o Estadual'
        GUID = '{21EED9B3-F628-4339-B071-4217D9F6DDF4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ENDERECO_POR_INSCRICAO'
    Cursor = 'CLIENTE_ENDERECO_INSCRICAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;530012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidadesInscricao: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{218FA3A7-2525-4816-A88F-2D1C8E4D92A1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{B921EC18-BA93-44C9-A4EE-11A6A2D0D445}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{1A6DCB9E-CF5D-4C82-A1A9-23A76A1EF6CB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;530013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbSchemaAtual: TFTable
    FieldDefs = <
      item
        Name = 'SERVICE_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Service Name'
        GUID = '{29B39E21-7BE5-4F51-B374-F251360B918C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SCHEMA_NAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Schema Name'
        GUID = '{2805A1AE-D537-4442-96AB-E5FBD663C537}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SCHEMA_ATUAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;530014'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEscolaridade: TFTable
    FieldDefs = <
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        GUID = '{8103951F-0576-4C8D-878F-2725F9DA60FA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{C3D84BCE-E858-46F3-89E8-6C71523AFC03}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'ESCOLARIDADE'
    Cursor = 'ESCOLARIDADE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;13801'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbMidia: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo'
        GUID = '{B6165A99-E299-4341-B668-3A9BA9A91735}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_MIDIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Midia'
        GUID = '{A0C23E57-DE18-49BE-A27E-73AECDB3D689}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_MIDIA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '7000195;552017'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
