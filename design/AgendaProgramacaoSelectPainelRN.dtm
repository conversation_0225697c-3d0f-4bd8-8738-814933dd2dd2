object AgendaProgramacaoSelectPainelRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '503017'
  Height = 299
  Width = 442
  object tbTvTipoCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Tipo'
        GUID = '{954F7D1E-6C1F-4C26-8209-EB3598D12143}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{0AA62F3A-E4E7-45B2-AB9C-B4E401579D16}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_TV_TIPO_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '503017;50301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{6E7AD4EC-7B19-44C9-A426-536B2E4B5228}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        GUID = '{5902657C-7AF4-425D-A618-8FA2F99DAF53}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '503017;50302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTvParamUserCombo: TFTable
    FieldDefs = <
      item
        Name = 'COD_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Painel'
        GUID = '{72FEDACE-1032-48F3-9A86-4257CF53A2CD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{44D63CD8-5426-4A39-9EB9-E89D493D8D54}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'OS_TV_PARAM_USER_COMBO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '503017;50303'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
