object AssinaturaDigitalVerRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '45509'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbSolicitacoesAssinaturas: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{1902C2E4-0093-43B3-9C66-759CD6F209E0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{AD542EEA-D6A4-4FB5-B8CE-BC059B382EF0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QR_CODE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Qr Code'
        GUID = '{AE345095-9E1C-40F1-B910-82422283AF5A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Assinatura'
        GUID = '{D440F32C-52A1-4A92-96D9-FD99EF66CE1A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SIGNATARIO_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Signatario Tipo'
        GUID = '{FC2A7343-514A-4684-BA43-8BC9D2432690}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SIGNATARIO_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Signatario C'#243'd.'
        GUID = '{0D63B905-C848-4E87-983E-6E5FC2397E60}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{0EBD2FAE-0636-45D1-BE67-E7C911382268}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone'
        GUID = '{769AD87A-99EB-4B0C-B0F7-AC95E9A91164}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_ENVELOPE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Envelope'
        GUID = '{9F1CCA4C-3DC1-419D-9FA9-428C3EAA4DE5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAG_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tag Documento'
        GUID = '{C8EDFA82-84DA-4272-AA7E-F08464227417}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Codigo'
        GUID = '{EEF0AB0D-E699-4E40-9A6C-522C3B701667}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Codigo'
        GUID = '{85E182B2-A7E1-4128-BFA0-8F5CA8FDC72F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_ANDAMENTO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Andamento Assinatura'
        GUID = '{07E21B2B-A9F3-432D-B576-60B0EF6F49EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Assinatura'
        GUID = '{1D610006-293E-48BB-B322-88BA4470E40D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_DESTINATARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Destinatario'
        GUID = '{D03117CA-258F-41B1-A6FF-2E693D2BA2B2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'SOLICITACOES_ASSINATURAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '45509;45501'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
