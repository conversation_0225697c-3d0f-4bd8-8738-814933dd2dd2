object PesquisaAvancadaAgentesFuncaoRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '37501219'
  Left = 44
  Top = 158
  Height = 299
  Width = 442
  object tbDepartamentos: TFTable
    FieldDefs = <
      item
        Name = 'DPTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DPTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dpto Descri'#231#227'o Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_EMPRESAS_DEPARTAMENTOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '37501219;37502'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '37501219;37503'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDivisoes: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Divis'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIVISAO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Divis'#227'o Descri'#231#227'o Codigo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_EMPRESAS_DIVISOES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '37501219;37504'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
