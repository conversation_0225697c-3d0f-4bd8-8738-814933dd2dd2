object ClientesFlagsRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '310036'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object sc: TFSchema
    Tables = <
      item
        Table = tbClienteDiversoFlag
        GUID = '{9FAAE96C-BEB4-428D-9EE1-5FF4513AC72A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbClienteDiversoFlag: TFTable
    FieldDefs = <
      item
        Name = 'FATURAMENTO_LIVRE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Faturamento Livre'
        GUID = '{E7BBBC0B-CDF7-4046-8D69-60F80AE546C9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_PRECO_FABRICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Pre'#231'o Fabrica'
        GUID = '{5416F1C7-7C6A-48D3-AC37-A689FA5646D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_GARANTIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Garantia'
        GUID = '{1E92E689-D343-4A18-86DE-5FCD1C64D12C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VP_AUTOMATICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vp Automatica'
        GUID = '{23FADC64-24DD-42FA-9F62-F09DCD53AC72}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_AUTOMATICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Reserva Automatica'
        GUID = '{7D0F24D7-33B1-4B21-AD90-CD9D905F5ECE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RESERVA_ADICIONAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Tempo Reserva Adicional'
        GUID = '{187976DE-CEDE-4A44-9ABB-8713D4D53D55}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_FC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Fc'
        GUID = '{8EE2407F-6022-4779-A455-0F2D0BCF154C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Cobranca'
        GUID = '{D025752C-9368-4A31-9A02-D9F5338AFB72}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{185CE2F6-5393-4A60-9317-1FEF5F25BAC6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cgc'
        GUID = '{D4154920-3706-435A-8520-6EA731D73676}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_COB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Cob'
        GUID = '{58B60833-25B9-4DFB-B823-937357A62C2E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{043A77D2-3FB3-440A-9703-8D48CF3E5046}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDEDOR_RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vendedor Respons'#225'vel'
        GUID = '{D4B595CF-E742-492E-B5E4-86C37F416374}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CURVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Curva'
        GUID = '{EE675F4D-2F07-493D-9216-9A3C7ED6A052}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REPRESENTANTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Representante'
        GUID = '{AAFA38B1-5399-40E7-A6D8-38CB1FB0FBA8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_ATACADISTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Atacadista'
        GUID = '{AAB6E385-39E0-4428-9D01-CD5B6F32D8A6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CLIENTE_DIVERSO'
    TableName = 'CLIENTE_DIVERSO'
    Cursor = 'CLIENTE_DIVERSO_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31006'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesParamEmpFlag: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{D9B4846E-4C8B-4AEE-A912-2EF534372565}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{574E218D-CCDA-4F09-99CF-95B01FC2C009}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cgc'
        GUID = '{32DDF2CF-C769-4539-89C2-1DCC8EF72544}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FATURAMENTO_LIVRE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Faturamento Livre'
        GUID = '{AD00E600-1E57-4FBF-BED6-3774542C6DD1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CLIENTES_PARAM_EMP_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFormaCobrancaFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{C23907DE-8E8A-4C40-9414-2F50BD2EF6F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_COBRANCA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Cobranca'
        GUID = '{6B631373-EEEE-487B-A66D-A3C17FE483BC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{8EEC05B9-9879-4F62-8EBE-D162EB337C9F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CHAVE_COB'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Chave Cob'
        GUID = '{9F6B4CB3-C9DC-46BF-AB99-6D7274C3666D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_COBRANCA_DESCR_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Cobranca Descri'#231#227'o Codigo'
        GUID = '{13E8A4B3-7B9C-4C1C-87CD-9680C024240B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'FORMA_COBRANCA_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteTransportadoraFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_TRANSPORTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Transportadora'
        GUID = '{29AA4A9B-B36A-491B-8229-36C83099916C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{A4BFCFE5-70E3-4C6D-A4C5-A324934F76AB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cgc'
        GUID = '{8125E5B0-36B6-44FD-B55B-82EEFF571129}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{43DFC340-CD97-459A-96E2-112AC2E6C9EF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_INITCAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Initcap'
        GUID = '{B7021BF7-5540-4F9F-A411-E9302B1A68BF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_COD_TRANSPORTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o C'#243'd. Transportadora'
        GUID = '{11D84764-8B78-4831-824F-8585A4614E86}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_TRANSPORTADORA'
    Cursor = 'CLIENTE_TRANSPORTADORA_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;31009'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTransportadorasFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_TRANSPORTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Transportadora'
        GUID = '{5354DEBE-4791-4C5B-8A5D-85144CFAD6C1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{1A738913-7860-4636-9C85-6EE58275F548}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF_CGC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf Cgc'
        GUID = '{657BA18B-6973-484F-AE36-DC19768A2ED0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'TRANSPORTADORAS_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310010'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteFormaPgtoFlag: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{C0556BD2-3FEE-4BA4-8010-CCBD03D55159}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{1E71B7D3-88B9-4BC2-BCF5-3781DDCAAF21}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        GUID = '{5C87EF0B-9D32-4DD2-BBB3-5182065BA31A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        GUID = '{87614763-E3EE-4097-A183-02FF49CF6BC1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Departamento'
        GUID = '{2986D821-180A-4A2C-A7C1-5CC94429CBC3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        GUID = '{F521B92E-9273-4FF6-B741-606CDB17B854}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        GUID = '{3D6DBDAF-8D87-41F2-A11C-88A29E19B036}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento'
        GUID = '{7377252E-9DFF-426A-9067-6495191180F2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CODIGO_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Codigo Empresa'
        GUID = '{F954215E-1ACD-42F8-98DA-85A734FB887D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_CODIGO_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Codigo Departamento'
        GUID = '{29D0B2E8-F3F1-49F5-BABF-AC51E9C24A60}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo Forma Pagamento'
        GUID = '{2B905505-B618-456D-813C-956D1D438275}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO_CONDICAO_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo Condi'#231#227'o Pagamento'
        GUID = '{5C71402F-8D54-40FD-B5D2-68C364F04FF5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Exclusiva'
        GUID = '{CF2EC38E-3613-4B61-8D1A-F75FDEC54D03}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONDICAO_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Condi'#231#227'o Pagamento Exclusiva'
        GUID = '{CA8B3234-8EC6-46DE-B96D-942781D0E8A9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMA_PGTO_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310011'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesDescontosFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{DA4BE05E-8F5A-4973-8B8E-C1817FD3191E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Letra'
        GUID = '{1E5B0C54-E7FB-4A21-BCEE-67B237254FC7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PER_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Per Desconto'
        GUID = '{1DC88968-D396-431B-97AC-14C6790BF9C9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{D40B6742-45F6-4F55-A1C3-A95DF9370CC5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_DESCONTOS'
    Cursor = 'CLIENTES_DESCONTOS_FLAG'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbItensPerDescFlag: TFTable
    FieldDefs = <
      item
        Name = 'COD_MAX_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Max Desconto'
        GUID = '{2698DD89-891D-47BB-977B-BE4A0C4FACEF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{0266499F-643A-4E4F-A065-AD99261F583C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ITENS_PER_DESC_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310016'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbVendedorRespFlag: TFTable
    FieldDefs = <
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        GUID = '{725601CC-6D62-41FC-89B7-2E8B69593930}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{45677DB0-2FB2-4A53-A7E8-0D9495863C6C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAB_PRECO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tab Pre'#231'o'
        GUID = '{10E3E9F3-B727-4041-AE25-761D99DFCD6A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'VENDEDOR_RESP_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310017'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDadosJuridicosFlag: TFTable
    FieldDefs = <
      item
        Name = 'ID_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Segmento'
        GUID = '{38BC9CCF-5B9B-4620-BAC0-58A96EA42D3C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{BCDBFC39-1237-4F72-A007-B42E6D5917D7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{FCAF90B3-2F32-4A7E-99A7-0219CB0C4811}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGMENTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Segmento Descri'#231#227'o Codigo'
        GUID = '{C870BD4A-2E06-4C6C-9E9D-F1D4A284637B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'DADOS_JURIDICOS'
    Cursor = 'DADOS_JURIDICOS_FLAG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;310018'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbBuscaRepresentanteCliente: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{24384700-CC51-42A9-80EC-0C6DB112DDEE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Login'
        GUID = '{430DCB34-CBBB-4458-AB0E-77B20380328B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_REPRESENTANTE_CLIENTE'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaClienteFormasPagamento: TFTable
    FieldDefs = <
      item
        Name = 'FORMA_PGTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        GUID = '{E60F8993-CEBD-4016-ABDE-230E48C8319A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Descri'#231#227'o Codigo'
        GUID = '{4FB476DE-750C-4831-9D27-944D9AD150D6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{5D2173F1-A75E-4577-8224-10DB6E6FDBFA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        GUID = '{5E779009-28D9-4254-B800-2AF903E51C1D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        GUID = '{88E633E3-867A-49E0-88DB-0EABFFE95028}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Departamento Descri'#231#227'o Codigo'
        GUID = '{F7ED7DF4-7E19-4D0C-9DDD-14AB898945EA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{8C7EB07C-65C4-412F-945F-8649B77DA963}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FORMA_PGTO_EXCLUSIVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Forma Pagamento Exclusiva'
        GUID = '{82FB7690-BD86-4DDE-B5E8-56C72727834B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_CLIENTE_FORMAS_PAGAMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteFormasPgto: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{645D74ED-94A3-4A00-96B5-04D3D5EDF387}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        GUID = '{FF29089E-1E7E-4AAC-83A4-30DBE3FFC5C8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CONDICAO_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Condi'#231#227'o Pagamento'
        GUID = '{34400A79-9AD9-49E2-8D35-CB3DAA48FEA4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{8452891C-8E5A-4EC4-8062-E0BCBA8D8D44}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        GUID = '{E299D694-19FF-4344-8377-18789D1908C0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_FORMAS_PGTO'
    Cursor = 'CLIENTE_FORMAS_PGTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbDescEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{C914E1D2-906B-483C-AE75-B67303A992F4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Letra'
        GUID = '{21C9ACFF-4BD0-456A-B9C9-A04B90E14015}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{CE83E7C7-2D6C-41F8-9735-999FAA70C158}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PER_DESC'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Per Desconto'
        GUID = '{45D1A39A-DB68-49C7-9E54-6A1095A651B3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        GUID = '{1184591A-4093-4303-8EB9-4BFE6417DB38}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO_LETRA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        GUID = '{C92AEF77-16ED-41EB-9EE6-151D3C2910D8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_DESCONTOS_EMPRESA'
    Cursor = 'CLI_DESC_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;53008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasFiliaisSel: TFTable
    FieldDefs = <
      item
        Name = 'CODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{09154771-AC99-4195-8B72-3444919FA742}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOMEECODIGODAEMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nomeecodigodaempresa'
        GUID = '{A640C3D9-DF07-430B-9556-FA04322336BF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESA'
    Cursor = 'EMPRESAS_FILIAIS_SEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;530012'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClientesEspeciaisMargem: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{0F9425C2-FA1A-4020-B45F-************}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        GUID = '{1325A88C-0F47-4BAB-81B9-7F2DA1E0465C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_ESPECIAIS_MARGEM'
    Cursor = 'CLIENTES_ESPECIAIS_MARGEM'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;530013'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCliEspMargEmp: TFTable
    FieldDefs = <
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{855E4063-9115-4B92-8B39-A61BBE1D8555}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{9B799385-6B24-4466-A5A5-C97A79FB706B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MARGEM_MINIMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Margem Minima'
        GUID = '{BD0FB6A5-1059-4910-8703-E66F50CF6E94}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_NOME_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Nome Codigo'
        GUID = '{11D7949A-9598-43B1-AF71-E3DE7AB0B3AD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTES_ESPECIAIS_MARGEM_EMP'
    Cursor = 'BUSCA_CLI_ESP_MARG_EMP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;161014'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbListaClienteResponsavel: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{7AD52C48-A49C-414B-B28B-67EEBE2E5E14}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{412DE03D-9F4F-4642-814D-91815615205D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SISTEMA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Sistema'
        GUID = '{B333E9B7-305B-40CD-B6AB-5AB20ECCBD11}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROCESSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Processo'
        GUID = '{221502D1-**************-6C7D322D1271}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPERATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Temperatura'
        GUID = '{BE4949E3-9F9E-4E68-80CA-533470C8F70A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel'
        GUID = '{3E032C0E-F8E1-409A-BCF4-E54914BFFFC7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SISTEMA_STR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sistema Str'
        GUID = '{C506CE0C-9AF3-44F0-821B-F944A0CD9DAA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PROCESSO_STR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Processo Str'
        GUID = '{FBAB25E6-AD18-437B-AF3D-F213B6D15E54}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPERATURA_STR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Temperatura Str'
        GUID = '{32B2D050-8A03-4962-A3FB-1DD9E7EDBC8E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vendedor'
        GUID = '{F4ED0A55-37DF-44A4-9F33-0D9F2782BEB5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESAS_NOME_INITCAP_COD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresas Nome Initcap C'#243'd.'
        GUID = '{839EE0BA-6265-4EE5-937F-BFD09FA042B1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LISTA_CLIENTE_RESPONSAVEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;28401'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteSegmento: TFTable
    FieldDefs = <
      item
        Name = 'ID_SEGMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Segmento'
        GUID = '{74DE7737-6B1B-4284-B48B-4AE88AAF69F0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{502E6EB0-EA0C-434B-A150-2C83EC0E544D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEGMENTO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Segmento Descri'#231#227'o Codigo'
        GUID = '{91578ED8-2CA4-457F-A7FC-F856356FF956}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_SEGMENTO'
    Cursor = 'BUSCA_CLIENTE_SEGMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '310036;507015'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
