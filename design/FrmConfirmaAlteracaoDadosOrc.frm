object FrmConfirmaAlteracaoDadosOrc: TFForm
  Left = 130
  Top = 130
  Caption = 'Altera'#231#227'o Dados Or'#231'amento'
  ClientHeight = 224
  ClientWidth = 436
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600599'
  ShortcutKeys = <>
  InterfaceRN = 'ConfirmaAlteracaoDadosOrcRN'
  Access = False
  ChangedProp = 
    'FrmConfirmaAlteracaoDadosOrc.Width;'#13#10'FrmConfirmaAlteracaoDadosOr' +
    'c.Height;'#13#10
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxMainAltDadosOrc: TFVBox
    Left = 0
    Top = 0
    Width = 436
    Height = 224
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitLeft = 360
    ExplicitTop = 68
    ExplicitWidth = 185
    ExplicitHeight = 41
    object FHBox11: TFHBox
      Left = 0
      Top = 0
      Width = 429
      Height = 61
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnVoltar: TFButton
        Left = 0
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Voltar Tela'
        Align = alLeft
        Caption = 'Voltar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnVoltarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
          FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
          290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
          086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
          152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
          F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
          86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
          B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
          AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
          EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
          AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
          736BB6EF9B710000000049454E44AE426082}
        ImageId = 700081
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnAceitar: TFButton
        Left = 60
        Top = 0
        Width = 60
        Height = 56
        Hint = 'Aceitar'
        Align = alLeft
        Caption = 'Confirma'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnAceitarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000001674944415478DAED943B4B04311485930561AD5C0459B1D7DA7E1515
          0BB1157F80D682EDEA228A0A3E5AC1DE5EECB5F1857FC0567F802F10AD1404E3
          1727338430492662A7170E974C72CFD97B72B352540C29E538E9CC2C279452E7
          95EAFE05FEB08043180BAF60B0838A22C16EA2164544A256950A405A23F551FC
          101029C8D96F929E587F060538D84F9A036BA00E26293A2D11B1C9F3EFEF601D
          1CB0776F71CA6EF214D8054396DE2558E5F0856397B06DE1DB2869138C59B537
          A00D4EB480B236B4251D7004C96BEC7E9CEE7B4833601B34ED0E7201DD41A7CC
          C744A19AB16AC515D0F16875F09248DC301D6CF93A70E34ADF81CE887D7848BB
          482D7307239E335E013BF6C11EB835EB41B008162A745649208F67937B13ACFB
          1ED369B0637ED96F841ED32570EC3EB401D2BCC826A09E48AA1FDA86C81EDA5D
          C1E9694B8FDA305806B311E24391CDFE75F4AFC2235666616101A46FC1FA140F
          8C85CAB6205A9322F093F80220039B3DD610218B0000000049454E44AE426082}
        ImageId = 700088
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object gridDetailValores: TFGridPanel
      Left = 0
      Top = 62
      Width = 421
      Height = 57
      Caption = 'gridDetailValores'
      ColumnCollection = <
        item
          Value = 20.000500012500310000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 20.000500012500310000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 20.000500012500310000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 19.998499962499060000
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          Value = 20.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      ControlCollection = <
        item
          Column = 0
          Control = FLabel1
          Row = 1
        end
        item
          Column = 1
          Control = FLabel2
          Row = 0
        end
        item
          Column = 2
          Control = FLabel3
          Row = 0
        end
        item
          Column = 4
          Control = FLabel4
          Row = 0
        end
        item
          Column = 3
          Control = FLabel5
          Row = 0
        end
        item
          Column = 0
          Control = FLabel6
          Row = 2
        end
        item
          Column = 1
          Control = lblTotServAntes
          Row = 1
        end
        item
          Column = 2
          Control = lblTotPecasAntes
          Row = 1
        end
        item
          Column = 3
          Control = lblDescAntes
          Row = 1
        end
        item
          Column = 4
          Control = lblTotAntes
          Row = 1
        end
        item
          Column = 1
          Control = lblTotServDepois
          Row = 2
        end
        item
          Column = 2
          Control = lblTotPecasDepois
          Row = 2
        end
        item
          Column = 3
          Control = lblDescDepois
          Row = 2
        end
        item
          Column = 4
          Control = lblTotDepois
          Row = 2
        end>
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      RowCollection = <
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          WOwner = FrInterno
          WOrigem = EhNone
        end
        item
          SizeStyle = ssAuto
          Value = 100.000000000000000000
          WOwner = FrInterno
          WOrigem = EhNone
        end>
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      AllRowFlex = False
      WOwner = FrInterno
      WOrigem = EhNone
      ColumnTabOrder = False
      object FLabel1: TFLabel
        Left = 25
        Top = 14
        Width = 59
        Height = 13
        Align = alRight
        Alignment = taRightJustify
        Caption = 'Valor Antes:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 43
        ExplicitTop = 1
      end
      object FLabel2: TFLabel
        Left = 119
        Top = 1
        Width = 48
        Height = 13
        Align = alRight
        Alignment = taRightJustify
        Caption = 'Servi'#231'os'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 15174738
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 105
      end
      object FLabel3: TFLabel
        Left = 217
        Top = 1
        Width = 33
        Height = 13
        Align = alRight
        Alignment = taRightJustify
        Caption = 'Pe'#231'as'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 15174738
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 194
      end
      object FLabel4: TFLabel
        Left = 391
        Top = 1
        Width = 29
        Height = 13
        Align = alRight
        Alignment = taRightJustify
        Caption = 'Total'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 15174738
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 364
      end
      object FLabel5: TFLabel
        Left = 274
        Top = 1
        Width = 59
        Height = 13
        Align = alRight
        Alignment = taRightJustify
        Caption = 'Descontos'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clRed
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 266
      end
      object FLabel6: TFLabel
        Left = 24
        Top = 27
        Width = 60
        Height = 13
        Align = alRight
        Alignment = taRightJustify
        Caption = 'Valor Agora:'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 15
      end
      object lblTotServAntes: TFLabel
        Left = 125
        Top = 14
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 129
      end
      object lblTotPecasAntes: TFLabel
        Left = 208
        Top = 14
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 212
      end
      object lblDescAntes: TFLabel
        Left = 291
        Top = 14
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clRed
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 295
      end
      object lblTotAntes: TFLabel
        Left = 378
        Top = 14
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 382
      end
      object lblTotServDepois: TFLabel
        Left = 125
        Top = 27
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 81
        ExplicitTop = 1
      end
      object lblTotPecasDepois: TFLabel
        Left = 208
        Top = 27
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 81
        ExplicitTop = 1
      end
      object lblDescDepois: TFLabel
        Left = 291
        Top = 27
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clRed
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 81
        ExplicitTop = 1
      end
      object lblTotDepois: TFLabel
        Left = 378
        Top = 27
        Width = 42
        Height = 13
        Align = alRight
        Caption = 'R$ 0,00'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = [fsBold]
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taVerticalCenter
        Mask = 'R$ ,##0.00'
        WordBreak = False
        MaskType = mtText
        ExplicitLeft = 81
        ExplicitTop = 1
      end
    end
    object chkZerarDesc: TFCheckBox
      Left = 0
      Top = 120
      Width = 297
      Height = 17
      Caption = 'Zerar descontos, se houver. (N'#227'o se aplica a promo'#231#227'o)'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 2
      OnCheck = chkZerarDescCheck
      ReadOnly = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taAlignTop
    end
    object chkEnivarOrc: TFCheckBox
      Left = 0
      Top = 138
      Width = 309
      Height = 17
      Caption = 'Enviar novo or'#231'amento ao Cliente.'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 3
      ReadOnly = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taAlignTop
    end
  end
  object tbConsultaTotal: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_TOTAL_BRUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Total Bruto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_TOTAL_DESCONTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Total Desconto'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OS_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Os L'#237'quido'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS'
    Cursor = 'OS_CONSULTA_TOTAL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600599;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
