object CadClienteContatoRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600220'
  Height = 299
  Width = 442
  object tbClienteContato: TFTable
    FieldDefs = <
      item
        Name = 'USUARIO_CAD_NOME_COMPL_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio Cad Nome Complemento Login'
        GUID = '{41C9903C-D202-4290-9843-BDB8348BDD18}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Contato'
        GUID = '{DA8C434B-9375-44E9-B920-1A9FD2AD2706}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CPF'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Cpf'
        GUID = '{54FD0D34-0402-4A81-B3A1-8D1B03860216}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PREFIXO_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Prefixo Cel'
        GUID = '{BED6AD59-FA5B-4CD6-A485-3B7A7406DD9F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        GUID = '{4E9D3EAB-A373-4D3A-B3B4-FB16F598ED81}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Email'
        GUID = '{9C7A1C31-0364-4DB3-8AFC-0BDE2A97B298}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_CADASTRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Cadastro'
        GUID = '{342449CF-F1F9-4D25-A2AF-050A96A0DB07}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESP_GARANTIA_MONTADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resp Garantia Montadora'
        GUID = '{278D9FFD-766A-4560-AF91-E4E37A61EDB3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_COMPRADOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Comprador'
        GUID = '{EA266201-AE94-4784-89A4-1F0BFF665B1A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ULTIMA_ATUALIZACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data '#218'ltima Atualiza'#231#227'o'
        GUID = '{1CA9FBB2-F38F-4F8E-ACA9-A8F2B8B35193}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUAR_ULT_ATU_NOME_COMPL_LOGIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usuar Ult Atu Nome Complemento Login'
        GUID = '{7DBE207B-D402-4E98-A685-9236468D9EA7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_PESQUISA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Pesquisa'
        GUID = '{DF5C5F7B-B946-41E3-BE70-93859AFF265F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AREA_CONTATO_DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Area Contato Descri'#231#227'o Codigo'
        GUID = '{1B759FD6-BC40-4D98-B0F1-09F3CB21F59C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CLIENTE_CONTATO'
    Cursor = 'BUSCA_CLIENTE_CONTATO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600220;44802'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbClienteContatoTipo: TFTable
    FieldDefs = <
      item
        Name = 'AREA_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Area Contato'
        GUID = '{106CA85A-97EF-4A71-B2EE-2C3A73EA076F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo'
        GUID = '{EF308D61-F77F-4D7E-97DE-7F8B23A30FE0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{7BCC66EC-D19F-4B71-9C74-1AA0DDDD90E2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_CLIENTE_CONTATO_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600220;44803'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbFiltroClienteContatoTipo: TFTable
    FieldDefs = <
      item
        Name = 'AREA_CONTATO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Area Contato'
        GUID = '{9D438626-0E68-4944-BACA-C805EDDC5073}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO_CODIGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o Codigo'
        GUID = '{995DBEA1-0EB0-4ABC-9C99-73B1970D4ED7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        GUID = '{3720A6BA-ED86-4EC2-876E-3672C2A7CDED}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BUSCA_CLIENTE_CONTATO_TIPO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600220;44804'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
