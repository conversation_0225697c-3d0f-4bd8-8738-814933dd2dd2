object PesquisaRapidaClienteRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '298021'
  Left = 321
  Top = 158
  Height = 299
  Width = 442
  object tbLeadsConsultaClientes: TFTable
    FieldDefs = <
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Exibir'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE_EXIBIR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_CONSULTA_CLIENTES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298021;29801'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 8
    Top = 12
  end
end
