object TipoDocumentoRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '43702'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object tbTipoDocumento: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIPO_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tipo Documento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ATIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ativo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGATORIO_AVALIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Obrigatorio Avalia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODULO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modulo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PERMITIR_OBRIGAR_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Permitir Obrigar Assinatura'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSULTOR_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Consultor Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRODUTIVO_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Produtivo Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'GERENTE_ASSINA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Gerente Assina'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIPO_DOCUMENTO'
    Cursor = 'CRM_TIPO_DOCUMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '43702;43701'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbTipoDocumentoID: TFTable
    FieldDefs = <
      item
        Name = 'ID_TIPO_DOCUMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tipo Documento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_TIPO_DOCUMENTO'
    Cursor = 'CRM_TIPO_DOCUMENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '43702;43702'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
