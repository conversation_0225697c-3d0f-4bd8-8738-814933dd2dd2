object FrmCentralPendenciaService: TFForm
  Left = 321
  Top = 163
  ActiveControl = vboxPrincipal
  BorderIcons = [biSystemMenu, biMinimize]
  Caption = 'Central de Pend'#234'ncia'
  ClientHeight = 662
  ClientWidth = 984
  Color = clBtnFace
  Constraints.MaxHeight = 5000
  Constraints.MaxWidth = 5000
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600480'
  ShortcutKeys = <>
  InterfaceRN = 'CentralPendenciaServiceRN'
  Access = False
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 984
    Height = 662
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 978
      Height = 170
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox1: TFVBox
        Left = 0
        Top = 0
        Width = 791
        Height = 165
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftMin
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FVBox2: TFVBox
          Left = 0
          Top = 0
          Width = 787
          Height = 76
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          DesignSize = (
            783
            72)
          object cbEmpresaUsuario: TFCombo
            Left = 0
            Top = 0
            Width = 782
            Height = 21
            LookupTable = tbConsultaComboEmpresa
            LookupKey = 'COD_EMPRESA'
            LookupDesc = 'EMPRESA'
            Flex = True
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Selecione'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = False
            HideClearButtonOnNullValue = False
            OnChange = cbEmpresaUsuarioChange
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
          object hBoxFiltroOrc: TFHBox
            Left = 0
            Top = 22
            Width = 579
            Height = 30
            Anchors = []
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hbPendTodos: TFHBox
              Left = 0
              Top = 0
              Width = 89
              Height = 23
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Color = clSilver
              Padding.Top = 2
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentBackground = False
              TabOrder = 0
              OnClick = hbPendTodosClick
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox6: TFVBox
                Left = 0
                Top = 0
                Width = 16
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblOrcTodos: TFLabel
                Left = 16
                Top = 0
                Width = 34
                Height = 14
                Caption = 'Todos'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FVBox7: TFVBox
                Left = 50
                Top = 0
                Width = 12
                Height = 19
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
            object hbPendServicos: TFHBox
              Left = 89
              Top = 0
              Width = 114
              Height = 23
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 2
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentBackground = False
              TabOrder = 1
              OnClick = hbPendServicosClick
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox75: TFVBox
                Left = 0
                Top = 0
                Width = 16
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblOrcCliente: TFLabel
                Left = 16
                Top = 0
                Width = 44
                Height = 14
                Caption = 'Servi'#231'os'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FVBox77: TFVBox
                Left = 60
                Top = 0
                Width = 12
                Height = 19
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
            object hbPendPecas: TFHBox
              Left = 203
              Top = 0
              Width = 125
              Height = 23
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 2
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              ParentBackground = False
              TabOrder = 2
              OnClick = hbPendPecasClick
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 0
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object FVBox86: TFVBox
                Left = 0
                Top = 0
                Width = 16
                Height = 20
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblOrcGaratia: TFLabel
                Left = 16
                Top = 0
                Width = 31
                Height = 14
                Caption = 'Pe'#231'as'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -12
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object FVBox97: TFVBox
                Left = 47
                Top = 0
                Width = 12
                Height = 19
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
          end
        end
        object hBoxBtnTotais: TFHBox
          Left = 0
          Top = 77
          Width = 589
          Height = 70
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 5
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object FVBox3: TFVBox
            Left = 0
            Top = 0
            Width = 9
            Height = 41
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object FPanelButtonTodos: TFPanelButton
            Left = 9
            Top = 0
            Width = 80
            Height = 60
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stBoxShadow
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            OnClick = FPanelButtonTodosClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BoxShadowConfig.HorizontalLength = 0
            BoxShadowConfig.VerticalLength = 7
            BoxShadowConfig.BlurRadius = 10
            BoxShadowConfig.SpreadRadius = -5
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Toggle = True
            ToggleColor = clSilver
            WOwner = FrInterno
            WOrigem = EhNone
            object FPanelButtonTodosItems: TFFastDesignCmpItems
              object FPanelButtonItem1: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'FPanelButtonItem1'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
                ItemType = itVBox
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ItemAlign = iaLeft
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 5
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
                object lblTotalTodas: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = '0'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -21
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
                object FPanelButtonItem3: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = 'Todas'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
              end
            end
          end
          object FPanelButtonRecusadas: TFPanelButton
            Left = 89
            Top = 0
            Width = 80
            Height = 60
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stBoxShadow
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            OnClick = FPanelButtonRecusadasClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BoxShadowConfig.HorizontalLength = 0
            BoxShadowConfig.VerticalLength = 7
            BoxShadowConfig.BlurRadius = 10
            BoxShadowConfig.SpreadRadius = -5
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Toggle = True
            ToggleColor = clSilver
            WOwner = FrInterno
            WOrigem = EhNone
            object FPanelButtonRecusadasItems: TFFastDesignCmpItems
              object FPanelButtonItem2: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'FPanelButtonItem1'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{DB343B3A-F1B8-4548-A4E9-5908746938CC}'
                ItemType = itVBox
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ItemAlign = iaLeft
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 5
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
                object lblTotalRecusadas: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = '0'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{8FB2EAA8-D8C6-4596-B601-2CB12E8FF213}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clRed
                  Font.Height = -21
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
                object FPanelButtonItem13: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = 'Recusadas'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{0683FAD0-4311-4868-9A38-0825AE367504}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
              end
            end
          end
          object FPanelButtonEmAnalise: TFPanelButton
            Left = 169
            Top = 0
            Width = 80
            Height = 60
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stBoxShadow
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            OnClick = FPanelButtonEmAnaliseClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BoxShadowConfig.HorizontalLength = 0
            BoxShadowConfig.VerticalLength = 7
            BoxShadowConfig.BlurRadius = 10
            BoxShadowConfig.SpreadRadius = -5
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Toggle = True
            ToggleColor = clSilver
            WOwner = FrInterno
            WOrigem = EhNone
            object FPanelButtonEmAnaliseItems: TFFastDesignCmpItems
              object FPanelButtonItem5: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'FPanelButtonItem5'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{9C151997-2D02-4834-9AB8-BDCC7D8C90A9}'
                ItemType = itVBox
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ItemAlign = iaLeft
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 5
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
                object lblTotaEmAnalise: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = '0'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{2EC4CF58-C402-4C00-B75E-085DBE400292}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clYellow
                  Font.Height = -21
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
                object FPanelButtonItem15: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = 'Em An'#225'lise'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{F4146C76-89DD-41C5-9A11-D996D3F07E41}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
              end
            end
          end
          object FPanelButtonLiberadas: TFPanelButton
            Left = 249
            Top = 0
            Width = 80
            Height = 60
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stBoxShadow
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 4
            OnClick = FPanelButtonLiberadasClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            BoxShadowConfig.HorizontalLength = 0
            BoxShadowConfig.VerticalLength = 7
            BoxShadowConfig.BlurRadius = 10
            BoxShadowConfig.SpreadRadius = -5
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            Toggle = True
            ToggleColor = clSilver
            WOwner = FrInterno
            WOrigem = EhNone
            object FPanelButtonLiberadasItems: TFFastDesignCmpItems
              object FPanelButtonItem4: TFPanelButtonItem
                AutoHotkeys = maManual
                Caption = 'FPanelButtonItem4'
                WOwner = FrInterno
                WOrigem = EhNone
                GUID = '{D06973C5-E276-483B-A9FF-5EB2E0342AA9}'
                ItemType = itVBox
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -13
                Font.Name = 'Tahoma'
                Font.Style = []
                ItemAlign = iaLeft
                Flex = False
                ItemVAlign = ivaTop
                Padding.Top = 5
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                BorderStyle = stNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                BevelInner = bvNone
                BevelKind = bkTile
                BevelOuter = bvNone
                Color = clBtnFace
                WordBreak = False
                object lblTotalLiberadas: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = '0'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{13FDBE32-8032-4047-B934-32AFEDEA2208}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clGreen
                  Font.Height = -21
                  Font.Name = 'Tahoma'
                  Font.Style = [fsBold]
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
                object FPanelButtonItem36: TFPanelButtonItem
                  AutoHotkeys = maManual
                  Caption = 'Aprovadas'
                  WOwner = FrInterno
                  WOrigem = EhNone
                  GUID = '{370044A6-D98C-46FE-9A32-B0B0B9FE75D2}'
                  ItemType = itLabel
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ItemAlign = iaCenter
                  Flex = False
                  ItemVAlign = ivaTop
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  BorderStyle = stNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  BevelInner = bvNone
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  Color = clBtnFace
                  WordBreak = False
                end
              end
            end
          end
          object FVBox9: TFVBox
            Left = 329
            Top = 0
            Width = 9
            Height = 41
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 5
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
        object lblMaisFiltros: TFLabel
          Left = 0
          Top = 148
          Width = 60
          Height = 13
          Caption = 'lblMaisFiltros'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlue
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          Visible = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object FHBox2: TFHBox
        Left = 791
        Top = 0
        Width = 165
        Height = 76
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnMaisFiltros: TFButton
          Left = 0
          Top = 0
          Width = 80
          Height = 70
          Caption = '++ Filtros'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          OnClick = btnMaisFiltrosClick
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnPesquisar: TFButton
          Left = 80
          Top = 0
          Width = 80
          Height = 70
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = btnPesquisarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D49484452000000300000003008060000005702F9
            870000049F4944415478DAD5997B88555514C6F74D2B4B2A8334CA174CA2948A
            266449983286CA8835A9A8898F081FA3282821697FF410411D3432115F043A51
            A9F91C6DA66470CAFE50844C7C20063E41F281685ABED26EBF8FBD2F5C2E7367
            F63E73EFDC33073E16F7CCACBDBEEF9CB3D75E7BED8489702593C94730C5A004
            1481F60E4F814BE082C371B0031C4E2412C928B11ABA1281C4DFC0948161E0C9
            00D7B3603B588E90734D2E00E22F611683E169B7EF835FC111F0A7C35FA01D78
            0E3C0F5E073DD27CEE822FC122845CCBBB0088B774C4E78087DCEDDDE01BF023
            24AE7B882F72C2A780EEEEB6C897E1BF396F0208DC06B3090C76B7F6817904DD
            1F2510E3B5C08C070B402777FB33FD66CCFF722AC03DB52AD00DDC035341452E
            262263B7C27C01A6B95BDF83098C7D372702DC933FE0C82BA39432F881C612AF
            23CE0C63E783DECC7AF07E940794C81854DFFC0FC67E3622FF6AAEB34646BCB7
            8C4DB3E23197584B1B2B40037C606CB618988F275F87880F8D4D147AFA4389B9
            279200067A1173CCD86CF31E036DC8377917571CBE0563C11FA007B1FF8D22A0
            D2D874A76C33305F2B671611CF624E81D66006B1570509702BEC2FEE5EBFA6F8
            74EA10A194FA31B80CBAC0E1668800BDC277C16E1C87FB38E641804A13250C65
            C189F0F8DA4B802BCCAE185BDB8CC3F1BB420870222A3013C076788CF0153014
            5B6D6C6DD32E57354A4401A38C5DD86E8367E072CB4780169359A01687E24291
            77029EC05C050F8312F854FB085071A6F258A5EEEC420A70224E62BA8299F059
            E923E0776C6F630BB5253110F0336680B125F7473E0294B6DA82493854C44080
            92881635158F937C04A86C502652D1B6330602561B5BA956C2E76D1F01E7B11D
            C1541CD6C540C036CC3B602D7CA635F4FF12A00DCA6BE0131C16C44040101F09
            D8821D09D6E0501603015A8DB5639B0C9FAF7C04A46A906338F42C30797DCAE7
            DD4FAF9A4C025EC61E72BF5FC0E97401056897A6DCAF0E47079FBD72C2D5E367
            4067301BA7E50514A09557A5CD6A784CF7F14955A39F1BDB3A5127AD17CE0F0A
            40BE0BE604D0B67688EFCE2C25404F5F4BF8A3A609776319023662C680DF405F
            DF564BFA8EAC1C33D7D849D48D01EE3421F93E8EB8AE6262D7FAFAA60B78DAD8
            6D9DACF7379803F2AA4095FBD5B5AB22EEB010FFCCAEC46863BB71BA82F6A611
            C9AB81B015941A5B46BF42CC339105B84153EB8226F20806ACCC23F9BDC6569E
            DA4C0D22D6BED071125906D65BD0EE489D89F9A03C975D0AF7D9A8F22D75B7B4
            F3EA4F8C43A16365EB8D2A1BAD01A9725619426BC4A51C90EFE3C8EB9BD79357
            EFF571633BD66F868AA8AF3BADBFA94B57EEFEEF1FB04C20C88D08C495E7171A
            9B2A75E99BD7C6FD6F50636CF20816D1E0010781D5275D61EC364F97CE047601
            CD8D9FEAEBDFB8DA466D1AF5400719BB48E952E77B666AC2BAB7124984EF098D
            36D993C1A7C69EC0A42EB5004522DB094DA78CA1444A4DDCBD75C4882422F48C
            4C934F134F1B0ED52C8F79B85D34B603AD33B29AFA56D8282282046404D3C453
            0A4C3FA56CE308A79F521E0C398109151159403EAF1011B114102222B6027C45
            C45A808F88D80B684844B310509F88662320AB8842936AA4888BCD4E409A089D
            671FFD1F719BF90A58A66F7A0000000049454E44AE426082}
          ImageId = 7000252
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
    end
    object FHBox3: TFHBox
      Left = 0
      Top = 171
      Width = 978
      Height = 300
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object gridPendencia: TFGrid
        Left = 0
        Top = 0
        Width = 972
        Height = 290
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Table = tbConsultaPendenciaOrcGrid
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Paging.Enabled = True
        Paging.PageSize = 0
        Paging.DbPaging = False
        FrozenColumns = 0
        ShowFooter = False
        ShowHeader = True
        MultiSelection = False
        Grouping.Enabled = False
        Grouping.Expanded = False
        Grouping.ShowFooter = False
        Crosstab.Enabled = False
        Crosstab.GroupType = cgtConcat
        EnablePopup = False
        WOwner = FrInterno
        WOrigem = EhNone
        EditionEnabled = False
        AuxColumnHeaders = <>
        NoBorder = False
        ActionButtons.BtnAccept = False
        ActionButtons.BtnView = False
        ActionButtons.BtnEdit = False
        ActionButtons.BtnDelete = False
        ActionButtons.BtnInLineEdit = False
        CustomActionButtons = <>
        ActionColumn.Title = 'A'#231#245'es'
        ActionColumn.Width = 100
        ActionColumn.TextAlign = taCenter
        ActionColumn.Visible = True
        Columns = <
          item
            Expanded = False
            Font = <>
            Width = 35
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <
              item
                Expression = '*'
                EvalType = etExpression
                GUID = '{9FEBF47D-0BCC-4D15-BCCA-3B8E40C61CAE}'
                WOwner = FrInterno
                WOrigem = EhNone
                ImageId = 7000213
                OnClick = 'ClickImageAdicao         '
                Color = clBlack
              end>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{DF7B68E0-2CBC-4654-B204-5C305A8BA502}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'NUMERO'
            Font = <
              item
                Expression = '*'
                EvalType = etExpression
                GUID = '{F5A43CAF-3924-4A8A-9469-40CD59C42CFF}'
                WOwner = FrInterno
                WOrigem = EhNone
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clBlue
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
              end>
            Title.Caption = 'O.S/ORC.'
            Width = 100
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{8408983B-8846-4E05-8F89-125BFDD69224}'
            WOwner = FrInterno
            WOrigem = EhNone
            OnClick = gridPendenciaColumns1Click
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'PENDENCIA'
            Font = <>
            Title.Caption = 'Pend'#234'ncia'
            Width = 180
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = True
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{09B58DE6-8052-4B0E-9D3B-30BAD160BF35}'
            WOwner = FrWizard
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'NOME_QUEM_REQUISITOU'
            Font = <>
            Title.Caption = 'Quem Requisitou'
            Width = 162
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{68FBE1CD-D9D5-42A1-B101-FCE72A741F57}'
            WOwner = FrInterno
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end
          item
            Expanded = False
            FieldName = 'ST'
            Font = <>
            Title.Caption = 'Status'
            Width = 130
            Visible = True
            Precision = 0
            TextAlign = taLeft
            FieldType = ftString
            FlexRatio = 0
            Sort = False
            ImageHeader = 0
            Wrap = False
            Flex = False
            Colors = <>
            Images = <>
            Masks = <>
            CharCase = ccNormal
            BlobConfig.MimeType = bmtText
            BlobConfig.ShowType = btImageViewer
            ShowLabel = True
            Editor.EditType = etTFString
            Editor.Precision = 0
            Editor.Step = 0
            Editor.MaxLength = 100
            Editor.LookupFilterKey = 0
            Editor.LookupFilterDesc = 0
            Editor.PopupHeight = 400
            Editor.PopupWidth = 400
            Editor.CharCase = ccNormal
            Editor.LookupColumns = <>
            Editor.Enabled = False
            Editor.ReadOnly = False
            Editor.Filter = False
            Editor.ShowClearButton = False
            CheckedValue = 'S'
            UncheckedValue = 'N'
            HiperLink = False
            GUID = '{36FEB9FC-D88E-4863-AC85-C48048F3030C}'
            WOwner = FrWizard
            WOrigem = EhNone
            EditorConstraint.CheckWhen = cwImmediate
            EditorConstraint.CheckType = ctExpression
            EditorConstraint.FocusOnError = False
            EditorConstraint.EnableUI = True
            EditorConstraint.Enabled = False
            EditorConstraint.FormCheck = True
            Empty = False
            MobileOpts.ShowMobile = False
            MobileOpts.Order = 0
            BoxSize = 0
            ImageSrcType = istSource
            IconReverseDirection = False
            FooterConfig.ColSpan = 0
            FooterConfig.TextAlign = taLeft
            FooterConfig.Enabled = False
            HeaderTextAlign = taLeft
            Priority = 0
          end>
      end
    end
    object lblMensagem: TFLabel
      Left = 0
      Top = 472
      Width = 61
      Height = 13
      Caption = 'lblMensagem'
      Color = clBtnFace
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clRed
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentColor = False
      ParentFont = False
      Visible = False
      WOwner = FrInterno
      WOrigem = EhNone
      VerticalAlignment = taVerticalCenter
      WordBreak = False
      MaskType = mtText
    end
    object FHBox4: TFHBox
      Left = 0
      Top = 486
      Width = 978
      Height = 106
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 5
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 0
      Flex.Vflex = ftMin
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FVBox8: TFVBox
        Left = 0
        Top = 0
        Width = 183
        Height = 99
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object mmObs: TFMemo
          Left = 0
          Top = 0
          Width = 179
          Height = 94
          CharCase = ccNormal
          Enabled = False
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Lines.Strings = (
            'mmObs')
          Maxlength = 0
          ParentFont = False
          TabOrder = 0
          FieldName = 'OBS_FIM'
          Table = tbConsultaPendenciaOrcGrid
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          WOwner = FrInterno
          WOrigem = EhNone
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          Required = False
        end
      end
      object FVBox4: TFVBox
        Left = 183
        Top = 0
        Width = 20
        Height = 99
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object FVBox10: TFVBox
        Left = 203
        Top = 0
        Width = 96
        Height = 85
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 5
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnAprovar: TFButton
          Left = 0
          Top = 0
          Width = 79
          Height = 33
          Caption = 'Aprovar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          OnClick = btnAprovarClick
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnRecusarReabrir: TFButton
          Left = 0
          Top = 34
          Width = 79
          Height = 33
          Caption = 'Reprovar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = btnRecusarReabrirClick
          ImageId = 0
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
    end
  end
  object tbConsultaComboEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_COMBO_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbConsultaPendenciaOrcGrid: TFTable
    FieldDefs = <
      item
        Name = 'NUMERO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'St'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DDD_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ddd Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TELEFONE_CEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Telefone Cel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS_ORIGINAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os Original'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tp'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESEJADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desejado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_FIM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VALOR_APROVADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Mostrar Valor Aprovado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIXAR_MANUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Baixar Manual'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL_ESPECIFICO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel Especifico'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_ESPECIFICA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Fun'#231#227'o Especifica'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_REQUISITOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Requisitou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_QUEM_REQUISITOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CONSULTA_PENDENCIA_ORC_GRID'
    MaxRowCount = 0
    OnAfterScroll = tbConsultaPendenciaOrcGridAfterScroll
    OnMaxRow = tbConsultaPendenciaOrcGridMaxRow
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object sc: TFSchema
    Tables = <
      item
        Table = tbPendenciaEvento
        GUID = '{ABD1E238-50C9-47DB-9BE5-B8BF9FFCBCA9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbPendenciaEvento: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PENDENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_REQUISITOU'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Requisitou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_ENCERROU'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Encerrou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_INICIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data In'#237'cio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_FIM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_INICIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o In'#237'cio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_FIM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Fim'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_DESEJADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Desejado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_APROVADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor Aprovado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBS_PENDENCIA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o Pendencia'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_PENDENCIA_EVENTO'
    TableName = 'CRM_PENDENCIA_EVENTO'
    Cursor = 'CRM_PENDENCIA_EVENTO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46004'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsResponsavel: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESPONSAVEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Respons'#225'vel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_RESPONSAVEL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600480;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
