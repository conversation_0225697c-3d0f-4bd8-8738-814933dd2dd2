object FrmAgrupaPagamentos: TFForm
  Left = 44
  Top = 158
  ActiveControl = vBoxAgrupaPagamentos
  Caption = 'Agrupa Pagamentos'
  ClientHeight = 652
  ClientWidth = 1021
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FFormCreate
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '298020'
  ShortcutKeys = <>
  InterfaceRN = 'AgrupaPagamentosRN'
  Access = False
  ChangedProp = 
    'FrmAgrupaPagamentos.Width;'#13#10'FrmAgrupaPagamentos.Height;'#13#10#13#10'FrmAg' +
    'rupaPagamentos.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxAgrupaPagamentos: TFVBox
    Left = 0
    Top = 0
    Width = 1021
    Height = 652
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = True
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object vBoxTopoFitros: TFVBox
      Left = 0
      Top = 0
      Width = 1007
      Height = 194
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object vBoxTopo: TFVBox
        Left = 0
        Top = 0
        Width = 101
        Height = 6
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
      end
      object hBoxTopoBotoes: TFHBox
        Left = 0
        Top = 7
        Width = 996
        Height = 57
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxTopoBotoesSeparador: TFVBox
          Left = 0
          Top = 0
          Width = 10
          Height = 52
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object btnVoltar: TFButton
          Left = 10
          Top = 0
          Width = 60
          Height = 54
          Hint = 'Voltar Tela'
          Align = alLeft
          Caption = 'Voltar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 1
          OnClick = btnVoltarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
            FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
            290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
            086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
            152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
            F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
            86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
            B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
            AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
            EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
            AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
            736BB6EF9B710000000049454E44AE426082}
          ImageId = 700081
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
      end
      object hBoxTopoFiltros1: TFHBox
        Left = 0
        Top = 65
        Width = 996
        Height = 61
        Align = alTop
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxDiv1: TFVBox
          Left = 0
          Top = 0
          Width = 10
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxEmpresa: TFVBox
          Left = 10
          Top = 0
          Width = 168
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxDiv2: TFVBox
            Left = 0
            Top = 0
            Width = 118
            Height = 8
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblEmpresa: TFLabel
            Left = 0
            Top = 9
            Width = 45
            Height = 13
            Caption = 'Empresa:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object cbbComboEmpresa: TFCombo
            Left = 0
            Top = 23
            Width = 161
            Height = 21
            Hint = 'Empresas'
            LookupTable = tbLeadsEmpresasUsuarios
            FieldName = 'COD_EMPRESA'
            LookupKey = 'COD_EMPRESA'
            LookupDesc = 'EMPRESA'
            Flex = True
            ReadOnly = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Prompt = 'Selecione'
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            ClearOnDelKey = True
            UseClearButton = False
            HideClearButtonOnNullValue = False
            Colors = <>
            Images = <>
            Masks = <>
            Fonts = <>
            MultiSelection = False
            IconReverseDirection = False
          end
        end
        object vBoxDiv7: TFVBox
          Left = 178
          Top = 0
          Width = 5
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxNrAgrupamento: TFVBox
          Left = 183
          Top = 0
          Width = 108
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxDiv3: TFVBox
            Left = 0
            Top = 0
            Width = 101
            Height = 8
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblNrAgrupamento: TFLabel
            Left = 0
            Top = 9
            Width = 84
            Height = 13
            Caption = 'N'#186' Agrupamento:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtNumeroAgrupamento: TFString
            Left = 0
            Top = 23
            Width = 93
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
        object vBoxDiv8: TFVBox
          Left = 291
          Top = 0
          Width = 5
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxDataInicial: TFVBox
          Left = 296
          Top = 0
          Width = 125
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 5
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxDiv4: TFVBox
            Left = 0
            Top = 0
            Width = 93
            Height = 8
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblDataInicial: TFLabel
            Left = 0
            Top = 9
            Width = 57
            Height = 13
            Caption = 'Data Inicial:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDataInicial: TFDate
            Left = 0
            Top = 23
            Width = 120
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Format = 'dd/MM/yyyy'
            ShowCheckBox = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ShowTime = False
          end
        end
        object vBoxDiv9: TFVBox
          Left = 421
          Top = 0
          Width = 5
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 6
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxDataFinal: TFVBox
          Left = 426
          Top = 0
          Width = 125
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 7
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxDiv5: TFVBox
            Left = 0
            Top = 0
            Width = 98
            Height = 8
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object lblDataFinal: TFLabel
            Left = 0
            Top = 9
            Width = 52
            Height = 13
            Caption = 'Data Final:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtDataFinal: TFDate
            Left = 0
            Top = 23
            Width = 120
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            Format = 'dd/MM/yyyy'
            ShowCheckBox = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            ShowTime = False
          end
        end
        object vBoxDiv6: TFVBox
          Left = 551
          Top = 0
          Width = 10
          Height = 56
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 8
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object hBoxTopoFiltros2: TFHBox
        Left = 0
        Top = 127
        Width = 996
        Height = 56
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 3
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object vBoxDiv10: TFVBox
          Left = 0
          Top = 0
          Width = 10
          Height = 52
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxCpfCnpj: TFVBox
          Left = 10
          Top = 0
          Width = 282
          Height = 52
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblCpfCnpj: TFLabel
            Left = 0
            Top = 0
            Width = 52
            Height = 13
            Caption = 'CPF/CNPJ:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtCpfCnpj: TFString
            Left = 0
            Top = 14
            Width = 274
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 0
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            OnEnter = edtCpfCnpjEnter
            OnExit = edtCpfCnpjExit
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
        object vBoxDiv11: TFVBox
          Left = 292
          Top = 0
          Width = 5
          Height = 50
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxCliente: TFVBox
          Left = 297
          Top = 0
          Width = 168
          Height = 52
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object lblNomeCliente: TFLabel
            Left = 0
            Top = 0
            Width = 37
            Height = 13
            Caption = 'Cliente:'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taVerticalCenter
            WordBreak = False
            MaskType = mtText
          end
          object edtNomeCliente: TFString
            Left = 0
            Top = 14
            Width = 160
            Height = 24
            TabOrder = 0
            AccessLevel = 0
            Flex = True
            WOwner = FrInterno
            WOrigem = EhNone
            Required = False
            Constraint.CheckWhen = cwImmediate
            Constraint.CheckType = ctExpression
            Constraint.FocusOnError = False
            Constraint.EnableUI = True
            Constraint.Enabled = False
            Constraint.FormCheck = True
            IconDirection = idLeft
            CharCase = ccNormal
            Pwd = False
            Maxlength = 0
            Enabled = False
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -13
            Font.Name = 'Tahoma'
            Font.Style = []
            SaveLiteralCharacter = False
            TextAlign = taLeft
          end
        end
        object vBoxDiv12: TFVBox
          Left = 465
          Top = 0
          Width = 5
          Height = 50
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 4
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxBtnPesquisarCliente: TFVBox
          Left = 470
          Top = 0
          Width = 28
          Height = 52
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 5
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxDiv15: TFVBox
            Left = 0
            Top = 0
            Width = 26
            Height = 15
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object btnPesquisarCliente: TFButton
            Left = 0
            Top = 16
            Width = 26
            Height = 28
            Hint = 'Pesquisa Cliente'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 1
            OnClick = btnPesquisarClienteClick
            PngImage.Data = {
              89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
              F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
              782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
              B9B0DBF5D30000000049454E44AE426082}
            ImageId = 0
            WOwner = FrInterno
            WOrigem = EhNone
            Color = clBtnFace
            Access = False
            IconClass = 'external-link'
            IconReverseDirection = False
          end
        end
        object vBoxDiv13: TFVBox
          Left = 498
          Top = 0
          Width = 5
          Height = 50
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 6
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object vBoxBtnPesquisar: TFVBox
          Left = 503
          Top = 0
          Width = 86
          Height = 52
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 7
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object vBoxDiv16: TFVBox
            Left = 0
            Top = 0
            Width = 57
            Height = 15
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object btnPesquisar: TFButton
            Left = 0
            Top = 16
            Width = 82
            Height = 28
            Caption = 'Pesquisar'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 1
            OnClick = btnPesquisarClick
            PngImage.Data = {
              89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
              F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
              782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
              B9B0DBF5D30000000049454E44AE426082}
            ImageId = 0
            WOwner = FrInterno
            WOrigem = EhNone
            Color = clBtnFace
            Access = False
            IconClass = 'search'
            IconReverseDirection = False
          end
        end
        object vBoxDiv14: TFVBox
          Left = 589
          Top = 0
          Width = 10
          Height = 52
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 8
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
    end
    object pgcAgrupaPagamentos: TFPageControl
      Left = 0
      Top = 195
      Width = 1007
      Height = 442
      ActivePage = tabPagamentosAgrupados
      Align = alClient
      TabOrder = 1
      TabPosition = tpTop
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      WOwner = FrInterno
      WOrigem = EhNone
      RenderStyle = rsTabbed
      object tabOrcamentosPreNota: TFTabsheet
        Caption = 'Or'#231'amentos/Pr'#233'-Notas'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxOrcamentosPreNota: TFVBox
          Left = 0
          Top = 0
          Width = 999
          Height = 414
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 8
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = True
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxBotoesOrcamentos: TFHBox
            Left = 0
            Top = 0
            Width = 977
            Height = 26
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 5
            Padding.Right = 5
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object hBoxSelOrcPreNotas: TFHBox
              Left = 0
              Top = 0
              Width = 154
              Height = 22
              AutoWrap = False
              BevelInner = bvRaised
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stRaised
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              OnClick = hBoxSelOrcPreNotasClick
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 50
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 5
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 80
              VAlign = tvMiddle
              BorderRadius.TopLeft = 10
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 10
              object separador1: TFHBox
                Left = 1
                Top = 1
                Width = 10
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblSelOrcPreNotas: TFLabel
                Left = 11
                Top = 1
                Width = 120
                Height = 13
                Caption = 'Selecione os Or'#231'amentos'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object separador2: TFHBox
                Left = 131
                Top = 1
                Width = 10
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
            object hBoxOrcPreNotasAgrupadas: TFHBox
              Left = 154
              Top = 0
              Width = 154
              Height = 22
              AutoWrap = False
              BevelInner = bvRaised
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stRaised
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              OnClick = hBoxOrcPreNotasAgrupadasClick
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvMiddle
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object separador3: TFHBox
                Left = 1
                Top = 1
                Width = 10
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblOrcPreNotasAgrupadas: TFLabel
                Left = 11
                Top = 1
                Width = 123
                Height = 13
                Caption = 'Or'#231'amentos Selecionados'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object separador4: TFHBox
                Left = 134
                Top = 1
                Width = 10
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
            object hBoxFormasPagamento: TFHBox
              Left = 308
              Top = 0
              Width = 140
              Height = 22
              AutoWrap = False
              BevelInner = bvRaised
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stRaised
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              OnClick = hBoxFormasPagamentoClick
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvMiddle
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 10
              BorderRadius.BottomRight = 10
              BorderRadius.BottomLeft = 0
              object separador5: TFHBox
                Left = 1
                Top = 1
                Width = 10
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object lblFormasPagamento: TFLabel
                Left = 11
                Top = 1
                Width = 107
                Height = 13
                Caption = 'Formas de Pagamento'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
              object separador6: TFHBox
                Left = 118
                Top = 1
                Width = 10
                Height = 10
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
          end
          object separadorTopo: TFHBox
            Left = 0
            Top = 27
            Width = 973
            Height = 8
            Align = alTop
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
          object pgcAgrupamentoDePagamentos: TFPageControl
            Left = 0
            Top = 36
            Width = 988
            Height = 359
            ActivePage = tabOrcPreNotasSelecionadas
            Align = alClient
            TabOrder = 2
            TabPosition = tpTop
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            RenderStyle = rsCard
            object tabOrcPreNotas: TFTabsheet
              Caption = 'Selecionar Or'#231'amentos e PreNotas'
              Visible = True
              Closable = False
              WOwner = FrInterno
              WOrigem = EhNone
              ExplicitLeft = 0
              ExplicitTop = 0
              ExplicitWidth = 0
              ExplicitHeight = 0
              object vBoxOrcPreNotas: TFVBox
                Left = 0
                Top = 0
                Width = 980
                Height = 330
                Align = alClient
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = True
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hBoxTopoOrcNotas: TFHBox
                  Left = 0
                  Top = 0
                  Width = 973
                  Height = 32
                  Align = alTop
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 10
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblTituloOrcNotasAgrupadas: TFLabel
                    Left = 0
                    Top = 0
                    Width = 221
                    Height = 13
                    Caption = 'Or'#231'amentos e Pr'#233'-Notas a serem Agrupadas: '
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object separador23: TFHBox
                    Left = 221
                    Top = 0
                    Width = 5
                    Height = 10
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object btnSelecao: TFIconClass
                    Left = 226
                    Top = 0
                    Width = 22
                    Height = 22
                    Hint = 'Marcar/Desmarcar Todos'
                    Picture.Data = {
                      07544269746D6170C6070000424DC60700000000000036000000280000001600
                      0000160000000100200000000000900700000000000000000000000000000000
                      0000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D4000000000000000000000000000000000000000000000000000000
                      00000000000000000000000000000000000000000000C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000
                      0000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D4000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000
                      000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000000000000000C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D4000000000000000000C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000
                      0000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D4000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000
                      000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000000000000000C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D4000000000000000000C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000
                      0000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D4000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000
                      000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000000000000000C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D4000000000000000000C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D4000000
                      0000000000000000000000000000000000000000000000000000000000000000
                      0000000000000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000000000000000
                      0000000000000000000000000000000000000000000000000000000000000000
                      0000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                      D400C8D0D400C8D0D400C8D0D400C8D0D400}
                    OnClick = btnSelecaoClick
                    IconClass = 'square-o'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Size = 22
                    Color = clTeal
                  end
                  object separador22: TFHBox
                    Left = 242
                    Top = 0
                    Width = 6
                    Height = 10
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object btnAddOrcNotas: TFButton
                    Left = 248
                    Top = 0
                    Width = 78
                    Height = 28
                    Caption = 'Adicionar'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 2
                    OnClick = btnAddOrcNotasClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                      782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                      B9B0DBF5D30000000049454E44AE426082}
                    ImageId = 0
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconClass = 'plus-circle'
                    IconReverseDirection = False
                  end
                  object separador51: TFHBox
                    Left = 326
                    Top = 0
                    Width = 10
                    Height = 10
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 3
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object hBoxOrcPreNotasParaAgrupar: TFHBox
                  Left = 0
                  Top = 33
                  Width = 972
                  Height = 262
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object separador19: TFVBox
                    Left = 0
                    Top = 0
                    Width = 10
                    Height = 251
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxOrcPreNotasParaAgrupar: TFVBox
                    Left = 10
                    Top = 0
                    Width = 941
                    Height = 249
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object gridOrcPreNotasParaAgrupar: TFGrid
                      Left = 0
                      Top = 0
                      Width = 930
                      Height = 224
                      TabOrder = 0
                      TitleFont.Charset = DEFAULT_CHARSET
                      TitleFont.Color = clWindowText
                      TitleFont.Height = -12
                      TitleFont.Name = 'Tahoma'
                      TitleFont.Style = []
                      Table = tbOrcPreNotasParaAgrupar
                      Flex.Vflex = ftTrue
                      Flex.Hflex = ftTrue
                      Paging.Enabled = False
                      Paging.PageSize = 0
                      Paging.DbPaging = False
                      FrozenColumns = 0
                      ShowFooter = True
                      ShowHeader = True
                      MultiSelection = False
                      Grouping.Enabled = False
                      Grouping.Expanded = False
                      Grouping.ShowFooter = False
                      Crosstab.Enabled = False
                      Crosstab.GroupType = cgtConcat
                      EnablePopup = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditionEnabled = False
                      AuxColumnHeaders = <>
                      ContextMenu = FPopupMenuSelecaoOrcamentosPreNotas
                      NoBorder = False
                      ActionButtons.BtnAccept = False
                      ActionButtons.BtnView = False
                      ActionButtons.BtnEdit = False
                      ActionButtons.BtnDelete = False
                      ActionButtons.BtnInLineEdit = False
                      CustomActionButtons = <>
                      Columns = <
                        item
                          Expanded = False
                          Font = <>
                          Title.Caption = '#'
                          Width = 60
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FooterExpression = '"Total:"'
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = 'SEL='#39'S'#39
                              EvalType = etExpression
                              GUID = '{3A4B896D-4EBC-4C00-95BA-5320B3AD93E2}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 310010
                              Color = clBlack
                            end
                            item
                              Expression = 'SEL='#39'N'#39
                              EvalType = etExpression
                              GUID = '{6E3B10CF-EB84-40D1-AB27-CFF9F1DA150B}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 310011
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{6D498089-5225-4E0B-8D0A-4E43FADF1758}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          OnClick = selecionaOrcPreNotasParaAgrupar
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_EMPRESA'
                          Font = <>
                          Title.Caption = 'C'#243'd. Empresa'
                          Width = 110
                          Visible = False
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{205CC81C-617B-4B82-ADE1-B25C3539D877}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_ORC_MAPA'
                          Font = <>
                          Title.Caption = 'Orc. Mapa'
                          Width = 120
                          Visible = True
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{A7F6A751-F1A0-460F-96E6-1999FB44D14B}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'SALDO_PARA_AGRUPAR'
                          Font = <>
                          Title.Caption = 'Total Adiantamento'
                          Width = 140
                          Visible = True
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FooterExpression = 
                            'new String("R$ "+tbOrcPreNotasParaAgrupar.compute("SUM","SALDO_P' +
                            'ARA_AGRUPAR").asDecimal()+"")'
                          FlexRatio = 0
                          Sort = True
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <
                            item
                              Expression = '*'
                              EvalType = etExpression
                              GUID = '{A5D56AB5-7474-48C4-BF07-55D507E06284}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              Mask = 'R$ ,##0.00'
                              PadLength = 0
                              PadDirection = pdNone
                              MaskType = mtDecimal
                            end>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{A1C6331D-355C-4A9B-BFAF-6C1158441E42}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'VLR_AGRUPADO_ANT'
                          Font = <>
                          Title.Caption = 'Valor_agrupador Ant'
                          Width = 150
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{DDEDE572-EE29-492B-9BFE-9FC81EF27A49}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_CLIENTE'
                          Font = <>
                          Title.Caption = 'Cod. Cliente'
                          Width = 150
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{662F5CF2-6A21-4CA6-9F9E-7ED2B9A94D9A}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'NOME'
                          Font = <>
                          Title.Caption = 'Nome'
                          Width = 213
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{6F167339-E5A3-4605-B475-334F76447316}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end>
                    end
                  end
                  object separador20: TFVBox
                    Left = 951
                    Top = 0
                    Width = 10
                    Height = 251
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object separador21: TFHBox
                  Left = 0
                  Top = 296
                  Width = 973
                  Height = 10
                  Align = alBottom
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
            object tabOrcPreNotasSelecionadas: TFTabsheet
              Caption = 'Or'#231'amentos e Pre-Notas Selecionadas'
              Visible = True
              Closable = False
              WOwner = FrInterno
              WOrigem = EhNone
              object vBoxOrcPreNotasSelecionadas: TFVBox
                Left = 0
                Top = 0
                Width = 980
                Height = 330
                Align = alClient
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = True
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object hBoxTopoOrcNotasAgrupadas: TFHBox
                  Left = 0
                  Top = 0
                  Width = 973
                  Height = 32
                  Align = alTop
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 10
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblTitOrcNotasAgrupadas: TFLabel
                    Left = 0
                    Top = 0
                    Width = 177
                    Height = 13
                    Caption = 'Or'#231'amentos e Pr'#233'-Notas Agrupadas:'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                  object separador49: TFHBox
                    Left = 177
                    Top = 0
                    Width = 6
                    Height = 10
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object btnRemoverTodosItens: TFButton
                    Left = 183
                    Top = 0
                    Width = 26
                    Height = 28
                    Hint = 'Remover Todos'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 1
                    OnClick = btnRemoverTodosItensClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                      782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                      B9B0DBF5D30000000049454E44AE426082}
                    ImageId = 0
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconClass = 'trash'
                    IconReverseDirection = False
                  end
                  object separador24: TFHBox
                    Left = 209
                    Top = 0
                    Width = 5
                    Height = 10
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object btnDefinirFormasPagamento: TFButton
                    Left = 214
                    Top = 0
                    Width = 167
                    Height = 28
                    Hint = 'Definir Formas Pagamento'
                    Caption = 'Definir Formas Pagamento'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    TabOrder = 3
                    OnClick = btnDefinirFormasPagamentoClick
                    PngImage.Data = {
                      89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                      F8000000384944415478DA63FC0F040C34048CA3160C5E0B18191949360C9B51
                      782D20C573B8D48F5A306AC1A805A3168C5A402A20C9026A81A16F010046E3B9
                      B9B0DBF5D30000000049454E44AE426082}
                    ImageId = 0
                    WOwner = FrInterno
                    WOrigem = EhNone
                    Color = clBtnFace
                    Access = False
                    IconClass = 'money'
                    IconReverseDirection = False
                  end
                  object separador25: TFHBox
                    Left = 381
                    Top = 0
                    Width = 10
                    Height = 10
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 4
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    VAlign = tvTop
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object hBoxOrcNotasSelecionadas: TFHBox
                  Left = 0
                  Top = 33
                  Width = 964
                  Height = 271
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object separador26: TFVBox
                    Left = 0
                    Top = 0
                    Width = 10
                    Height = 251
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxOrcNotasSelecionadas: TFVBox
                    Left = 10
                    Top = 0
                    Width = 929
                    Height = 249
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object gridOrcPreNotasSelecionadas: TFGrid
                      Left = 0
                      Top = 0
                      Width = 907
                      Height = 224
                      TabOrder = 0
                      TitleFont.Charset = DEFAULT_CHARSET
                      TitleFont.Color = clWindowText
                      TitleFont.Height = -12
                      TitleFont.Name = 'Tahoma'
                      TitleFont.Style = []
                      Table = tbLeadsPgtoAgrupado
                      Flex.Vflex = ftTrue
                      Flex.Hflex = ftTrue
                      Paging.Enabled = False
                      Paging.PageSize = 0
                      Paging.DbPaging = False
                      FrozenColumns = 0
                      ShowFooter = True
                      ShowHeader = True
                      MultiSelection = False
                      Grouping.Enabled = False
                      Grouping.Expanded = False
                      Grouping.ShowFooter = False
                      Crosstab.Enabled = False
                      Crosstab.GroupType = cgtConcat
                      EnablePopup = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditionEnabled = False
                      AuxColumnHeaders = <>
                      NoBorder = False
                      ActionButtons.BtnAccept = False
                      ActionButtons.BtnView = False
                      ActionButtons.BtnEdit = False
                      ActionButtons.BtnDelete = False
                      ActionButtons.BtnInLineEdit = False
                      CustomActionButtons = <>
                      Columns = <
                        item
                          Expanded = False
                          Font = <>
                          Title.Caption = '#'
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = 'SEL='#39'S'#39
                              EvalType = etExpression
                              GUID = '{015FD281-A0A2-496C-84B0-9762FD1C7F9B}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 310010
                              Color = clBlack
                            end
                            item
                              Expression = 'SEL='#39'N'#39
                              EvalType = etExpression
                              GUID = '{36C8E9C5-70C3-4FBD-A3D0-D144D47CC775}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 310011
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{F2B8BDDC-E55C-4071-B72A-634DA5F0CB55}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'ID_PAGAMENTO'
                          Font = <>
                          Title.Caption = 'Id. Pagamento'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FooterExpression = '"Totais:"'
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{6F167339-E5A3-4605-B475-334F76447316}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_EMPRESA'
                          Font = <>
                          Title.Caption = 'C'#243'd. Empresa'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FooterExpression = '"Total:"'
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{F93B969F-CCD1-4784-A2E2-CE31B2BD2044}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'NUMERO_OS'
                          Font = <>
                          Title.Caption = 'N'#250'mero Os'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{A16FBF66-63B0-4B78-9C72-38EC15241EA2}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_ORC_MAPA'
                          Font = <>
                          Title.Caption = 'N'#176' Or'#231'amento'
                          Width = 100
                          Visible = True
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{02B19F75-1C9B-4BC9-9132-B8A56A95DC4B}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_CLIENTE'
                          Font = <>
                          Title.Caption = 'C'#243'd. Cliente'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftDecimal
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{F60022BE-59E2-4BE6-8C2E-214EA9532751}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'TIPO'
                          Font = <>
                          Title.Caption = 'Tipo'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{7CEF13EB-2248-4AAF-A80E-CB8EADC417EA}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'VALOR'
                          Font = <>
                          Title.Caption = 'Valor'
                          Width = 100
                          Visible = True
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FooterExpression = 
                            'new String("R$ "+tbLeadsPgtoAgrupado.compute("SUM","VALOR").asDe' +
                            'cimal()+"")'
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <
                            item
                              Expression = '*'
                              EvalType = etExpression
                              GUID = '{DF584CCC-270F-437D-A751-0E12AD0E659A}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              Mask = 'R$ ,##0.00'
                              PadLength = 0
                              PadDirection = pdNone
                              MaskType = mtDecimal
                            end>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{9727FF1E-2B62-4368-855A-B09AF39ADCED}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          Font = <>
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = '*'
                              Hint = 'Remover Item'
                              EvalType = etExpression
                              GUID = '{D16580D5-0797-4860-8625-2AA0E4CDED11}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 700095
                              OnClick = 'removeItemAgrupadoGrid'
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{B4572222-9B94-4D65-98C8-29F6A09BD177}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end>
                    end
                  end
                  object separador27: TFVBox
                    Left = 939
                    Top = 0
                    Width = 10
                    Height = 251
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object separador28: TFHBox
                  Left = 0
                  Top = 305
                  Width = 973
                  Height = 10
                  Align = alBottom
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
            object tabFormasDePagamento: TFTabsheet
              Caption = 'Formas de Pagamento'
              Visible = True
              Closable = False
              WOwner = FrInterno
              WOrigem = EhNone
              ExplicitLeft = 0
              ExplicitTop = 0
              ExplicitWidth = 0
              ExplicitHeight = 0
              object vBoxFormasDePagamento: TFVBox
                Left = 0
                Top = 0
                Width = 980
                Height = 330
                Align = alClient
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Scrollable = True
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object vBoxTopoFormasDePagamento: TFVBox
                  Left = 0
                  Top = 0
                  Width = 970
                  Height = 20
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 10
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object lblTituloParamAdiantamento: TFLabel
                    Left = 0
                    Top = 0
                    Width = 173
                    Height = 13
                    Caption = 'Par'#226'metros Adiantamento c /Cart'#227'o'
                    Font.Charset = DEFAULT_CHARSET
                    Font.Color = clWindowText
                    Font.Height = -11
                    Font.Name = 'Tahoma'
                    Font.Style = []
                    ParentFont = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    VerticalAlignment = taVerticalCenter
                    WordBreak = False
                    MaskType = mtText
                  end
                end
                object hBoxParametrosAdiantamento: TFHBox
                  Left = 0
                  Top = 21
                  Width = 971
                  Height = 50
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 1
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object separador30: TFVBox
                    Left = 0
                    Top = 0
                    Width = 10
                    Height = 45
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxTipoAdiantamento: TFVBox
                    Left = 10
                    Top = 0
                    Width = 100
                    Height = 45
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblTipoAdiantamento: TFLabel
                      Left = 0
                      Top = 0
                      Width = 94
                      Height = 13
                      Caption = 'Tipo Adiantamento:'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edtTipoAdiantamento: TFString
                      Left = 0
                      Top = 14
                      Width = 93
                      Height = 24
                      Table = tbLeadsPgtoAgrupadoParam
                      FieldName = 'TIPO_ADIANTAMENTO'
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = True
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      CharCase = ccNormal
                      Pwd = False
                      Maxlength = 0
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      SaveLiteralCharacter = False
                      TextAlign = taLeft
                    end
                  end
                  object separador31: TFVBox
                    Left = 110
                    Top = 0
                    Width = 5
                    Height = 45
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxNatRecDespAdiantamento: TFVBox
                    Left = 115
                    Top = 0
                    Width = 168
                    Height = 45
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 3
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblNatRecDespAdiantamento: TFLabel
                      Left = 0
                      Top = 0
                      Width = 163
                      Height = 13
                      Caption = 'Nat.: Rec.: Desp.: Adiantamento:'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edtNatRecDespAdiantamento: TFString
                      Left = 0
                      Top = 14
                      Width = 159
                      Height = 24
                      Table = tbLeadsPgtoAgrupadoParam
                      FieldName = 'NAT_REC_DESP_ADT'
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = True
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      CharCase = ccNormal
                      Pwd = False
                      Maxlength = 0
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      SaveLiteralCharacter = False
                      TextAlign = taLeft
                    end
                  end
                  object separador32: TFVBox
                    Left = 283
                    Top = 0
                    Width = 5
                    Height = 45
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 4
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxNatRecDespCartao: TFVBox
                    Left = 288
                    Top = 0
                    Width = 134
                    Height = 45
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 5
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object lblNatRecDespCartao: TFLabel
                      Left = 0
                      Top = 0
                      Width = 129
                      Height = 13
                      Caption = 'Nat.: Rec.: Desp.: Cart'#227'o:'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edtNatRecDespCartao: TFString
                      Left = 0
                      Top = 14
                      Width = 126
                      Height = 24
                      Table = tbLeadsPgtoAgrupadoParam
                      FieldName = 'NAT_REC_DESP_CART'
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      CharCase = ccNormal
                      Pwd = False
                      Maxlength = 0
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      SaveLiteralCharacter = False
                      TextAlign = taLeft
                    end
                  end
                  object separador33: TFVBox
                    Left = 422
                    Top = 0
                    Width = 5
                    Height = 45
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 6
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object hBoxFormasPgtoParcelas: TFHBox
                  Left = 0
                  Top = 72
                  Width = 972
                  Height = 60
                  Align = alTop
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object separador34: TFVBox
                    Left = 0
                    Top = 0
                    Width = 10
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxTotal: TFVBox
                    Left = 10
                    Top = 0
                    Width = 80
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object separador43: TFVBox
                      Left = 0
                      Top = 0
                      Width = 58
                      Height = 8
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object lblTotal: TFLabel
                      Left = 0
                      Top = 9
                      Width = 28
                      Height = 13
                      Caption = 'Total:'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edtTotal: TFDecimal
                      Left = 0
                      Top = 23
                      Width = 75
                      Height = 24
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      Maxlength = 0
                      Precision = 0
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      Alignment = taRightJustify
                      Mode = dmDecimal
                    end
                  end
                  object separador35: TFVBox
                    Left = 90
                    Top = 0
                    Width = 5
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxFormasPgto: TFVBox
                    Left = 95
                    Top = 0
                    Width = 115
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 3
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object separador44: TFVBox
                      Left = 0
                      Top = 0
                      Width = 101
                      Height = 8
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object lblFormasPgto: TFLabel
                      Left = 0
                      Top = 9
                      Width = 107
                      Height = 13
                      Caption = 'Formas de Pagamento'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object cbbFormasPgto: TFCombo
                      Left = 0
                      Top = 23
                      Width = 110
                      Height = 22
                      Flex = True
                      ListOptions = '2 - D'#233'bito=2;3 - Cr'#233'dito=3;4 - PIX-Sitef=4'
                      ReadOnly = True
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Prompt = 'Selecione'
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      ClearOnDelKey = True
                      UseClearButton = False
                      HideClearButtonOnNullValue = False
                      OnChange = cbbFormasPgtoChange
                      Colors = <>
                      Images = <>
                      Masks = <>
                      Fonts = <>
                      MultiSelection = False
                      IconReverseDirection = False
                    end
                  end
                  object separador36: TFVBox
                    Left = 210
                    Top = 0
                    Width = 5
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 4
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxQtdParcelas: TFVBox
                    Left = 215
                    Top = 0
                    Width = 55
                    Height = 55
                    Hint = 'Quantidade de Parcelas.'
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 5
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object separador45: TFVBox
                      Left = 0
                      Top = 0
                      Width = 50
                      Height = 8
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object lblQtdParcelas: TFLabel
                      Left = 0
                      Top = 9
                      Width = 44
                      Height = 13
                      Caption = 'Parcelas:'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edtQtdParcelas: TFString
                      Left = 0
                      Top = 23
                      Width = 50
                      Height = 24
                      Hint = 'Quantidade de Parcelas.'
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = True
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      CharCase = ccNormal
                      Pwd = False
                      Maxlength = 0
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      SaveLiteralCharacter = False
                      TextAlign = taLeft
                    end
                  end
                  object separador37: TFVBox
                    Left = 270
                    Top = 0
                    Width = 5
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 6
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxValor: TFVBox
                    Left = 275
                    Top = 0
                    Width = 80
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 7
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object separador46: TFVBox
                      Left = 0
                      Top = 0
                      Width = 75
                      Height = 8
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object lblValor: TFLabel
                      Left = 0
                      Top = 9
                      Width = 44
                      Height = 13
                      Caption = 'Valor R$:'
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      VerticalAlignment = taVerticalCenter
                      WordBreak = False
                      MaskType = mtText
                    end
                    object edtValor: TFDecimal
                      Left = 0
                      Top = 23
                      Width = 75
                      Height = 24
                      TabOrder = 0
                      AccessLevel = 0
                      Flex = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Required = False
                      Constraint.CheckWhen = cwImmediate
                      Constraint.CheckType = ctExpression
                      Constraint.FocusOnError = False
                      Constraint.EnableUI = True
                      Constraint.Enabled = False
                      Constraint.FormCheck = True
                      IconDirection = idLeft
                      Maxlength = 0
                      Precision = 0
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -13
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      Alignment = taRightJustify
                      OnEnter = edtValorEnter
                      Mode = dmDecimal
                    end
                  end
                  object vBoxAplicarFormasPgto: TFVBox
                    Left = 355
                    Top = 0
                    Width = 28
                    Height = 55
                    Hint = 'Aplicar Formas de Pagamento'
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 8
                    OnClick = btnAplicarFormasPagamentosClick
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object separador47: TFVBox
                      Left = 0
                      Top = 0
                      Width = 24
                      Height = 24
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnAplicarFormasPgto: TFIconClass
                      Left = 0
                      Top = 25
                      Width = 24
                      Height = 24
                      Hint = 'Aplicar Formas de Pagamento'
                      Picture.Data = {
                        07544269746D617036090000424D360900000000000036000000280000001800
                        0000180000000100200000000000000900000000000000000000000000000000
                        0000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400000000000000
                        0000000000000000000000000000000000000000000000000000000000000000
                        0000000000000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400000000000000
                        0000000000000000000000000000000000000000000000000000000000000000
                        0000000000000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400}
                      OnClick = btnAplicarFormasPagamentosClick
                      IconClass = 'plus-square'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Size = 24
                      Color = clTeal
                    end
                  end
                  object separador38: TFVBox
                    Left = 383
                    Top = 0
                    Width = 5
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 9
                    Visible = False
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxRemoverFormasPgto: TFVBox
                    Left = 388
                    Top = 0
                    Width = 28
                    Height = 55
                    Hint = 'Remover Todos'
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 10
                    OnClick = btnRemoverFormasPagamentosClick
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object separador48: TFVBox
                      Left = 0
                      Top = 0
                      Width = 22
                      Height = 24
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnRemoverFormasPgto: TFIconClass
                      Left = 0
                      Top = 25
                      Width = 24
                      Height = 24
                      Hint = 'Remover Todos'
                      Picture.Data = {
                        07544269746D617036090000424D360900000000000036000000280000001800
                        0000180000000100200000000000000900000000000000000000000000000000
                        0000C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400000000000000
                        0000000000000000000000000000000000000000000000000000000000000000
                        0000000000000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D40000000000C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D40000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400000000000000
                        0000000000000000000000000000000000000000000000000000000000000000
                        0000000000000000000000000000C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0D400C8D0
                        D400}
                      OnClick = btnRemoverFormasPagamentosClick
                      IconClass = 'trash'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Size = 24
                      Color = clTeal
                    end
                  end
                  object separador39: TFVBox
                    Left = 416
                    Top = 0
                    Width = 5
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 11
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftTrue
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxConfirmarFormasPgto: TFVBox
                    Left = 421
                    Top = 0
                    Width = 92
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 12
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object separador50: TFVBox
                      Left = 0
                      Top = 0
                      Width = 57
                      Height = 21
                      AutoWrap = False
                      BevelKind = bkTile
                      BevelOuter = bvNone
                      BorderStyle = stNone
                      Caption = ' '
                      FlowStyle = fsTopBottomLeftRight
                      Padding.Top = 0
                      Padding.Left = 0
                      Padding.Right = 0
                      Padding.Bottom = 0
                      TabOrder = 0
                      Margin.Top = 0
                      Margin.Left = 0
                      Margin.Right = 0
                      Margin.Bottom = 0
                      Spacing = 1
                      Flex.Vflex = ftFalse
                      Flex.Hflex = ftFalse
                      Scrollable = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      BoxShadowConfig.HorizontalLength = 10
                      BoxShadowConfig.VerticalLength = 10
                      BoxShadowConfig.BlurRadius = 5
                      BoxShadowConfig.SpreadRadius = 0
                      BoxShadowConfig.ShadowColor = clBlack
                      BoxShadowConfig.Opacity = 75
                      BorderRadius.TopLeft = 0
                      BorderRadius.TopRight = 0
                      BorderRadius.BottomRight = 0
                      BorderRadius.BottomLeft = 0
                    end
                    object btnConfirmarFormasPgto: TFButton
                      Left = 0
                      Top = 22
                      Width = 88
                      Height = 28
                      Caption = 'Confirmar'
                      Enabled = False
                      Font.Charset = DEFAULT_CHARSET
                      Font.Color = clWindowText
                      Font.Height = -11
                      Font.Name = 'Tahoma'
                      Font.Style = []
                      ParentFont = False
                      TabOrder = 1
                      OnClick = btnConfirmarFormasPgtoClick
                      PngImage.Data = {
                        89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                        F8000003AB4944415478DAED946D4C5B5518C7FFE7DEDEBEF2722F0CDAC00A5B
                        9903D489A190E13692991519091235B6FA458D4A20717E52975637BFE8072F89
                        3326860FB0B8C42C3ABCD5F8B2853899363287D9066C6B48E694970DB4EBA85A
                        5E6E590BED3D5E6E55246CCC64F8C1C42739B9C99373FFBF739EE7FF1C4229C5
                        BF19E43F0320AD44C006B8E8CBD4BFE60055DC51BD6B53CFE6EC2D8EF7073F69
                        5321BE3503903788737BD9E69E833B3F12CA852D78E9DBE771E074BB9FBE483D
                        B70D209DC4D5B4B15212AB0E0936931D734919823E17EF04DF84D82FFAA762F1
                        D61500CB21E2563F03B167E8E86AE23912713715D64A2F94BE0D3367C2DCC275
                        2D9FC50908848FE195DEBD981E4A2C0714BE47BC3B8BB78B13F1A1D1FEC874DD
                        DC1337869476136FBDAD4E7CB2783F14AA20914C80808199CDC2D19F0FA3F342
                        3B667E04E48B580238BAD88E86A2C696C71DCFE1D2DC59745D792B3A2C47EB2E
                        3F4807FE2E5EF30D11EF5FD7E8ADCF6B462C39BB74735D26BA434770F4870FF1
                        5350136FA31F531FD9788411E6A922BA8A6A5B1EB5EFC1747212469D09D16418
                        DD91768CCB614FFFAEB4F55CA789B423E72177555613E22919294501CB3048C4
                        8193335D3837D187F141201C844F156FD3FA74B7881663193A18A3055BF3B6E1
                        8E8C6A9854C062A49818FA663EC0587CBC35D7A07755F10DEE12C37DB8AEC454
                        77B0DA1E797E1683F2A798088F60E40C103985D6D96EDAF99711ACCD68595F8D
                        0EEB26E09A5AAD3C63068A2DF7C066C907AFB7A92724F83E1E80CDE0804D5F8A
                        B8222349E755C74C21941841343582E424C1505F223A7132B528BE7CD0729C10
                        F8ADF0DAD5652B63F10B4DA56B6A5497DE040B97816C2E1F062643CB27691C72
                        2A82DF1642307206E8AE5A71A6F76A74ECCB05CFCCD7F4C40A2BFFD9E4923D44
                        BCABD6EC2DB8D382081BD1727A6EB179D9E0D9F5C854ED6761782D1F53A660D0
                        71981CFB155F052E8E0EFB173CB181E5665801F803E255216279851D21FD65E8
                        18C0CE55228F2B44B66E9D2A9ABE858918D17FE9148EF50606CE1F804709DD7C
                        66560C5AC9D3C47DEF6EB354E9AC40422D53962E17765319043617993A1E996C
                        3E3E0F7642FAE2F8890BFBE151FF8FAE3AED377A2A8A1F23AEAA47CCD203358D
                        02CF176927DE602A8755578A7707F72D8AFBCFEF4BBF35B78A9BBE45050DC459
                        F314A487B7353B2A0A1AC0132B0E9F7B15FEE3813655DCF74FC45705684E7212
                        47FD5EF4B8773CEB18BE1684FFB3B3BEE06BE9015A1380B6811061F741485357
                        E0FFEEF5A5015A33C0EDC6FF805BC6EF8945931BE3ED43250000000049454E44
                        AE426082}
                      ImageId = 16
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Color = clBtnFace
                      Access = False
                      IconReverseDirection = False
                    end
                  end
                  object separador52: TFVBox
                    Left = 513
                    Top = 0
                    Width = 10
                    Height = 55
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 13
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftFalse
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object hBoxFormasPgtoOrcNotas: TFHBox
                  Left = 0
                  Top = 133
                  Width = 971
                  Height = 174
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 3
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                  object separador40: TFVBox
                    Left = 0
                    Top = 0
                    Width = 10
                    Height = 132
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 0
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                  object vBoxFormasPgtoOrcNotas: TFVBox
                    Left = 10
                    Top = 0
                    Width = 867
                    Height = 164
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 1
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftTrue
                    Scrollable = True
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                    object gridFormasPgtoOrcNotas: TFGrid
                      Left = 0
                      Top = 0
                      Width = 840
                      Height = 143
                      Align = alClient
                      TabOrder = 0
                      TitleFont.Charset = DEFAULT_CHARSET
                      TitleFont.Color = clWindowText
                      TitleFont.Height = -12
                      TitleFont.Name = 'Tahoma'
                      TitleFont.Style = []
                      Table = tbLeadsPgtoAgrupadoParc
                      Flex.Vflex = ftTrue
                      Flex.Hflex = ftTrue
                      Paging.Enabled = False
                      Paging.PageSize = 0
                      Paging.DbPaging = False
                      FrozenColumns = 0
                      ShowFooter = True
                      ShowHeader = True
                      MultiSelection = False
                      Grouping.Enabled = False
                      Grouping.Expanded = False
                      Grouping.ShowFooter = False
                      Crosstab.Enabled = False
                      Crosstab.GroupType = cgtConcat
                      EnablePopup = False
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditionEnabled = False
                      AuxColumnHeaders = <>
                      NoBorder = False
                      ActionButtons.BtnAccept = False
                      ActionButtons.BtnView = False
                      ActionButtons.BtnEdit = False
                      ActionButtons.BtnDelete = False
                      ActionButtons.BtnInLineEdit = False
                      CustomActionButtons = <>
                      Columns = <
                        item
                          Expanded = False
                          FieldName = 'SEQUENCIA_CARTAO'
                          Font = <>
                          Title.Caption = 'Seq. Cart'#227'o'
                          Width = 100
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{9517B085-EB2F-4B40-B008-719C23E00534}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'TIPO_PARCELA_CARTAO'
                          Font = <>
                          Title.Caption = 'D'#233'b. /Cr'#233'd. /Pix'
                          Width = 106
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{E6C6307D-A1F4-46CC-B11D-4C07BA0FF1A9}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'QTDE_PARCELA_CARTAO'
                          Font = <>
                          Title.Caption = 'Qtd. Parcelas'
                          Width = 95
                          Visible = True
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{FF7762D7-4235-414C-997B-FDAAE7CC3B84}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'VALOR'
                          Font = <>
                          Title.Caption = 'Valor Total Cart'#227'o'
                          Width = 128
                          Visible = True
                          Precision = 0
                          TextAlign = taRight
                          FieldType = ftString
                          FooterExpression = 
                            'new String("R$ "+tbLeadsPgtoAgrupadoParc.compute("SUM","VALOR").' +
                            'asDecimal()+"")'
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <
                            item
                              Expression = '*'
                              EvalType = etExpression
                              GUID = '{36BD4F65-2C67-45D7-9487-5D4C15108148}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              Mask = 'R$ ,##0.00'
                              PadLength = 0
                              PadDirection = pdNone
                              MaskType = mtDecimal
                            end>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{209BDEC8-EAE3-42B5-9612-8D83C1D1C4BC}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_EMPRESA'
                          Font = <>
                          Title.Caption = 'C'#243'd. Empresa'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{6F167339-E5A3-4605-B475-334F76447316}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_EVENTO'
                          Font = <>
                          Title.Caption = 'Cod. Evento'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{D6844D34-B654-4209-A975-E1573E3BFDC7}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'ORIGEM_EVENTO'
                          Font = <>
                          Title.Caption = 'Origem Evento'
                          Width = 120
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{2CDCFB70-AFD4-48B2-AD89-174D0F750A32}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'COD_FORMA_PGTO'
                          Font = <>
                          Title.Caption = 'C'#243'd. Forma Pgto'
                          Width = 130
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{DDB26681-9B1B-4BB5-8A7E-FEA306D35BC2}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'OBSERVACAO'
                          Font = <>
                          Title.Caption = 'Observa'#231#227'o'
                          Width = 220
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{4982AD90-2393-41AC-A7EC-5A664F48BA22}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          FieldName = 'ID_PAGAMENTO'
                          Font = <>
                          Title.Caption = 'Id. Pagamento'
                          Width = 100
                          Visible = False
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{074F4752-6811-4A7E-A83C-859809BA2A2C}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end
                        item
                          Expanded = False
                          Font = <>
                          Visible = True
                          Precision = 0
                          TextAlign = taLeft
                          FieldType = ftString
                          FlexRatio = 0
                          Sort = False
                          ImageHeader = 0
                          Wrap = False
                          Flex = False
                          Colors = <>
                          Images = <
                            item
                              Expression = '*'
                              Hint = 'Remover Item Pagamento'
                              EvalType = etExpression
                              GUID = '{5E0618FD-34AF-421D-97EA-6ACAA1080029}'
                              WOwner = FrInterno
                              WOrigem = EhNone
                              ImageId = 700095
                              OnClick = 'btnRemoverFormasPagamentosClick'
                              Color = clBlack
                            end>
                          Masks = <>
                          CharCase = ccNormal
                          BlobConfig.MimeType = bmtText
                          BlobConfig.ShowType = btImageViewer
                          ShowLabel = True
                          Editor.EditType = etTFString
                          Editor.Precision = 0
                          Editor.Step = 0
                          Editor.MaxLength = 100
                          Editor.LookupFilterKey = 0
                          Editor.LookupFilterDesc = 0
                          Editor.PopupHeight = 400
                          Editor.PopupWidth = 400
                          Editor.CharCase = ccNormal
                          Editor.LookupColumns = <>
                          Editor.Enabled = False
                          Editor.ReadOnly = False
                          CheckedValue = 'S'
                          UncheckedValue = 'N'
                          HiperLink = False
                          GUID = '{5704AB2D-5071-4817-99EA-36C206F38C42}'
                          WOwner = FrInterno
                          WOrigem = EhNone
                          EditorConstraint.CheckWhen = cwImmediate
                          EditorConstraint.CheckType = ctExpression
                          EditorConstraint.FocusOnError = False
                          EditorConstraint.EnableUI = True
                          EditorConstraint.Enabled = False
                          EditorConstraint.FormCheck = True
                          Empty = False
                          MobileOpts.ShowMobile = False
                          MobileOpts.Order = 0
                          BoxSize = 0
                          ImageSrcType = istSource
                          IconReverseDirection = False
                          FooterConfig.ColSpan = 0
                          FooterConfig.TextAlign = taLeft
                          FooterConfig.Enabled = False
                          HeaderTextAlign = taLeft
                        end>
                    end
                  end
                  object separador41: TFVBox
                    Left = 877
                    Top = 0
                    Width = 10
                    Height = 132
                    AutoWrap = False
                    BevelKind = bkTile
                    BevelOuter = bvNone
                    BorderStyle = stNone
                    Caption = ' '
                    FlowStyle = fsTopBottomLeftRight
                    Padding.Top = 0
                    Padding.Left = 0
                    Padding.Right = 0
                    Padding.Bottom = 0
                    TabOrder = 2
                    Margin.Top = 0
                    Margin.Left = 0
                    Margin.Right = 0
                    Margin.Bottom = 0
                    Spacing = 1
                    Flex.Vflex = ftTrue
                    Flex.Hflex = ftFalse
                    Scrollable = False
                    WOwner = FrInterno
                    WOrigem = EhNone
                    BoxShadowConfig.HorizontalLength = 10
                    BoxShadowConfig.VerticalLength = 10
                    BoxShadowConfig.BlurRadius = 5
                    BoxShadowConfig.SpreadRadius = 0
                    BoxShadowConfig.ShadowColor = clBlack
                    BoxShadowConfig.Opacity = 75
                    BorderRadius.TopLeft = 0
                    BorderRadius.TopRight = 0
                    BorderRadius.BottomRight = 0
                    BorderRadius.BottomLeft = 0
                  end
                end
                object separador42: TFHBox
                  Left = 0
                  Top = 308
                  Width = 926
                  Height = 10
                  Align = alBottom
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 4
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
        end
      end
      object tabPagamentosAgrupados: TFTabsheet
        Caption = 'Pagamentos Agrupados'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        Visible = True
        Closable = False
        WOwner = FrInterno
        WOrigem = EhNone
        object vBoxPagamentosAgrupados: TFVBox
          Left = 0
          Top = 0
          Width = 999
          Height = 414
          Align = alClient
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          FlowStyle = fsTopBottomLeftRight
          Padding.Top = 8
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftTrue
          Flex.Hflex = ftTrue
          Scrollable = True
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hBoxBotoesPgtoAgrupadosTopo: TFHBox
            Left = 0
            Top = 0
            Width = 991
            Height = 32
            Align = alTop
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 0
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object separador7: TFHBox
              Left = 0
              Top = 0
              Width = 10
              Height = 26
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object btnExcluirPgto: TFButton
              Left = 10
              Top = 0
              Width = 140
              Height = 28
              Caption = 'Excluir Agrupamento'
              Enabled = False
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 1
              OnClick = btnExcluirPgtoClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000004F24944415478DAB5965B6C145518C7FF736666AFA5DBEEB6DB6D4BE9
                426B5B682D8AA912B50662C40B1A5F34A6B531A009D1102331519EF0457D3131
                DE1E080F4653914B626262BC61141510039142A076DDDADBA6F4B2BB855E662F
                DDDD99397E33B36C172C8D26BAD97FCE9CD933FFDFF9CEF9E63B2BE07FFE082B
                FD7810D84C031E1519B6E81CED9CC349B775EA87A97F82749CFA9FF700FC5F01
                C8D82D0A784F94C4AEFAA65A7B79A05CF4F8BD707A3DC825D358885DC56C7496
                8F0F4F26530BA9014DC7530419FB470032BF8F09385ABDDAE769BD77A353D616
                C1D20B404A81905600878BF0A5E0AE55E0AB7C183D17524317463314CD2B04D9
                BF2280CC9BC8BC6FD35D2DEECA60007C7C082C350B511220524802B39EE03AAD
                93C6A1494E08C11624942CCEFC703E914E657713A47759009933F238DFB4614D
                6BDDBA2A918F8520316E99CB4201623C619AABA49CD5EA81B54868369CFEBE4F
                A1485A0832F937C0A7C0DED252E7BE8E2DB7BBF5A17E48820A49B6CC0BADC4CC
                27AE99AB399D645DE3968D18EE8FE44687267FE9E2D87A1D80666FA78B85CD9D
                ED36477A16C27C1C928DC1F3C111B0721FB2FB9E81A8C49700AB2A21BD7E085A
                7C0A8997BBAC4868B950DF8293C7CE26338BB947288A13C5800EBB4D3A7EF7D6
                3B4AB4F085C28CBD477E86746B07F4B141645EDA0E91C0F0FA21BDF325587D13
                D40BA7A1ECDCB61405DDFB636022333131F31A01DE2A06BCE0AFF0BCBD7EFD1A
                A71E195C5A92AA00CA3EFA06E2DA6613B2489138DEE835CDB5B13012BB1E861A
                8D5A119078452DA28A8AF040E46B5AA6ED05C021FA068381AE9A7207109F5CDA
                D43CC4F3A105E1D90C049BBD60AEC562D67EE4F784BB3D48BABCE83B1B8A7573
                5415008769BD1A1B6A3B7D76EACCC52DF3BC9804C8CD6D283B72D2343720F3DD
                9DD0067F876EA4EB3580914D3617548AE2CCAFFDD9A7695F8B97E8CD1ABF776F
                7DC023F2E8E52273013245E0FDE45B48EB96225047C298DBF190B93C66CA920C
                102FF5629E7C43A1C8255AA2F662C0E3252E476FDBFA60A91E19B2CC29E7A540
                005547BF83DCD00C75984CF7F4A0ECDD8390A89FA3FE4CF78304992E40987F35
                C6E30A1F9F9CD94F11EC2E06D40A8230D2715BB34D1F1F03836A026ABFF8118E
                3BEF416E288C78D736E857A2601555A83C6C4133E7CF62EAB14E13A06BF4A6D6
                3722148E24E694D4F33DE6AB55F4A2D1465FACF3FB5AFDDE12A64F5F360135BD
                9F5114D5883DFB04F4996861B468400E1C86766506D33B9EA4BDE0103C3EA425
                072E118012B69100D11B4B453375FADA1AEA5CB251D4920A18B3EA8FD11A2385
                6BA3C9819BE2E64653BA81D5D4E1E2C0486231A7BE48E61FDFACD8BDEAB2C9FB
                9A83B52548500555E64D53032208D7175EC3DC8018D555ACF0E3F2442C1B9D53
                4E7503F7AF544D194DE827A7246D6AA8AE743B6C22ADFB0C90CB5A11E4877323
                042682797DD0653B4627628BB3A9B4F215F0C051A09F8668CB0164524004AAF7
                003B37926A3CA5B2DF5BC644A306118467B310641982DD6E96ECB9850446E257
                72C39CFFF63E70E02A10218F69D20449B9114027096A48D5865A800DBB80E72A
                29C3A84E6B6E9B8D796499A5340D73193A5F545548D23144A972EC14708E9E99
                CA9B1BED9FA4E472279A2D0F3040E40D1FD5C84A8AA6A91108D6D17D4A8DF961
                32B944B3A51953F5C32C892ECDEB11586741F6A647E60DB012929B641CF654A8
                CCD7DFD8DA1C6931AF85FC6CB3CB99ACF8AFE2BFF8FC05DEE5323773766BE800
                00000049454E44AE426082}
              ImageId = 8
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object separador8: TFHBox
              Left = 150
              Top = 0
              Width = 5
              Height = 26
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object btnFechamentoParcial: TFButton
              Left = 155
              Top = 0
              Width = 140
              Height = 28
              Caption = 'Fechamento Parcial'
              Enabled = False
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              TabOrder = 3
              OnClick = btnFechamentoParcialClick
              PngImage.Data = {
                89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
                F8000003AB4944415478DAED946D4C5B5518C7FFE7DEDEBEF2722F0CDAC00A5B
                9903D489A190E13692991519091235B6FA458D4A20717E52975637BFE8072F89
                3326860FB0B8C42C3ABCD5F8B2853899363287D9066C6B48E694970DB4EBA85A
                5E6E590BED3D5E6E55246CCC64F8C1C42739B9C99373FFBF739EE7FF1C4229C5
                BF19E43F0320AD44C006B8E8CBD4BFE60055DC51BD6B53CFE6EC2D8EF7073F69
                5321BE3503903788737BD9E69E833B3F12CA852D78E9DBE771E074BB9FBE483D
                B70D209DC4D5B4B15212AB0E0936931D734919823E17EF04DF84D82FFAA762F1
                D61500CB21E2563F03B167E8E86AE23912713715D64A2F94BE0D3367C2DCC275
                2D9FC50908848FE195DEBD981E4A2C0714BE47BC3B8BB78B13F1A1D1FEC874DD
                DC1337869476136FBDAD4E7CB2783F14AA20914C80808199CDC2D19F0FA3F342
                3B667E04E48B580238BAD88E86A2C696C71DCFE1D2DC59745D792B3A2C47EB2E
                3F4807FE2E5EF30D11EF5FD7E8ADCF6B462C39BB74735D26BA434770F4870FF1
                5350136FA31F531FD9788411E6A922BA8A6A5B1EB5EFC1747212469D09D16418
                DD91768CCB614FFFAEB4F55CA789B423E72177555613E22919294501CB3048C4
                8193335D3837D187F141201C844F156FD3FA74B7881663193A18A3055BF3B6E1
                8E8C6A9854C062A49818FA663EC0587CBC35D7A07755F10DEE12C37DB8AEC454
                77B0DA1E797E1683F2A798088F60E40C103985D6D96EDAF99711ACCD68595F8D
                0EEB26E09A5AAD3C63068A2DF7C066C907AFB7A92724F83E1E80CDE0804D5F8A
                B8222349E755C74C21941841343582E424C1505F223A7132B528BE7CD0729C10
                F8ADF0DAD5652B63F10B4DA56B6A5497DE040B97816C2E1F062643CB27691C72
                2A82DF1642307206E8AE5A71A6F76A74ECCB05CFCCD7F4C40A2BFFD9E4923D44
                BCABD6EC2DB8D382081BD1727A6EB179D9E0D9F5C854ED6761782D1F53A660D0
                71981CFB155F052E8E0EFB173CB181E5665801F803E255216279851D21FD65E8
                18C0CE55228F2B44B66E9D2A9ABE858918D17FE9148EF50606CE1F804709DD7C
                66560C5AC9D3C47DEF6EB354E9AC40422D53962E17765319043617993A1E996C
                3E3E0F7642FAE2F8890BFBE151FF8FAE3AED377A2A8A1F23AEAA47CCD203358D
                02CF176927DE602A8755578A7707F72D8AFBCFEF4BBF35B78A9BBE45050DC459
                F314A487B7353B2A0A1AC0132B0E9F7B15FEE3813655DCF74FC45705684E7212
                47FD5EF4B8773CEB18BE1684FFB3B3BEE06BE9015A1380B6811061F741485357
                E0FFEEF5A5015A33C0EDC6FF805BC6EF8945931BE3ED43250000000049454E44
                AE426082}
              ImageId = 16
              WOwner = FrInterno
              WOrigem = EhNone
              Color = clBtnFace
              Access = False
              IconReverseDirection = False
            end
            object separador9: TFHBox
              Left = 295
              Top = 0
              Width = 10
              Height = 26
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 4
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object grpBoxOrcPrenotaAgrupadas: TFGroupbox
            Left = 0
            Top = 33
            Width = 992
            Height = 185
            Align = alTop
            Caption = 'OS, Or'#231'amentos e Pr'#233'-Notas Agrupadas '
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 5
            ParentFont = False
            TabOrder = 1
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            WOwner = FrInterno
            WOrigem = EhNone
            Scrollable = False
            Closable = True
            Closed = True
            OnClose = grpBoxOrcPrenotaAgrupadasClose
            Orient = coHorizontal
            Style = grp3D
            HeaderImageId = 31001
            object vBoxOrcPrenotaAgrupadas: TFVBox
              Left = 2
              Top = 15
              Width = 988
              Height = 168
              Align = alClient
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object hBoxMaisFiltros: TFHBox
                Left = 0
                Top = 0
                Width = 140
                Height = 25
                Hint = 'Filtros'
                Align = alTop
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Visible = False
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object separador10: TFHBox
                  Left = 0
                  Top = 0
                  Width = 10
                  Height = 16
                  Align = alRight
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftTrue
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object lblMaisFiltros: TFLabel
                  Left = 10
                  Top = 0
                  Width = 60
                  Height = 13
                  Caption = 'Mais Filtros: '
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  VerticalAlignment = taVerticalCenter
                  WordBreak = False
                  MaskType = mtText
                end
                object btnMaisFiltros: TFButton
                  Left = 70
                  Top = 0
                  Width = 20
                  Height = 20
                  Hint = 'Mais Filtros'
                  Font.Charset = DEFAULT_CHARSET
                  Font.Color = clWindowText
                  Font.Height = -11
                  Font.Name = 'Tahoma'
                  Font.Style = []
                  ParentFont = False
                  TabOrder = 1
                  PngImage.Data = {
                    89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
                    61000002214944415478DAA5925F48536118C69F51172B697AD56CDA70B1BC50
                    1063179A88D04DD24DE29D6012836A6511678254504689EE30447735DC09647A
                    E1AC067911BA19B855068D38E08D15E3B04D9B7367FE3B13644AE0E93B67389D
                    3B46D003E7E6BCDFF37B9FF7FD3E954884FF902AE80B8AAAA23D149DD2603BB3
                    85CDED2D60275BE4791E2BAB2B482C25108E86119809140222918848D334DA3B
                    DAD0D47845FE994EA7C1711CE25C1C53B353B279C0360093C95408904648914E
                    CF7A7A607F6947B1B6188B7C1A6B710E0CC3C8E611D7080C0683F208FB3B8846
                    A3181B1F03F580422C2920F4D10F8FD783570E1AEA9361ECED08B241CC6C4048
                    02E16406A515F50700492CCBCA9F14D566B7A1F35627AA74AB387DBE1C1A8D3A
                    7B68D1855F5C0256460DEF6B6F3E4092C5629101D3EFA6E174D9F15BF0415F73
                    9D54D6C856DF0331379E8C36C0FA7C1067B55A6580246976CFD05D94561BB385
                    DD38304FE1C5642DEE51C3B2396F078701A9A5144A4EC4D03BD885F2CAA66CF7
                    85A760BEE849BA3B79B7A10890BA53EDB568B979E3A0202510E6C91B5986F9FE
                    37F43BDFE09CFE6221605F6F5D8FB1B12962995FC7199D0EDDDD2D720AF3A364
                    CEAC98E0A8589621919BC9F6FBE0747CC0D5DB133056D51D3FC2612D8426505D
                    77996CDF0D27EDC6B587B3050FEA5800F73D0463D94F32B340CC0EB45A6772B1
                    FF09F0D93F891A9811FC5A824B1D7EE82F54FEFD291FD5A7B9007ECCF9D06AEE
                    CADDB992FE00D4950D0CC656D00E0000000049454E44AE426082}
                  ImageId = 5300469
                  WOwner = FrInterno
                  WOrigem = EhNone
                  Color = clHighlightText
                  Access = False
                  IconReverseDirection = False
                end
                object separador11: TFHBox
                  Left = 90
                  Top = 0
                  Width = 10
                  Height = 16
                  Align = alRight
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  VAlign = tvTop
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
              object separador12: TFHBox
                Left = 0
                Top = 26
                Width = 983
                Height = 5
                Align = alTop
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 1
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object hBoxOrcPrenotaAgrupadas: TFHBox
                Left = 0
                Top = 32
                Width = 968
                Height = 135
                Align = alTop
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftFalse
                Flex.Hflex = ftTrue
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                VAlign = tvTop
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
                object separador13: TFVBox
                  Left = 0
                  Top = 0
                  Width = 10
                  Height = 130
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 0
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
                object gridOrcPrenotaAgrupadas: TFGrid
                  Left = 10
                  Top = 0
                  Width = 912
                  Height = 130
                  TabOrder = 1
                  TitleFont.Charset = DEFAULT_CHARSET
                  TitleFont.Color = clWindowText
                  TitleFont.Height = -11
                  TitleFont.Name = 'Tahoma'
                  TitleFont.Style = []
                  Table = tbLeadsOrcNotasPgtoAgrupados
                  Flex.Vflex = ftTrue
                  Flex.Hflex = ftTrue
                  Paging.Enabled = False
                  Paging.PageSize = 0
                  Paging.DbPaging = False
                  FrozenColumns = 0
                  ShowFooter = False
                  ShowHeader = True
                  MultiSelection = False
                  Grouping.Enabled = False
                  Grouping.Expanded = False
                  Grouping.ShowFooter = False
                  Crosstab.Enabled = False
                  Crosstab.GroupType = cgtConcat
                  EnablePopup = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  EditionEnabled = False
                  AuxColumnHeaders = <>
                  NoBorder = False
                  ActionButtons.BtnAccept = False
                  ActionButtons.BtnView = False
                  ActionButtons.BtnEdit = False
                  ActionButtons.BtnDelete = False
                  ActionButtons.BtnInLineEdit = False
                  CustomActionButtons = <>
                  Columns = <
                    item
                      Expanded = False
                      FieldName = 'ID_PAGAMENTO'
                      Font = <>
                      Title.Caption = 'Id. Pagamento'
                      Width = 66
                      Visible = False
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{F41B81B7-68E8-4976-82A0-E727FE5BD042}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'TIPO'
                      Font = <>
                      Title.Caption = 'Tipo'
                      Width = 48
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{F7999BD4-8A8A-4515-A180-D95427A4CA42}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'COD_EMPRESA'
                      Font = <>
                      Title.Caption = 'Emp.'
                      Width = 40
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{4D255495-A693-443D-B86F-C32479CD5E79}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'COD_CLIENTE'
                      Font = <>
                      Title.Caption = 'C'#243'd. Cliente'
                      Width = 99
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{C587B1ED-7F26-4F27-B7C8-708F80F002B0}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'DATA'
                      Font = <>
                      Title.Caption = 'Data'
                      Width = 88
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftDate
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{EE1643D2-FA2D-4FD1-B003-19DCD958145B}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'USUARIO'
                      Font = <>
                      Title.Caption = 'Usu'#225'rio'
                      Width = 63
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{40AB9376-F688-4B5B-9396-BF533F621C52}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'VALOR'
                      Font = <>
                      Title.Caption = 'Valor'
                      Width = 63
                      Visible = True
                      Precision = 0
                      TextAlign = taRight
                      FieldType = ftDecimal
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{3EE9E157-1E06-43D4-ADA1-8C5A47953E11}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'DESCR'
                      Font = <>
                      Title.Caption = 'Descri'#231#227'o'
                      Width = 276
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{B64A2E60-9789-43AC-BEA9-F3A22BF06DC0}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'TIPO_ADIANTAMENTO'
                      Font = <>
                      Title.Caption = 'Tipo Adiantamento'
                      Width = 125
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{B3BBFBD2-7993-4A51-901F-1BB17E56D905}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'NAT_REC_DESP_ADT'
                      Font = <>
                      Title.Caption = 'Nat. Rec. Desp. Adt'
                      Width = 125
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{C0EB0C13-F6D1-42DF-8C97-3D54101928F5}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Hint = 'Natureza Rec Despesa Adiantamento'
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end
                    item
                      Expanded = False
                      FieldName = 'NAT_REC_DESP_CART'
                      Font = <>
                      Title.Caption = 'Nat. Rec. Desp. Cart'
                      Width = 125
                      Visible = True
                      Precision = 0
                      TextAlign = taLeft
                      FieldType = ftString
                      FlexRatio = 0
                      Sort = False
                      ImageHeader = 0
                      Wrap = False
                      Flex = False
                      Colors = <>
                      Images = <>
                      Masks = <>
                      CharCase = ccNormal
                      BlobConfig.MimeType = bmtText
                      BlobConfig.ShowType = btImageViewer
                      ShowLabel = True
                      Editor.EditType = etTFString
                      Editor.Precision = 0
                      Editor.Step = 0
                      Editor.MaxLength = 100
                      Editor.LookupFilterKey = 0
                      Editor.LookupFilterDesc = 0
                      Editor.PopupHeight = 400
                      Editor.PopupWidth = 400
                      Editor.CharCase = ccNormal
                      Editor.LookupColumns = <>
                      Editor.Enabled = False
                      Editor.ReadOnly = False
                      CheckedValue = 'S'
                      UncheckedValue = 'N'
                      HiperLink = False
                      GUID = '{E7A8A9C9-FA11-4F8F-8859-02B4ABED82D6}'
                      WOwner = FrInterno
                      WOrigem = EhNone
                      Hint = 'Natureza Rec Despesa Cart'#227'o'
                      EditorConstraint.CheckWhen = cwImmediate
                      EditorConstraint.CheckType = ctExpression
                      EditorConstraint.FocusOnError = False
                      EditorConstraint.EnableUI = True
                      EditorConstraint.Enabled = False
                      EditorConstraint.FormCheck = True
                      Empty = False
                      MobileOpts.ShowMobile = False
                      MobileOpts.Order = 0
                      BoxSize = 0
                      ImageSrcType = istSource
                      IconReverseDirection = False
                      FooterConfig.ColSpan = 0
                      FooterConfig.TextAlign = taLeft
                      FooterConfig.Enabled = False
                      HeaderTextAlign = taLeft
                    end>
                end
                object separador14: TFVBox
                  Left = 922
                  Top = 0
                  Width = 10
                  Height = 130
                  AutoWrap = False
                  BevelKind = bkTile
                  BevelOuter = bvNone
                  BorderStyle = stNone
                  Caption = ' '
                  FlowStyle = fsTopBottomLeftRight
                  Padding.Top = 0
                  Padding.Left = 0
                  Padding.Right = 0
                  Padding.Bottom = 0
                  TabOrder = 2
                  Margin.Top = 0
                  Margin.Left = 0
                  Margin.Right = 0
                  Margin.Bottom = 0
                  Spacing = 1
                  Flex.Vflex = ftFalse
                  Flex.Hflex = ftFalse
                  Scrollable = False
                  WOwner = FrInterno
                  WOrigem = EhNone
                  BoxShadowConfig.HorizontalLength = 10
                  BoxShadowConfig.VerticalLength = 10
                  BoxShadowConfig.BlurRadius = 5
                  BoxShadowConfig.SpreadRadius = 0
                  BoxShadowConfig.ShadowColor = clBlack
                  BoxShadowConfig.Opacity = 75
                  BorderRadius.TopLeft = 0
                  BorderRadius.TopRight = 0
                  BorderRadius.BottomRight = 0
                  BorderRadius.BottomLeft = 0
                end
              end
            end
          end
          object vBoxFormasPagamentos: TFVBox
            Left = 0
            Top = 219
            Width = 988
            Height = 180
            Align = alClient
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            FlowStyle = fsTopBottomLeftRight
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 2
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object separador17: TFHBox
              Left = 0
              Top = 0
              Width = 983
              Height = 6
              Align = alTop
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object hBoxTituloFormasPgto: TFHBox
              Left = 0
              Top = 7
              Width = 415
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 10
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftFalse
              Flex.Hflex = ftFalse
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object lblTituloFormasPgto: TFLabel
                Left = 0
                Top = 0
                Width = 264
                Height = 13
                Caption = 'Formas de Pagamento c /Cart'#227'o [D'#233'bito/Cr'#233'dito] e PIX'
                Font.Charset = DEFAULT_CHARSET
                Font.Color = clWindowText
                Font.Height = -11
                Font.Name = 'Tahoma'
                Font.Style = []
                ParentFont = False
                WOwner = FrInterno
                WOrigem = EhNone
                VerticalAlignment = taVerticalCenter
                WordBreak = False
                MaskType = mtText
              end
            end
            object hBoxFormasPagamentos: TFHBox
              Left = 0
              Top = 28
              Width = 968
              Height = 145
              Align = alTop
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 2
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              VAlign = tvTop
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
              object separador15: TFVBox
                Left = 0
                Top = 0
                Width = 10
                Height = 134
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 0
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
              object gridFormasPagamentos: TFGrid
                Left = 10
                Top = 0
                Width = 912
                Height = 134
                TabOrder = 1
                TitleFont.Charset = DEFAULT_CHARSET
                TitleFont.Color = clWindowText
                TitleFont.Height = -12
                TitleFont.Name = 'Tahoma'
                TitleFont.Style = []
                Table = tbLeadsFormaPgtoAgrupado
                Flex.Vflex = ftTrue
                Flex.Hflex = ftTrue
                Paging.Enabled = False
                Paging.PageSize = 0
                Paging.DbPaging = False
                FrozenColumns = 0
                ShowFooter = False
                ShowHeader = True
                MultiSelection = False
                Grouping.Enabled = False
                Grouping.Expanded = False
                Grouping.ShowFooter = False
                Crosstab.Enabled = False
                Crosstab.GroupType = cgtConcat
                EnablePopup = False
                WOwner = FrInterno
                WOrigem = EhNone
                EditionEnabled = False
                AuxColumnHeaders = <>
                NoBorder = False
                ActionButtons.BtnAccept = False
                ActionButtons.BtnView = False
                ActionButtons.BtnEdit = False
                ActionButtons.BtnDelete = False
                ActionButtons.BtnInLineEdit = False
                CustomActionButtons = <>
                Columns = <
                  item
                    Expanded = False
                    FieldName = 'SEQUENCIA_CARTAO'
                    Font = <>
                    Title.Caption = 'Seq.'
                    Width = 40
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{8CB75EA9-12A5-4B05-9DE7-6E090ABC0E32}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'TIPO_PARCELA_CARTAO'
                    Font = <>
                    Title.Caption = 'Tipo Cart'#227'o'
                    Width = 17
                    Visible = False
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{2F792A24-BBBE-4734-B81C-83E8E61B7638}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'DESC_TIPO_CARTAO'
                    Font = <>
                    Title.Caption = 'Tipo Cart'#227'o D/C/PIX'
                    Width = 192
                    Visible = True
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{92346CE7-AA4B-4E97-98A4-2955C311D93B}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'QTDE_PARCELA_CARTAO'
                    Font = <>
                    Title.Caption = 'Qtde. Parcelas'
                    Width = 97
                    Visible = True
                    Precision = 0
                    TextAlign = taRight
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{46104CF2-6BC1-47A4-9E94-D8AA77DA60BA}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'VALOR'
                    Font = <>
                    Title.Caption = 'Valor'
                    Width = 80
                    Visible = True
                    Precision = 0
                    TextAlign = taRight
                    FieldType = ftDecimal
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{A36BF59D-70CE-42C3-8AC2-CB5B7B7C8752}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'COD_EMPRESA'
                    Font = <>
                    Title.Caption = 'C'#243'd. Empresa'
                    Width = 86
                    Visible = False
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{B64A2E60-9789-43AC-BEA9-F3A22BF06DC0}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'COD_EVENTO'
                    Font = <>
                    Title.Caption = 'Id. Pagamento'
                    Width = 65
                    Visible = False
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{05ADBE64-6909-4CEC-B757-82684307968B}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'ORIGEM_EVENTO'
                    Font = <>
                    Title.Caption = 'Origem Evento'
                    Width = 103
                    Visible = False
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{BB548454-74C0-43C1-9DA6-C7BD80C9875C}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'COD_FORMA_PGTO'
                    Font = <>
                    Title.Caption = 'C'#243'd. Forma Pagamento'
                    Width = 147
                    Visible = False
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{BCD2D1DC-DD36-463D-96D2-C715C2091716}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'OBSERVACAO'
                    Font = <>
                    Title.Caption = 'Observa'#231#227'o'
                    Width = 111
                    Visible = False
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{8B49E26B-BE22-4B48-B80D-01B37FCF22D4}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end
                  item
                    Expanded = False
                    FieldName = 'ID_PAGAMENTO'
                    Font = <>
                    Title.Caption = 'Id. Pagamento'
                    Width = 40
                    Visible = False
                    Precision = 0
                    TextAlign = taLeft
                    FieldType = ftString
                    FlexRatio = 0
                    Sort = False
                    ImageHeader = 0
                    Wrap = False
                    Flex = False
                    Colors = <>
                    Images = <>
                    Masks = <>
                    CharCase = ccNormal
                    BlobConfig.MimeType = bmtText
                    BlobConfig.ShowType = btImageViewer
                    ShowLabel = True
                    Editor.EditType = etTFString
                    Editor.Precision = 0
                    Editor.Step = 0
                    Editor.MaxLength = 100
                    Editor.LookupFilterKey = 0
                    Editor.LookupFilterDesc = 0
                    Editor.PopupHeight = 400
                    Editor.PopupWidth = 400
                    Editor.CharCase = ccNormal
                    Editor.LookupColumns = <>
                    Editor.Enabled = False
                    Editor.ReadOnly = False
                    CheckedValue = 'S'
                    UncheckedValue = 'N'
                    HiperLink = False
                    GUID = '{CC65227D-0514-4BB0-B830-59C998E1ED10}'
                    WOwner = FrInterno
                    WOrigem = EhNone
                    EditorConstraint.CheckWhen = cwImmediate
                    EditorConstraint.CheckType = ctExpression
                    EditorConstraint.FocusOnError = False
                    EditorConstraint.EnableUI = True
                    EditorConstraint.Enabled = False
                    EditorConstraint.FormCheck = True
                    Empty = False
                    MobileOpts.ShowMobile = False
                    MobileOpts.Order = 0
                    BoxSize = 0
                    ImageSrcType = istSource
                    IconReverseDirection = False
                    FooterConfig.ColSpan = 0
                    FooterConfig.TextAlign = taLeft
                    FooterConfig.Enabled = False
                    HeaderTextAlign = taLeft
                  end>
              end
              object separador16: TFVBox
                Left = 922
                Top = 0
                Width = 10
                Height = 134
                AutoWrap = False
                BevelKind = bkTile
                BevelOuter = bvNone
                BorderStyle = stNone
                Caption = ' '
                FlowStyle = fsTopBottomLeftRight
                Padding.Top = 0
                Padding.Left = 0
                Padding.Right = 0
                Padding.Bottom = 0
                TabOrder = 2
                Margin.Top = 0
                Margin.Left = 0
                Margin.Right = 0
                Margin.Bottom = 0
                Spacing = 1
                Flex.Vflex = ftTrue
                Flex.Hflex = ftFalse
                Scrollable = False
                WOwner = FrInterno
                WOrigem = EhNone
                BoxShadowConfig.HorizontalLength = 10
                BoxShadowConfig.VerticalLength = 10
                BoxShadowConfig.BlurRadius = 5
                BoxShadowConfig.SpreadRadius = 0
                BoxShadowConfig.ShadowColor = clBlack
                BoxShadowConfig.Opacity = 75
                BorderRadius.TopLeft = 0
                BorderRadius.TopRight = 0
                BorderRadius.BottomRight = 0
                BorderRadius.BottomLeft = 0
              end
            end
          end
          object separador18: TFHBox
            Left = 0
            Top = 400
            Width = 343
            Height = 6
            Align = alBottom
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 0
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            TabOrder = 3
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 1
            Flex.Vflex = ftFalse
            Flex.Hflex = ftFalse
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
          end
        end
      end
    end
  end
  object FPopupMenuSelecaoOrcamentosPreNotas: TFPopupMenu
    AutoHotkeys = maManual
    WOwner = FrInterno
    Left = 854
    Top = 251
    object mnSelecionarTodos: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Todos'
      OnClick = mnSelecionarTodosClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{DE94E3FE-5070-400F-8AA5-FF545F1B0492}'
    end
    object mnSelecionarNenhum: TFMenuItem
      AutoHotkeys = maManual
      Caption = 'Selecionar Nenhum'
      OnClick = mnSelecionarNenhumClick
      WOwner = FrInterno
      WOrigem = EhNone
      Access = False
      Checkmark = False
      GUID = '{B8719EF7-E8E2-4DD1-855F-174DD6F0BC02}'
    end
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA_DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_COMPLETO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Completo'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29801'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 909
    Top = 15
  end
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_UPPER'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa Upper'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29802'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 792
    Top = 15
  end
  object tbOrcPreNotasParaAgrupar: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SALDO_PARA_AGRUPAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Saldo Para Agrupar'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VLR_AGRUPADO_ANT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Vlr Agrupado Ant'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'PESQ_ORC_PARA_AGRUPAR'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29803'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsPgtoAgrupado: TFTable
    FieldDefs = <
      item
        Name = 'ID_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Orc Mapa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PGTO_AGRUPADO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29804'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsPgtoAgrupadoParc: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_PARCELA_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Parcela Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_PARCELA_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Parcela Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PGTO_AGRUPADO_PARC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29805'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsPgtoAgrupadoParam: TFTable
    FieldDefs = <
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ADIANTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Adiantamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAT_REC_DESP_ADT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Natureza Rec Despesa Adt'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAT_REC_DESP_CART'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Natureza Rec Despesa Cart'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_PGTO_AGRUPADO_PARAM'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29806'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadsOrcNotasPgtoAgrupados: TFTable
    FieldDefs = <
      item
        Name = 'DESCR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Data'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_ADIANTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Adiantamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAT_REC_DESP_ADT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Natureza Rec Despesa Adt'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NAT_REC_DESP_CART'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Natureza Rec Despesa Cart'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_ORC_NOTAS_PGTO_AGRUPADOS'
    MaxRowCount = 200
    OnAfterScroll = tbLeadsOrcNotasPgtoAgrupadosAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29807'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 624
    Top = 17
  end
  object tbLeadsFormaPgtoAgrupado: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Origem Evento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_FORMA_PGTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Forma Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_PARCELA_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Parcela Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE_PARCELA_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade Parcela Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        Caption = 'Valor'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBSERVACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Observa'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQUENCIA_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Seq'#252#234'ncia Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PAGAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Pagamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESC_TIPO_CARTAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Desconto Tipo Cart'#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_FORMA_PGTO_AGRUPADO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '298020;29808'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
