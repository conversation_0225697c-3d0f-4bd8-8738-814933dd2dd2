object ChecklistOsAssinaturaConsultorRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '103021'
  Left = 44
  Top = 163
  Height = 299
  Width = 442
  object tbOsAssinatura: TFTable
    FieldDefs = <
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica'#231#227'o'
        GUID = '{FBD4C33C-A6FD-4B5D-9AFD-5C7CF31A996E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{D528AC31-6FE5-4E99-8456-4033032EA288}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{F323D930-10BB-41E3-B56D-4313E86664A5}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura'
        GUID = '{963513EB-C632-4BD9-A171-74F070C0E8C0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Assinatura'
        GUID = '{F6C8B2F0-96A0-4D3B-AA29-86EAEE9DBB6F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura Cliente'
        GUID = '{B5CF58B0-696B-4E52-83B5-93BD53065FF1}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_ASSINATURA_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Assinatura Cliente'
        GUID = '{54CE9802-A8AE-4085-866F-CB2CF66C9CCE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'MOB_OS_ASSINATURA'
    Cursor = 'MOB_OS_ASSINATURA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103021;10301'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{BBB61150-8F43-43F8-9C5A-7D309458A96F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSINATURA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assinatura'
        GUID = '{FD655A08-BA24-484C-8C10-2D61734A6C42}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS_USUARIOS'
    Cursor = 'EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103021;10302'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbOs: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{A4F68545-607E-4B53-8AB8-91FF5B9588CA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{5CD12A76-8DC4-4410-9549-F45C19AE5FA9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NUMERO_OS'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'N'#250'mero Os'
        GUID = '{1300CEAF-62A3-4821-8F82-F071D6011BB4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'OS'
    Cursor = 'OS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '103021;51601'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
