object CadastroLeadzapRN: TFDataModule
  OldCreateOrder = False
  OnDestroy = DataModuleDestroy
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '440093'
  Left = 321
  Top = 163
  Height = 299
  Width = 442
  object scCrmCadastroWhatsapp: TFSchema
    Tables = <
      item
        Table = tbCadastroWhatsapp
        GUID = '{8CB01D50-8DCF-4471-958B-BF94082E7ECA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbWhatsappEmpresa
        GUID = '{BDD4E2A7-6D76-44D8-988E-ED8B3AACDA15}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbCadastroWhatsappCidades
        GUID = '{2DA60FC6-E907-4B6D-B4BA-A52AE012CDDE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbCadastroWhatsapp: TFTable
    FieldDefs = <
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular Notifica'#231#227'o'
        GUID = '{AC1FB32B-7CB4-4D51-8AF2-4AAE4DBFBAB8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{79D3F040-4D1D-4359-B920-F95EA3DB7A9F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{37D55EB7-43FB-482E-B327-092F6D06C645}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        GUID = '{52764589-79B7-4D10-A5DA-54DCC0C4E369}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'IMAGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        Caption = 'Imagem'
        GUID = '{101957A3-34BE-450A-AE99-2128458FD520}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SICRONIZADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sicronizado'
        GUID = '{410C86CA-8B7D-4C08-B1CC-870BB698F977}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR_AVISO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Notifica'#231#227'o'
        GUID = '{CCED2268-4A5F-49C4-B0DE-F563EE350236}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_LEADZAP'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Leadzap'
        GUID = '{FDF96932-A261-4A7E-BE3B-826D96395754}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cliente'
        GUID = '{37209CDB-8B7F-40BC-9A19-155F8333177D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        GUID = '{E49D1DFA-B535-4552-B018-31C2C1794C85}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PHONE_NUMBER_TOKEN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Phone Number Token'
        GUID = '{6187013F-64C5-4D7B-94CE-4742C44E5DFF}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_ID'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Whatsapp Id.'
        GUID = '{4AB52011-A445-4624-B0B9-C603468A9980}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'X_API_TOKEN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'X Api Token'
        GUID = '{99FE4C5D-5171-44AD-A88C-2868F2E253CA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_API'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Api'
        GUID = '{EAA68AC6-6A71-41B6-A2C4-21371B1167A4}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_PAINEL_WEB'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Painel Web'
        GUID = '{9C604C35-6EE1-4646-9BC1-9853420D8B65}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_USUARIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Usu'#225'rio'
        GUID = '{71615221-55DE-4673-9A31-C909F494D6DA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_SENHA'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Senha'
        GUID = '{EC9CCE01-D63B-4A65-A5F1-045B56DB46BB}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_EMAIL_NOTIFICACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Email Notifica'#231#227'o'
        GUID = '{A681D79D-F50A-46AB-B210-3F8804836C12}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        GUID = '{4FF913E0-A9CF-4715-95AD-BE172824EEA7}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_INSTANCE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Instance'
        GUID = '{A9E51D0A-3E62-4822-829C-84092153630F}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_INSTANCE_TOKEN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Instance Token'
        GUID = '{122A1936-FFD7-48FC-9364-481211B74BD8}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TOKEN_API'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tokapi'
        GUID = '{57717509-4D33-41B9-A872-C7DF62E077F9}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_CLIENT_TOKEN'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Client Token'
        GUID = '{F15A5845-4F3A-4E66-ADDC-C35FE6CBBACC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_API_URL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Api Url'
        GUID = '{45789D36-69FE-4EAC-A7F6-82BF914C745B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_TIPO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Tipo'
        GUID = '{949D3C21-CCA1-47A7-8801-51E31D5A635D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ZAPI_CONECTADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Zapi Conectado'
        GUID = '{A59C0E83-6C06-4BD4-B342-A3DFDDDA9212}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_CADASTRO_WHATSAPP'
    TableName = 'CRM_CADASTRO_WHATSAPP'
    Cursor = 'CRM_CADASTRO_WHATSAPP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;44001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbWhatsappEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        GUID = '{36D2599C-7BB7-4AC8-BC38-7906F3E3777A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{1B8A43CD-7016-413C-A71C-63605DC2171A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        GUID = '{734F0F1D-299E-4BA4-BD7D-6E3C826527BD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    MasterFields = 'ID_CELULAR'
    DetailFilters = 'ID_CELULAR'
    UpdateTable = 'CRM_WHATSAPP_EMPRESA'
    TableName = 'CRM_WHATSAPP_EMPRESA'
    Cursor = 'CRM_WHATSAPP_EMPRESA'
    MaxRowCount = 200
    MasterTable = tbCadastroWhatsapp
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresas: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{495AAB4F-A2AC-4F04-B3A2-033E745D4B20}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{F164883B-F049-4BED-9C60-8D6609E61E72}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        GUID = '{2559C212-5201-4343-8B49-7470332A69BA}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS'
    Cursor = 'EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;44002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbWhatsappParametrosEmpresa: TFTable
    FieldDefs = <
      item
        Name = 'NOME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome'
        GUID = '{17B1484B-0DF5-43BE-9074-F496922C5DC6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'WHATSAPP_PARAMETROS_EMPRESA'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;44003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCadastroWhatsappValidar: TFTable
    FieldDefs = <
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        GUID = '{C41F8894-4B85-432F-8759-2347A7982361}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{37902DD9-75D5-4B50-A861-B7C8D2E78723}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_CADASTRO_WHATSAPP'
    Cursor = 'CRM_CADASTRO_WHATSAPP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;44004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmpresasCruzaLeadZap: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        GUID = '{8192FCAB-C774-4062-9B43-F37BBD9D761D}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Nome Empresa'
        GUID = '{B313F9F1-314C-497C-B564-5D296E299A2E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'EMPRESAS'
    Cursor = 'EMPRESAS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbWhatsappLog: TFTable
    FieldDefs = <
      item
        Name = 'DATA_OCORRENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        Caption = 'Data Ocorrencia'
        GUID = '{A79FDC83-6EA5-4317-87DE-D7D88A09DE07}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OCORRENCIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ocorrencia'
        GUID = '{CBFC49BA-7725-4360-8D37-6E95A64B3536}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_WHATSAPP_LOG'
    Cursor = 'CRM_WHATSAPP_LOG'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCadWhatsappAtivo: TFTable
    FieldDefs = <
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        GUID = '{AD24948C-3546-48BA-AF1C-C5D0CB660ED6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRM_CAD_WHATSAPP_ATIVO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbUf: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{17FF827F-E6C7-453F-A535-29609AE68913}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{0730DB8E-438A-4E96-A05D-371AE346D694}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'UF'
    Cursor = 'UF'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCidades: TFTable
    FieldDefs = <
      item
        Name = 'UF'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{522BAEDC-B17C-47CB-B25C-D3B8024A32F2}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{18D27CFB-505C-4140-BFD4-577ABAA3EE7A}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{FE911C77-AA39-4A47-956F-105FC758E6BE}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CIDADES'
    Cursor = 'CIDADES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCadastroWhatsappCidades: TFTable
    FieldDefs = <
      item
        Name = 'ID_WHATS_CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Whats Cidade'
        GUID = '{6FB37A44-A2E5-4443-8BBF-FDA4253CB5CD}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        GUID = '{AA0E024D-7CF3-43A7-AD10-C1CD1358B9AC}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'UF'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Uf'
        GUID = '{6C481B6B-1147-481D-8078-2567FF2E55F6}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        GUID = '{6A6C3C8B-55A7-42C9-A0B7-91B698150DF3}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CIDADES'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Cidades'
        GUID = '{B6A05E9A-4205-45AE-B16E-FA1D9B984F4B}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_CADASTRO_WHATSAPP_CIDADES'
    TableName = 'CRM_CADASTRO_WHATSAPP_CIDADES'
    Cursor = 'CRM_CADASTRO_WHATSAPP_CIDADES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46007'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbLeadzapMenu: TFTable
    FieldDefs = <
      item
        Name = 'ID_MENU'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Menu'
        GUID = '{EDD0AC8E-B58B-4E3F-8669-9FEF3343E50E}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENU_DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Menu Descri'#231#227'o'
        GUID = '{F6473868-2C95-4AAA-84E7-6CCC22B60468}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_LEADZAP_MENU'
    Cursor = 'CRM_LEADZAP_MENU'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440093;46008'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
