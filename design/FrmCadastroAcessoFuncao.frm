object FrmCadastroAcessoFuncao: TFForm
  Left = 44
  Top = 160
  ActiveControl = vboxPrincipal
  Caption = 'Nova Fun'#231#227'o'
  ClientHeight = 372
  ClientWidth = 495
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '382034'
  ShortcutKeys = <>
  InterfaceRN = 'CadastroAcessoFuncaoRN'
  Access = False
  ChangedProp = 
    'FrmCadastroAcessoFuncao.Width;'#13#10'FrmCadastroAcessoFuncao.Height;'#13 +
    #10#13#10'FrmCadastroAcessoFuncao.ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vboxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 495
    Height = 372
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 5
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 5
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object FHBox1: TFHBox
      Left = 0
      Top = 0
      Width = 476
      Height = 70
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 5
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object btnSalvar: TFButton
        Left = 0
        Top = 0
        Width = 75
        Height = 60
        Caption = 'Salvar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 0
        OnClick = btnSalvarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F8000003D04944415478DAB5956D4C535718C7FFE572DBD23704442A96CC54B3
          0FCBCC8C8AC962861A475CD018656EBA370638CD96EC83734EB850E096B7C220
          AB14B4BE14682953B6647ED88654652CD93E6C89F36D2C9ABD603A2DAED03950
          4BDFDBEBBD1745C4BB8492F824E7DC73CE6DFFBFE77F9EDC734478CA21E2BAA5
          5B9B2C91507817373D954ACD592CFF3F03C088182231A17DE89B03BBA7008B37
          35440FD36F26B83DE328087E386740CD583DE6A9E468E974C49CBD14F1089067
          603A0D8528A2ACF8E1C84E2814CAB8C5BDDE7BC879BF071FBD9707A3E5349CA7
          29D163006B43216C5F9F87BE601954AAE4B80177EFDE0165B988552F2C414B7B
          DF93005B6311A8D63EF4E8D6C117F4211C09CD5A9C4C14432691619BEE1CDEDA
          B21AA60E21C0A7452833397052B716E9E919713BF07846B0ADA21F6F6CCE465B
          A700C0DE540C8A059C28CF815ABD286E80DB3D8CFCCA7EBC9E978DC3D6FF0194
          B53AF079590E02E10082A1C0ACC5256229A4A414AF567D87EDAFAC84D9E61002
          EC42799B03DDD44BD0689E89DB81CBF537B6D303C8DFB812476C020EBA580715
          87CEC05E3A77C06BFA016CCD5D81A35D0EA12217A3CA7C0E5D256BE2167F183B
          AABFC7E69757E0789780838EC662984F5D8067F43618E6E15F187ECCF65C37B9
          32733E6D9C9CAAC29AE55A58EC020E2C8622F45EB835950DC33C1067BB183F66
          5B8C979B7C4E5F9FF65BED4225DA851C1CAD2B84E3D23F18BFFA2DBC37AFCC6E
          4F529E8357B59C77402412902B93F0BC36431860AE7D17672FBBE13A5B0F5D05
          05599202244942CC7EA5DC934C24F9ED8844A38846A218BF338ECACA4A64ACFF
          0422D164F6EED1312C5BAA46875D0070A8BA00FDBF8EC075A60E75F5B518BC76
          093DF6AFA692ADA9A94620E0879F6DC1500819E96A1EB0607D095F18858CC4F0
          C8189ECD4A1306B4EA0B3030380968686CC0EF7FFD86CEE3DDF06A0BA1B86E65
          C574BC380F0886B0285303BA8A867A43295FEA34A514D75DFF42AB4983D52E70
          9A1EAC7A073F5EF5E086A316CDCD4D1872FE0973DB31285EDC0BEF4F07514A1D
          80DFCF01022088042C601DE8693D3273CB78918529525C738E62712607E87D12
          B0FF832D20C512F47794C06834E2E6B0139F35B74C6DD1BE8FF7F2D9472211C8
          6572CC4B4E054DD3D06C2C674544C89A9F84C1A11164A953609B0958B2C9B087
          94488CBBDFCE95FDFC050D93C9C41656CC17361C0E2310F4C3E7F761626282DF
          0E2281E0DF733558BDB31631766DBE52823F6EDCE693EC3ED9C7DE68A58F6E34
          2EB2D6EDDB4FC8D29BB295BFC4F5F59EBFB76AC60A7B2733812F87FAE86276E2
          134D7BC39DD1F2399F138F478C6DB766029E4ADC07F973E62852430A58000000
          0049454E44AE426082}
        ImageId = 4
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
      object btnCancelar: TFButton
        Left = 75
        Top = 0
        Width = 75
        Height = 60
        Caption = 'Cancelar'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        Layout = blGlyphTop
        ParentFont = False
        TabOrder = 1
        OnClick = btnCancelarClick
        PngImage.Data = {
          89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
          F80000036F4944415478DAB5937B48537114C7CFDDBD9B5B375FA98569605A14
          8A2414988A927F88996952948948149A6020998614BD0B7A5A448564A5882912
          1469A9B3971AE5BB742DCC245F99736ACE747373BBAF7E8B4BDD86530BF783C3
          FDFDF89EDFF7737EE77031B0F1C2E69B189B27D954BEDF546B4BC01486611965
          29C63C9B00B6DD9150EECEDEF4F044DF0D8661B3D16BB805079CDBFD9028797B
          D9D0A96AA962682601414C0B0AB898F894A03803C8DB8AA61BBA2A9434CD4422
          C8F87F0150CF49D4F354424C6C060E5611205D7932BE18DA0DA5E0278D81775F
          6AA827ADF92A86A1CDC3EFFB2740DC5D699608C34F6C5C1B49047885C99C644B
          81B47384214601FDA6264030F0936D8501752F5B54776992A64D1108D23A2700
          558D897051AECF32FFA49D41E98B285C0B8394020CAC06284C0FE84580C0FC45
          1C7CA55B403761805B55D94686A5A310A4665600EAF3D9D51E0199311B92654D
          E345306CFCFC57B2D9DC1CB8080767F10A085EB217DE763E31D677567D66192E
          1C01345601A87A4F0921ED4A8B3A2F6B9C28041DFD1D569281E0245E0E8E2848
          DC05BA74B5F071B212DC243EBFCC9F298AA795FD0DCD1C0BE6EAF5B3B6281655
          BFC93FEE8887C772BC77AA09825CF6C017753B3730D2AD1FD27CE574FA49F240
          F445AC515308EB9DE2A1AC39CFD0ADFE508DCC7721736ACE21C7E5DBBD4A0C3D
          1CAEC6DAC0DF211AE41F0B8C9F7A143DC8E014923FA0501EDD51404C535A7850
          7F5D3FA8E92E465AEA5C3FDC1FC03DBBEEB4C8CBDE2AFA3DC8287728A9CBE945
          0601C860926F2195BEF52A51F2FA8A614CABCA294B311D87792C21A02C213433
          D6D1C11106C6BAA0A2E97EEDE37DC670C18C287B9933A39D1ECF2A4F31DD9C8F
          B9E5900F86F86EB910B236D66E4CA782FC17674738965B835EF083D7B5E89382
          CEA5F335B704D8E338DE931C71DAD595F484965E39FDBCBD5439D8C286B5DEA6
          8D9BAF89C3E419D41BC15D4E102C1FD6013C24DACDC1F3414268C6227BA90BBC
          EA28353574C8ABCB534D49481659E4B3BC39C3EF193E68AB00F339FA96248924
          C9DCED41A9322F177FEC666526A5908F062A4B1835D271FE0E3B83A9F04B5903
          98AB24C2CF88D739B8638F5C17AF583AAAFD36F8F298316C6AE4978EF339ACC0
          90B6D8D3B3017E43CC117C88F0EE79C98CAA151C25A85ED822D60246F17B6E36
          8050C305558BAC001881293793894D97CD013F01E2D89E284DE0BF3E00000000
          49454E44AE426082}
        ImageId = 9
        WOwner = FrInterno
        WOrigem = EhNone
        Color = clBtnFace
        Access = False
        IconReverseDirection = False
      end
    end
    object gridAcessoFuncao: TFGrid
      Left = 0
      Top = 71
      Width = 474
      Height = 232
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Table = tbGridCadAcessoFunc
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Paging.Enabled = False
      Paging.PageSize = 0
      Paging.DbPaging = False
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      ActionButtons.BtnAccept = False
      ActionButtons.BtnView = False
      ActionButtons.BtnEdit = False
      ActionButtons.BtnDelete = False
      ActionButtons.BtnInLineEdit = False
      CustomActionButtons = <>
      Columns = <
        item
          Expanded = False
          FieldName = 'SEL'
          Font = <>
          Title.Caption = 'Sel'
          Width = 60
          Visible = True
          Precision = 0
          TextAlign = taCenter
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <
            item
              Expression = 'SEL = '#39'N'#39
              EvalType = etExpression
              GUID = '{78F662FA-1931-4D8B-9412-B3FB32783F93}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 34006
              OnClick = 'gridFuncaoUnChecked'
              Color = clBlack
            end
            item
              Expression = 'SEL = '#39'S'#39
              EvalType = etExpression
              GUID = '{556BBAD8-1F48-46A6-8621-4977A33E5CA1}'
              WOwner = FrInterno
              WOrigem = EhNone
              ImageId = 34007
              OnClick = 'gridFuncaoChecked'
              Color = clBlack
            end>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = False
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{10EB598D-7760-465F-BC5D-8A0D80075AEA}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'ID'
          Font = <>
          Title.Caption = 'Id'
          Width = 60
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{D45CF1CA-AE54-4064-BE31-3D2625FE0F6C}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end
        item
          Expanded = False
          FieldName = 'FUNCAO'
          Font = <>
          Title.Caption = 'Fun'#231#227'o'
          Width = 120
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = False
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{12E5D49A-B0BB-418C-AEED-F9CFC10C54B0}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
          BoxSize = 0
          ImageSrcType = istSource
          IconReverseDirection = False
          FooterConfig.ColSpan = 0
          FooterConfig.TextAlign = taLeft
          FooterConfig.Enabled = False
          HeaderTextAlign = taLeft
        end>
    end
  end
  object tbGridCadAcessoFunc: TFTable
    FieldDefs = <
      item
        Name = 'SEL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Sel'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'BSC_GRID_CAD_ACESSO_FUNC'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382034;38201'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbPainelAcessoFuncao: TFTable
    FieldDefs = <
      item
        Name = 'COD_FUNCAO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Fun'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PAINEL'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Painel'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'BSC_PAINEL_ACESSO_FUNCAO'
    Cursor = 'BSC_PAINEL_ACESSO_FUNCAO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '382034;38202'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
