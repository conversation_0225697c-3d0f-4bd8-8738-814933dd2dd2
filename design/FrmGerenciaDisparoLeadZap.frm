object FrmGerenciaDisparoLeadZap: TFForm
  Left = 320
  Top = 162
  ActiveControl = vBoxPrincipal
  Caption = 'Disparos Lzap'
  ClientHeight = 480
  ClientWidth = 921
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '4600177'
  ShortcutKeys = <>
  InterfaceRN = 'GerenciaDisparoLeadZapRN'
  Access = False
  ChangedProp = 
    'FrmGerenciaDisparoLeadZap.Height;'#13#10#13#10'FrmGerenciaDisparoLeadZap.A' +
    'ctiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object vBoxPrincipal: TFVBox
    Left = 0
    Top = 0
    Width = 921
    Height = 480
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 0
    Padding.Left = 0
    Padding.Right = 0
    Padding.Bottom = 0
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    object hBoxFiltros: TFHBox
      Left = 0
      Top = 0
      Width = 916
      Height = 45
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object vBoxComboEmp: TFVBox
        Left = 0
        Top = 0
        Width = 433
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object comboEmpresa: TFCombo
          Left = 0
          Top = 0
          Width = 391
          Height = 21
          LookupTable = tbLeadsEmpresasUsuarios
          LookupKey = 'COD_EMPRESA'
          LookupDesc = 'EMPRESA'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Empresa'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = True
          HideClearButtonOnNullValue = True
          Align = alClient
          OnChange = comboEmpresaChange
          OnClearClick = comboEmpresaClearClick
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
        end
      end
      object vBoxComboDisparo: TFVBox
        Left = 433
        Top = 0
        Width = 433
        Height = 41
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 1
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object cbDisparo: TFCombo
          Left = 0
          Top = 0
          Width = 391
          Height = 21
          LookupTable = tbDisparoCombo
          LookupKey = 'ID_DISPARO'
          LookupDesc = 'DESCRICAO'
          Flex = True
          ReadOnly = True
          WOwner = FrInterno
          WOrigem = EhNone
          Required = False
          Prompt = 'Disparo'
          Constraint.CheckWhen = cwImmediate
          Constraint.CheckType = ctExpression
          Constraint.FocusOnError = False
          Constraint.EnableUI = True
          Constraint.Enabled = False
          Constraint.FormCheck = True
          ClearOnDelKey = True
          UseClearButton = True
          HideClearButtonOnNullValue = True
          Align = alClient
          OnChange = cbDisparoChange
          OnClearClick = cbDisparoClearClick
          Colors = <>
          Images = <>
          Masks = <>
          Fonts = <>
        end
      end
    end
    object grdDisparosLeadZap: TFGrid
      Left = 0
      Top = 46
      Width = 732
      Height = 14
      Align = alClient
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -11
      TitleFont.Name = 'Tahoma'
      TitleFont.Style = []
      Visible = False
      Table = tbAnaliseDisparoLzapGrd
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Paging.Enabled = True
      FrozenColumns = 0
      ShowFooter = False
      ShowHeader = True
      MultiSelection = False
      Grouping.Enabled = False
      Grouping.Expanded = False
      Grouping.ShowFooter = False
      Crosstab.Enabled = False
      Crosstab.GroupType = cgtConcat
      EnablePopup = False
      WOwner = FrInterno
      WOrigem = EhNone
      EditionEnabled = False
      AuxColumnHeaders = <>
      NoBorder = False
      Columns = <
        item
          Expanded = False
          FieldName = 'DESCRICAO'
          Font = <>
          Title.Caption = 'Disparo'
          Width = 124
          Visible = True
          Precision = 0
          TextAlign = taLeft
          FieldType = ftString
          FlexRatio = 0
          Sort = True
          ImageHeader = 0
          Wrap = False
          Flex = True
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{0D1AC353-A11C-4A05-A482-B44ADB46D478}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
        end
        item
          Expanded = False
          FieldName = 'QTDE'
          Font = <>
          Title.Caption = 'Quantidade'
          Width = 99
          Visible = True
          Precision = 0
          TextAlign = taRight
          FieldType = ftString
          FlexRatio = 0
          Sort = True
          ImageHeader = 0
          Wrap = False
          Flex = False
          Colors = <>
          Images = <>
          Masks = <>
          CharCase = ccNormal
          BlobConfig.MimeType = bmtText
          BlobConfig.ShowType = btImageViewer
          ShowLabel = True
          Editor.EditType = etTFString
          Editor.Precision = 0
          Editor.Step = 0
          Editor.MaxLength = 100
          Editor.LookupFilterKey = 0
          Editor.LookupFilterDesc = 0
          Editor.PopupHeight = 400
          Editor.PopupWidth = 400
          Editor.CharCase = ccNormal
          Editor.LookupColumns = <>
          Editor.Enabled = False
          Editor.ReadOnly = False
          CheckedValue = 'S'
          UncheckedValue = 'N'
          HiperLink = False
          GUID = '{CF1C7188-7F42-4FDE-B39E-895C10642D8A}'
          WOwner = FrInterno
          WOrigem = EhNone
          EditorConstraint.CheckWhen = cwImmediate
          EditorConstraint.CheckType = ctExpression
          EditorConstraint.FocusOnError = False
          EditorConstraint.EnableUI = True
          EditorConstraint.Enabled = False
          EditorConstraint.FormCheck = True
          Empty = False
          MobileOpts.ShowMobile = False
          MobileOpts.Order = 0
        end>
    end
    object vBoxBottom: TFVBox
      Left = 0
      Top = 61
      Width = 916
      Height = 403
      Align = alClient
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object hBoxGerDispLZap: TFHBox
        Left = 0
        Top = 0
        Width = 571
        Height = 34
        Align = alClient
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 0
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object hbGerDispLZapFiltroGraf01: TFHBox
          Left = 0
          Top = 0
          Width = 140
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 5
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 0
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hbGerDispLZapQtde: TFHBox
            Left = 0
            Top = 0
            Width = 52
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = clSilver
            Padding.Top = 3
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 0
            OnClick = hbGerDispLZapQtdeClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox3: TFVBox
              Left = 0
              Top = 0
              Width = 7
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblQtde: TFLabel
              Left = 7
              Top = 0
              Width = 28
              Height = 14
              Caption = 'Qtde'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
            end
            object FVBox9: TFVBox
              Left = 35
              Top = 0
              Width = 7
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object hbGerDispLZapRespostas: TFHBox
            Left = 52
            Top = 0
            Width = 78
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 3
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 1
            OnClick = hbGerDispLZapRespostasClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox53: TFVBox
              Left = 0
              Top = 0
              Width = 7
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblRespostas: TFLabel
              Left = 7
              Top = 0
              Width = 54
              Height = 14
              Caption = 'Respostas'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
            end
            object FVBox54: TFVBox
              Left = 61
              Top = 0
              Width = 7
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
        end
        object FHBox26: TFHBox
          Left = 140
          Top = 0
          Width = 15
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 1
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object hbGerDispLZapFiltroGraf02: TFHBox
          Left = 155
          Top = 0
          Width = 338
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftTrue
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object hbGerDispLZapMesAtual: TFHBox
            Left = 0
            Top = 0
            Width = 94
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Color = clSilver
            Padding.Top = 3
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 0
            OnClick = hbGerDispLZapMesAtualClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox5: TFVBox
              Left = 0
              Top = 0
              Width = 16
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblMesAtual: TFLabel
              Left = 16
              Top = 0
              Width = 53
              Height = 14
              Caption = 'M'#234's Atual'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
            end
            object FVBox6: TFVBox
              Left = 69
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object hbGerDispLZapMesAnterior: TFHBox
            Left = 94
            Top = 0
            Width = 110
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 3
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 1
            OnClick = hbGerDispLZapMesAnteriorClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox7: TFVBox
              Left = 0
              Top = 0
              Width = 16
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblMesAnterior: TFLabel
              Left = 16
              Top = 0
              Width = 69
              Height = 14
              Caption = 'M'#234's Anterior'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
            end
            object FVBox8: TFVBox
              Left = 85
              Top = 0
              Width = 12
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
          object hbGerDispLZapUltimos12Meses: TFHBox
            Left = 204
            Top = 0
            Width = 124
            Height = 25
            AutoWrap = False
            BevelKind = bkTile
            BevelOuter = bvNone
            BorderStyle = stNone
            Caption = ' '
            Padding.Top = 3
            Padding.Left = 0
            Padding.Right = 0
            Padding.Bottom = 0
            ParentBackground = False
            TabOrder = 2
            OnClick = hbGerDispLZapUltimos12MesesClick
            Margin.Top = 0
            Margin.Left = 0
            Margin.Right = 0
            Margin.Bottom = 0
            Spacing = 0
            Flex.Vflex = ftTrue
            Flex.Hflex = ftTrue
            Scrollable = False
            WOwner = FrInterno
            WOrigem = EhNone
            BoxShadowConfig.HorizontalLength = 10
            BoxShadowConfig.VerticalLength = 10
            BoxShadowConfig.BlurRadius = 5
            BoxShadowConfig.SpreadRadius = 0
            BoxShadowConfig.ShadowColor = clBlack
            BoxShadowConfig.Opacity = 75
            VAlign = tvTop
            BorderRadius.TopLeft = 0
            BorderRadius.TopRight = 0
            BorderRadius.BottomRight = 0
            BorderRadius.BottomLeft = 0
            object FVBox20: TFVBox
              Left = 0
              Top = 0
              Width = 7
              Height = 20
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 0
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
            object lblUltimos12Meses: TFLabel
              Left = 7
              Top = 0
              Width = 94
              Height = 14
              Caption = #218'ltimos 12 Meses'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = 'Tahoma'
              Font.Style = []
              ParentFont = False
              WOwner = FrInterno
              WOrigem = EhNone
              VerticalAlignment = taVerticalCenter
              WordBreak = False
            end
            object FVBox21: TFVBox
              Left = 101
              Top = 0
              Width = 7
              Height = 19
              AutoWrap = False
              BevelKind = bkTile
              BevelOuter = bvNone
              BorderStyle = stNone
              Caption = ' '
              FlowStyle = fsTopBottomLeftRight
              Padding.Top = 0
              Padding.Left = 0
              Padding.Right = 0
              Padding.Bottom = 0
              TabOrder = 1
              Margin.Top = 0
              Margin.Left = 0
              Margin.Right = 0
              Margin.Bottom = 0
              Spacing = 1
              Flex.Vflex = ftTrue
              Flex.Hflex = ftTrue
              Scrollable = False
              WOwner = FrInterno
              WOrigem = EhNone
              BoxShadowConfig.HorizontalLength = 10
              BoxShadowConfig.VerticalLength = 10
              BoxShadowConfig.BlurRadius = 5
              BoxShadowConfig.SpreadRadius = 0
              BoxShadowConfig.ShadowColor = clBlack
              BoxShadowConfig.Opacity = 75
              BorderRadius.TopLeft = 0
              BorderRadius.TopRight = 0
              BorderRadius.BottomRight = 0
              BorderRadius.BottomLeft = 0
            end
          end
        end
        object FHBox23: TFHBox
          Left = 493
          Top = 0
          Width = 10
          Height = 30
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
      end
      object FChartBarQtdeMes: TFChartBar
        Left = 0
        Top = 35
        Width = 644
        Height = 94
        WOwner = FrInterno
        WOrigem = EhNone
        BuildIn3d = False
        CategoryField = 'MES'
        CategoryLabel = 'Dias'
        ShowLegend = True
        ShowTooltip = True
        Table = tbAnaliseDispLzapGrafQtdMes
        TooltipFormat = '{0}: ({1}, {2})'
        Series = <
          item
            CaptionField = 'STATUS'
            XField = 'MES'
            YField = 'QTDE'
            GUID = '{993CAB73-EB8B-4702-84C1-8BF9AE7AB97A}'
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        Orient = coHorizontal
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        YAxisTickInterval = 0
        XAxisTickInterval = 0
        Stacking = True
        ShowDataLabel = True
        XLabelRotation = -45
        YLabelRotation = 0
        LegendAlign = center
        LegendVerticalAlign = bottom
        ColorPaletteIndex = 13
      end
      object FChartBarQtdeAno: TFChartBar
        Left = 0
        Top = 130
        Width = 644
        Height = 86
        WOwner = FrInterno
        WOrigem = EhNone
        BuildIn3d = False
        CategoryField = 'MES_ANO'
        CategoryLabel = 'Meses'
        ShowLegend = True
        ShowTooltip = True
        Table = tbAnaliseDispLzapGrafQtdAno
        TooltipFormat = '{0}: ({1}, {2})'
        Series = <
          item
            CaptionField = 'STATUS'
            XField = 'MES_ANO'
            YField = 'QTDE'
            GUID = '{993CAB73-EB8B-4702-84C1-8BF9AE7AB97A}'
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        Orient = coHorizontal
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Visible = False
        YAxisTickInterval = 0
        XAxisTickInterval = 0
        Stacking = True
        ShowDataLabel = True
        XLabelRotation = -45
        YLabelRotation = 0
        LegendAlign = center
        LegendVerticalAlign = bottom
        ColorPaletteIndex = 13
      end
      object FChartBarRespMes: TFChartBar
        Left = 0
        Top = 217
        Width = 644
        Height = 94
        WOwner = FrInterno
        WOrigem = EhNone
        BuildIn3d = False
        CategoryField = 'MES'
        CategoryLabel = 'Dias'
        ShowLegend = True
        ShowTooltip = True
        Table = tbAnaliseDispLzapGrafQtdMes
        TooltipFormat = '{0}: ({1}, {2})'
        Series = <
          item
            CaptionField = 'STATUS'
            XField = 'MES'
            YField = 'QTDE'
            GUID = '{993CAB73-EB8B-4702-84C1-8BF9AE7AB97A}'
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        Orient = coHorizontal
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Visible = False
        YAxisTickInterval = 0
        XAxisTickInterval = 0
        Stacking = True
        ShowDataLabel = True
        XLabelRotation = -45
        YLabelRotation = 0
        LegendAlign = center
        LegendVerticalAlign = bottom
        ColorPaletteIndex = 13
      end
      object FChartBarRespAno: TFChartBar
        Left = 0
        Top = 312
        Width = 644
        Height = 86
        WOwner = FrInterno
        WOrigem = EhNone
        BuildIn3d = False
        CategoryField = 'MES_ANO'
        CategoryLabel = 'Meses'
        ShowLegend = True
        ShowTooltip = True
        Table = tbAnaliseDispLzapGrafQtdAno
        TooltipFormat = '{0}: ({1}, {2})'
        Series = <
          item
            CaptionField = 'STATUS'
            XField = 'MES_ANO'
            YField = 'QTDE'
            GUID = '{993CAB73-EB8B-4702-84C1-8BF9AE7AB97A}'
            WOwner = FrInterno
            WOrigem = EhNone
          end>
        Orient = coHorizontal
        Flex.Vflex = ftTrue
        Flex.Hflex = ftTrue
        Visible = False
        YAxisTickInterval = 0
        XAxisTickInterval = 0
        Stacking = True
        ShowDataLabel = True
        XLabelRotation = -45
        YLabelRotation = 0
        LegendAlign = center
        LegendVerticalAlign = bottom
        ColorPaletteIndex = 13
      end
    end
  end
  object tbAnaliseDisparoLzapGrd: TFTable
    FieldDefs = <
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id.'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ANALISE_DISPARO_LZAP_GRD'
    MaxRowCount = 0
    OnAfterScroll = tbAnaliseDisparoLzapGrdAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600177;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    Left = 164
    Top = 15
  end
  object tbAnaliseDispLzapGrafQtdMes: TFTable
    FieldDefs = <
      item
        Name = 'MES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#234's'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dia'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ANALISE_DISP_LZAP_GRAF_QTD_MES'
    MaxRowCount = 0
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600177;46002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbAnaliseDispLzapGrafQtdAno: TFTable
    FieldDefs = <
      item
        Name = 'MES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#234's'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MES_ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#234's Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ANALISE_DISP_LZAP_GRAF_QTD_ANO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600177;46003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbAnaliseDispLzapGrafResMes: TFTable
    FieldDefs = <
      item
        Name = 'RESPOSTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resposta'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Dia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#234's'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ANALISE_DISP_LZAP_GRAF_RES_MES'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600177;46004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbAnaliseDispLzapGrafResAno: TFTable
    FieldDefs = <
      item
        Name = 'RESPOSTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Resposta'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#234's'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MES_ANO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'M'#234's Ano'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QTDE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quantidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'ANALISE_DISP_LZAP_GRAF_RES_ANO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600177;46005'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbLeadsEmpresasUsuarios: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'C'#243'd. Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BAIRRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Bairro'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Empresa'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CIDADE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Cidade'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ESTADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Estado'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'LEADS_EMPRESAS_USUARIOS'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600177;46006'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
  object tbDisparoCombo: TFTable
    FieldDefs = <
      item
        Name = 'ID_DISPARO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Disparo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'CRM_DISPARO_COMBO'
    MaxRowCount = 200
    OnAfterScroll = tbDisparoComboAfterScroll
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '4600177;46007'
    DeltaMode = dmChanged
    RatioBatchSize = 20
  end
end
