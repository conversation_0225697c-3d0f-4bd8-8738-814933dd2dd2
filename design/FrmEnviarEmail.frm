object FrmEnviarEmail: TFForm
  Left = 321
  Top = 142
  ActiveControl = FVBox3
  Caption = 'Enviar E-Mail'
  ClientHeight = 462
  ClientWidth = 1024
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '340042'
  ShortcutKeys = <>
  InterfaceRN = 'EnviarEmailRN'
  Access = False
  ChangedProp = 
    'FrmEnviarEmail.Width;'#13#10'FrmEnviarEmail.Height;'#13#10#13#10'FrmEnviarEmail.' +
    'ActiveControl'
  Spacing = 0
  PixelsPerInch = 96
  TextHeight = 13
  object FVBox3: TFVBox
    Left = 0
    Top = 0
    Width = 1024
    Height = 462
    Align = alClient
    AutoWrap = False
    BevelKind = bkTile
    BevelOuter = bvNone
    BorderStyle = stNone
    Caption = ' '
    FlowStyle = fsTopBottomLeftRight
    Padding.Top = 5
    Padding.Left = 5
    Padding.Right = 8
    Padding.Bottom = 5
    TabOrder = 0
    Margin.Top = 0
    Margin.Left = 0
    Margin.Right = 0
    Margin.Bottom = 0
    Spacing = 1
    Flex.Vflex = ftTrue
    Flex.Hflex = ftTrue
    Scrollable = False
    WOwner = FrInterno
    WOrigem = EhNone
    BoxShadowConfig.HorizontalLength = 10
    BoxShadowConfig.VerticalLength = 10
    BoxShadowConfig.BlurRadius = 5
    BoxShadowConfig.SpreadRadius = 0
    BoxShadowConfig.ShadowColor = clBlack
    BoxShadowConfig.Opacity = 75
    BorderRadius.TopLeft = 0
    BorderRadius.TopRight = 0
    BorderRadius.BottomRight = 0
    BorderRadius.BottomLeft = 0
    ExplicitHeight = 612
    object FHBox9: TFHBox
      Left = 0
      Top = 0
      Width = 1024
      Height = 68
      Align = alTop
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Color = 16514043
      Padding.Top = 5
      Padding.Left = 2
      Padding.Right = 0
      Padding.Bottom = 5
      ParentBackground = False
      TabOrder = 0
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object FHBox10: TFHBox
        Left = 0
        Top = 0
        Width = 829
        Height = 60
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        Padding.Top = 2
        Padding.Left = 0
        Padding.Right = 0
        Padding.Bottom = 0
        ParentBackground = False
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 2
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 3
        Flex.Vflex = ftFalse
        Flex.Hflex = ftTrue
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        VAlign = tvTop
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object btnVoltar: TFButton
          Left = 0
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Voltar'
          Caption = 'Voltar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 0
          OnClick = btnVoltarClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000001594944415478DAC595CD4A03311446935AACB8D667F0091475A1885A
            FC41DC08825B374237DD08BE82B8F00F0457BE8088A828A22EDCEA0314F42174
            290A1D4F98DB618833E9D8A43470B89026DF693B931BADBA3C74CF055AEB12E5
            086661228AA2CF600209BF805599DA42701A4420E197B0929AAE2338F41648F8
            152C5B1FF90B24FC1A9632D6FB0908EFA3DC423567FD1BECC10B3490FD141648
            F81DCC17FC724D91ED22FA700A24FC5EC5AF62276313CE10457F0412FE00331D
            86B7C639ACE368DA827D6ADD33BC359EA09A96184199FA0C938124E699EC2402
            B194C53E15483286E43511A4248F301D40F00E23E6A1679D03D7DBF42DB5BFE8
            AFC83AC9AEF3909C64D65528C32608B661DC5A7BC2DA5A5E2F32921B58C81364
            EC31B203D890A92F186CD74DED86D7B617B16F51C5ED46390529C9BFEF03F68D
            AAB85F0D15BDD18E61CEFCCFAEBE63ED5B3392AEDEC948067A7FE9FB8E5F6437
            736BB6EF9B710000000049454E44AE426082}
          ImageId = 700081
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object btnEnviarEmail: TFButton
          Left = 65
          Top = 0
          Width = 65
          Height = 53
          Hint = 'Enviar'
          Caption = 'Enviar'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          Layout = blGlyphTop
          ParentFont = False
          TabOrder = 1
          OnClick = btnEnviarEmailClick
          PngImage.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F8000002674944415478DAB5D57D68CD511CC7F1EF2EB250CB166516A5146A79
            285126A5E629F2108D7F742333F963F3B83C95A7248F434AE10F91A78CF28714
            6BFEC15F9A566BFCE58F292BD414CAB3F7B7F3594EBFEE76BB7773EA55F79E73
            7EF7FB3BDFF33DE716D8FF69D3D180E2827EFEE1513882B4BE77F6578042D461
            0F86A10553D1D4D700FEFC529CC438BC433D5EA21567FB12A0DC429EE7E21B4E
            E0283EA30A37B1319F002538881AA4D0881D7813CDF1F17DA8C825C0206CC27E
            0C570A6AF144E303F04B9F3DE80ACBA18AE6E33426E203F6E252F483D35086FB
            FADE8E2294660B301EA7B0183F714ECBEFD2B83FBF19EB30033F30185FD08CCA
            9E0214E92D6B959A07D88657D11C4FD3652CB7B0D1CDEA2F57FACEA02E19C0F3
            98B6705846E235B62A40DC665AA892B1B885D5D1987FBE816A5C8C03CC56543F
            209F7000E7F13D9A93D24AFC05062A1513F0369A7348ABAFC0530F3006C72CD4
            EE6F8F6AA1C4DE27DE7A04AE6061D457AF67E3765769F3147679808F28D626FA
            F21A33ECC91C5C4769D4E7FB3139B142535AFDBA18DD5D055B94E7324D788C0B
            164ACE57B4DB42EDA7123F54A9B9712B54DA9A30AF3B80299F8B2C9CCE05EAEF
            448785AB37D9EE6055867E5F91DF430D7A71CB54A67E696DC07AE53DD9BE6A63
            3B328CAD512AABB597D6DB419B84B60CFDBEFC2AED5DB21DB67065CFC2B36C01
            FC2DDB7B18F3DBF3B685BD7A8E3FEABF8765A60ACA2780A7C7CFC04A4C515FAB
            025DC30B0CB17F059353002FE32578A8E7FCEEA951BABC7AFC7F60281E59B81C
            730EB0165733CCF333945630BF1C8F6367AE01B65BF85BECADF939F1926ED36A
            B206285100BF3177599EED2FF82981D6887056640000000049454E44AE426082}
          ImageId = 34004
          WOwner = FrInterno
          WOrigem = EhNone
          Color = clBtnFace
          Access = False
          IconReverseDirection = False
        end
        object FHBox2: TFHBox
          Left = 130
          Top = 0
          Width = 57
          Height = 41
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 2
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
        end
        object FHBox3: TFHBox
          Left = 187
          Top = 0
          Width = 185
          Height = 51
          AutoWrap = False
          BevelKind = bkTile
          BevelOuter = bvNone
          BorderStyle = stNone
          Caption = ' '
          Padding.Top = 0
          Padding.Left = 0
          Padding.Right = 0
          Padding.Bottom = 0
          TabOrder = 3
          Margin.Top = 0
          Margin.Left = 0
          Margin.Right = 0
          Margin.Bottom = 0
          Spacing = 1
          Flex.Vflex = ftFalse
          Flex.Hflex = ftFalse
          Scrollable = False
          WOwner = FrInterno
          WOrigem = EhNone
          BoxShadowConfig.HorizontalLength = 10
          BoxShadowConfig.VerticalLength = 10
          BoxShadowConfig.BlurRadius = 5
          BoxShadowConfig.SpreadRadius = 0
          BoxShadowConfig.ShadowColor = clBlack
          BoxShadowConfig.Opacity = 75
          VAlign = tvTop
          BorderRadius.TopLeft = 0
          BorderRadius.TopRight = 0
          BorderRadius.BottomRight = 0
          BorderRadius.BottomLeft = 0
          object ckSolicitarAprCliente: TFCheckBox
            Left = 0
            Top = 0
            Width = 169
            Height = 17
            Caption = 'Solicitar Aprova'#231#227'o do Cliente'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -11
            Font.Name = 'Tahoma'
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            Visible = False
            ReadOnly = False
            WOwner = FrInterno
            WOrigem = EhNone
            VerticalAlignment = taAlignTop
          end
        end
      end
    end
    object FHBox4: TFHBox
      Left = 0
      Top = 69
      Width = 750
      Height = 18
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      Color = 15592941
      Padding.Top = 0
      Padding.Left = 5
      Padding.Right = 0
      Padding.Bottom = 0
      ParentBackground = False
      TabOrder = 1
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      VAlign = tvTop
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object lblMensagem: TFLabel
        Left = 0
        Top = 0
        Width = 67
        Height = 13
        Align = alLeft
        Caption = 'Mensagem....'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clNavy
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        WOwner = FrInterno
        WOrigem = EhNone
        VerticalAlignment = taAlignTop
        WordBreak = False
        MaskType = mtText
      end
    end
    object FVBox2: TFVBox
      Left = 0
      Top = 88
      Width = 697
      Height = 125
      AutoWrap = False
      BevelKind = bkTile
      BevelOuter = bvNone
      BorderStyle = stNone
      Caption = ' '
      FlowStyle = fsTopBottomLeftRight
      Padding.Top = 0
      Padding.Left = 0
      Padding.Right = 0
      Padding.Bottom = 0
      TabOrder = 2
      Margin.Top = 0
      Margin.Left = 0
      Margin.Right = 0
      Margin.Bottom = 0
      Spacing = 1
      Flex.Vflex = ftFalse
      Flex.Hflex = ftTrue
      Scrollable = False
      WOwner = FrInterno
      WOrigem = EhNone
      BoxShadowConfig.HorizontalLength = 10
      BoxShadowConfig.VerticalLength = 10
      BoxShadowConfig.BlurRadius = 5
      BoxShadowConfig.SpreadRadius = 0
      BoxShadowConfig.ShadowColor = clBlack
      BoxShadowConfig.Opacity = 75
      BorderRadius.TopLeft = 0
      BorderRadius.TopRight = 0
      BorderRadius.BottomRight = 0
      BorderRadius.BottomLeft = 0
      object vBoxTemplate: TFVBox
        Left = 0
        Top = 0
        Width = 107
        Height = 25
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 2
        Padding.Left = 2
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 0
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel2: TFLabel
          Left = 0
          Top = 0
          Width = 47
          Height = 13
          Caption = 'Template '
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object cmbTemplate: TFCombo
        Left = 0
        Top = 26
        Width = 240
        Height = 21
        LookupTable = tbEmailModelo
        LookupKey = 'ID_EMAIL_MODELO'
        LookupDesc = 'MODELO'
        Flex = True
        ReadOnly = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Prompt = 'Selecione'
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        ClearOnDelKey = True
        UseClearButton = True
        HideClearButtonOnNullValue = True
        OnChange = cmbTemplateChange
        Colors = <>
        Images = <>
        Masks = <>
        Fonts = <>
        MultiSelection = False
        IconReverseDirection = False
      end
      object FVBox1: TFVBox
        Left = 0
        Top = 48
        Width = 52
        Height = 25
        AutoWrap = False
        BevelKind = bkTile
        BevelOuter = bvNone
        BorderStyle = stNone
        Caption = ' '
        FlowStyle = fsTopBottomLeftRight
        Padding.Top = 2
        Padding.Left = 2
        Padding.Right = 0
        Padding.Bottom = 0
        TabOrder = 2
        Margin.Top = 0
        Margin.Left = 0
        Margin.Right = 0
        Margin.Bottom = 0
        Spacing = 1
        Flex.Vflex = ftFalse
        Flex.Hflex = ftFalse
        Scrollable = False
        WOwner = FrInterno
        WOrigem = EhNone
        BoxShadowConfig.HorizontalLength = 10
        BoxShadowConfig.VerticalLength = 10
        BoxShadowConfig.BlurRadius = 5
        BoxShadowConfig.SpreadRadius = 0
        BoxShadowConfig.ShadowColor = clBlack
        BoxShadowConfig.Opacity = 75
        BorderRadius.TopLeft = 0
        BorderRadius.TopRight = 0
        BorderRadius.BottomRight = 0
        BorderRadius.BottomLeft = 0
        object FLabel1: TFLabel
          Left = 0
          Top = 0
          Width = 42
          Height = 13
          Caption = 'Assunto '
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          WOwner = FrInterno
          WOrigem = EhNone
          VerticalAlignment = taVerticalCenter
          WordBreak = False
          MaskType = mtText
        end
      end
      object edAssunto: TFString
        Left = 0
        Top = 74
        Width = 457
        Height = 24
        TabOrder = 0
        AccessLevel = 0
        Flex = True
        WOwner = FrInterno
        WOrigem = EhNone
        Required = False
        Constraint.CheckWhen = cwImmediate
        Constraint.CheckType = ctExpression
        Constraint.FocusOnError = False
        Constraint.EnableUI = True
        Constraint.Enabled = False
        Constraint.FormCheck = True
        CharCase = ccNormal
        Pwd = False
        Maxlength = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Tahoma'
        Font.Style = []
        SaveLiteralCharacter = False
        TextAlign = taLeft
      end
    end
    object edMailBody: TFRichEdit
      Left = 0
      Top = 214
      Width = 433
      Height = 157
      Align = alClient
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      Lines.Strings = (
        'FRichEdit1')
      ParentFont = False
      TabOrder = 3
      Flex.Vflex = ftTrue
      Flex.Hflex = ftTrue
      ToolbarConfig = tcBasicPlus
      WOwner = FrInterno
      WOrigem = EhNone
    end
  end
  object spEnviarEmail: TFStoredProcedure
    Params = <
      item
        DataType = dtNUMBER
        Direction = pdIN
        Name = 'I_COD_EMPRESA'
      end
      item
        DataType = dtNUMBER
        Direction = pdIN
        Name = 'I_COD_EVENTO'
      end
      item
        DataType = dtVARCHAR2
        Direction = pdIN
        Name = 'I_USUARIO'
      end
      item
        DataType = dtVARCHAR2
        Direction = pdIN
        Name = 'I_EMAIL_PARA'
      end
      item
        DataType = dtVARCHAR2
        Direction = pdIN
        Name = 'I_EMAIL_ASSUNTO'
      end
      item
        DataType = dtVARCHAR2
        Direction = pdIN
        Name = 'I_EMAIL_MSG'
      end
      item
        DataType = dtVARCHAR2
        Direction = pdIN
        Name = 'I_EMAIL_MSG_PURE'
      end>
    Sql = 
      'call PK_CRM_EVENTOS_RN_2.CRM_ACAO_ENVIAR_EMAIL_NOVO(:I_COD_EMPRE' +
      'SA,'#13#10'                                          :I_COD_EVENTO,'#13#10' ' +
      '                                        :I_USUARIO,'#13#10'           ' +
      '                              :I_EMAIL_PARA,'#13#10'                  ' +
      '                       :I_EMAIL_ASSUNTO,'#13#10'                      ' +
      '                   :I_EMAIL_MSG,'#13#10'                              ' +
      '           :I_EMAIL_MSG_PURE)'
    WOwner = FrInterno
    WOrigem = EhNone
    Left = 485
    Top = 172
  end
  object scCrmEmailFluxo: TFSchema
    Tables = <
      item
        Table = tbEmailFluxo
        GUID = '{A0863F02-E438-41FD-993B-A50CD3D942D0}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbEmailFluxo: TFTable
    FieldDefs = <
      item
        Name = 'NOME_EMPRESAS_USUARIOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SEQ_ID_MAIL_FLUXO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSUNTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_ENVIO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESTINO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIDO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CORPO_EMAIL_HTML'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EMAIL'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENSAGEM_ERRO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ORIGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DATA_EXIBICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_EMAIL_FLUXO'
    TableName = 'CRM_EMAIL_FLUXO'
    Cursor = 'CRM_EMAIL_FLUXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;34001'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 8
    Top = 204
  end
  object tbEmailModelo: TFTable
    FieldDefs = <
      item
        Name = 'ID_EMAIL_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_CRIOU'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRIVADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSUNTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENSAGEM'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO'
    Cursor = 'CRM_EMAIL_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;34002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Top = 168
  end
  object tbEmailModeloTag: TFTable
    FieldDefs = <
      item
        Name = 'ID_TAG'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO_TAG'
    Cursor = 'CRM_EMAIL_MODELO_TAG'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;34004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 28
    Top = 172
  end
  object tbCrmpartsDadosEmail: TFTable
    FieldDefs = <
      item
        Name = 'SAUDACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'SR_SRA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_DO_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'E_MAIL_DO_CLIENTE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'N_ORCAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_BRUTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'VALOR_LIQUIDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ORC_MAPA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    Cursor = 'VW_CRMPARTS_DADOS_EMAIL'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;31002'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
    Left = 32
    Top = 3
  end
  object tbParmFluxo: TFTable
    FieldDefs = <
      item
        Name = 'COD_EMPRESA'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MARKUP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'INTERVALO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FLUXO_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGA_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_NA_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGA_NA_AGENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ENTREGA_RESERVA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAXIMO_ATRASADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_INICIO_NORMAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_FIM_NORMAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_INICIO_SABADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_FIM_SABADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_INICIO_DOMINGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXP_FIM_DOMINGO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDate
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONSIDERADA_FERIADO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_ANDAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RECEPCAO_ENXERGA_TUDO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIBERA_FIM_DE_SEMANA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_CLIENTE_FONE2'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_CLIENTE_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_CLIENTE_SEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VEICULO_FAMILIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VEICULO_MODELO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_VEICULO_COR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_OBS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEFAULT_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEFAULT_INTERESSE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_FLUXO_DIVERSO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'AUTO_OCUPAR_VENDEDORES'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEST_DRIVE_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_GERENTE_FI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_MESA_FI'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNCAO_FINANC_EXTERNA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_ESPERA_MIN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_FLUXO_LOJA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'HORAS_RESERVA_ESTOQUE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MAX_FICHA_LEILAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGAR_TEST_DRIVE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'OBRIGAR_SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPT_VEIC_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPT_VEIC_USADOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCO_ACESSORIO_VEIC_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCO_ACESSORIO_VEIC_USADOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TROCO_AVALIACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOSTRAR_PLACA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_GER_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_VEND_NOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_SEC_VENDAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_GER_SNOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_VEND_SNOVOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_SEC_SVENDAS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_FINANCIADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FUNC_SEGURADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FI_MAX_RANK_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEXTO_MENU_SELLING'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_CRM_GOLD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_01'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_02'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_03'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'REL_PERCENT_04'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_SEGURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_CONSORCIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_ACESSORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_VISITA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FLUXO_CONSIDERA_DIVISAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMPRESA_VENDEDORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEXTO_TERMO_TDRIVE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVENTO_OBRIGAR_DDD'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FONE_LIBERA_VEZ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TIPO_RESERVA_TEMP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPO_RESERVA_TEMP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FAVORECIDO_TROCO_SERV'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_NUM_FILA_MANUAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PLANO_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRAZO_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STEP_DIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PLANO_OURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRAZO_OURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STEP_OURO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_PLANO_PRATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRAZO_PRATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STEP_PRATA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_TAG_CLIENTE_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'LIMPAR_FILA_DA_VEZ'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'FI_IMPOSTOS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MINIMO_R_VENDEDOR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_VISITA_EXTERNA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CANAL_MIDIA_SOCIAL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TERMOMETRO_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TDRIVE_MUDA_TERMOMETRO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXIGE_TERMOMETRO_ACAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CONVERTER_MONTADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BALCAO_TIPO_EVENTO_LOJA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_COMISSAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'BALCAO_TIPO_EVENTO_FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_OBRIGAR_FONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_OBRIGAR_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CRMPARTS_USA_RESGATE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_RESGATE_TEMPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CP_GERENTE_LOJA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'USA_ENTREGA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_TERMOMETRO_DEFAULT'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EVB_RESGATE_TIME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_APR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_A'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_B'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_C'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'RESERVA_ADICIONAL_D'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_INTEGRADORA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_TIPO_EVENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_LINK'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_KEY_SECRET'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ULTIMA_CHECAGEM_WHATSAPP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_TIME_ZONE'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_OBRIGATORIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_PECA_A_LIBERA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_VALOR_MINIMO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CAUCAO_PERCENT_VENDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftDecimal
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_CLASSE_TIPO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'COD_TIPO_EVENTO_CRMPARTS'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CREDLINE_AUTO_ATUALIZAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_USERNAME'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_SENHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_API_KEY'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_TIPO_HOST'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_HOST_ENTRADA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_HOST_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_PORTA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_PORTA_SAIDA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EMAIL_USA_SSL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MOTIVO_PERDA_CAMPANHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_EMAIL_ORCAMENTO_EMAIL'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TEMPLATE_EMAIL_ORCAMENTO_ZAP'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'WHATSAPP_LINK_ORCAMENTO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_PARM_FLUXO'
    Cursor = 'CRM_PARM_FLUXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;31003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmailFluxoAnexo: TFTable
    FieldDefs = <
      item
        Name = 'SEQ_ID_MAIL_FLUXO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_MAIL_ANEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ANEXO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftBinary
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'NOME_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EXTENSAO_ARQUIVO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_FLUXO_ANEXO'
    Cursor = 'CRM_EMAIL_FLUXO_ANEXO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '340042;31004'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
