object TemplatesRN: TFDataModule
  OldCreateOrder = False
  WOwner = FrWizard
  WOrigem = EhMain
  WKey = '440092'
  Height = 299
  Width = 442
  object scCrmEmailModelo: TFSchema
    Tables = <
      item
        Table = tbEmailModelo
        GUID = '{FF8A8F81-468A-4272-A436-A2679D65188C}'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Table = tbNbsapiMessageTemplate
        GUID = '{B16AF527-4953-4663-B2A6-5E646DA3D013}'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    WOwner = FrWizard
    WOrigem = EhNone
  end
  object tbEmailModelo: TFTable
    FieldDefs = <
      item
        Name = 'ID_EMAIL_MODELO'
        Calculated = False
        Updatable = True
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Email Modelo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MODELO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Modelo*'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'QUEM_CRIOU'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Quem Criou'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'PRIVADO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Privado'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'APLICACAO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Aplica'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DEPARTAMENTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Departamento'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ASSUNTO'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Assunto*'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TP_ARQUIVO_ANEXO_MENSAGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tipo Arquivo Anexo'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'MENSAGEM'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftText
        JSONConfig.NullOnEmpty = False
        Caption = 'Mensagem'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_TEMPLATE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Template(Zenvia)'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'ID_NBSAPI_MESSAGE_TEMPLATE'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Nbsapi Message Template'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'STATUS_ZENVIA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Status Zenvia'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_CHAT'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = #201' Template de Chat?'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'CRM_EMAIL_MODELO'
    TableName = 'CRM_EMAIL_MODELO'
    Cursor = 'CRM_EMAIL_MODELO'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440092;44002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbEmailModeloTag: TFTable
    FieldDefs = <
      item
        Name = 'ID_TAG'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Tag'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'TAG'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Tag'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_EMAIL_MODELO_TAG'
    Cursor = 'CRM_EMAIL_MODELO_TAG'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440092;44003'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbCadastroWhatsapp: TFTable
    FieldDefs = <
      item
        Name = 'ID_CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Celular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'DESCRICAO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Descri'#231#227'o'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'CELULAR'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Celular'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_SENHA'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Senha'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'API_USUARIO'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Api Usu'#225'rio'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'X_API_TOKEN'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'X Api Token'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'URL_API'
        Calculated = False
        Updatable = False
        PrimaryKey = False
        FieldType = ftString
        JSONConfig.NullOnEmpty = False
        Caption = 'Url Api'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    TableName = 'CRM_CADASTRO_WHATSAPP'
    Cursor = 'CRM_CADASTRO_WHATSAPP'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440092;46001'
    DeltaMode = dmChanged
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
  object tbNbsapiMessageTemplate: TFTable
    FieldDefs = <
      item
        Name = 'ID_NBSAPI_MESSAGE_TEMPLATE'
        Calculated = False
        Updatable = False
        PrimaryKey = True
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = 'Id. Nbsapi Message Template'
        WOwner = FrWizard
        WOrigem = EhNone
      end
      item
        Name = 'EH_CHAT'
        Calculated = False
        Updatable = True
        PrimaryKey = False
        FieldType = ftInteger
        JSONConfig.NullOnEmpty = False
        Caption = #201' Chat'
        WOwner = FrWizard
        WOrigem = EhNone
      end>
    UpdateTable = 'NBSAPI_MESSAGE_TEMPLATE'
    TableName = 'NBSAPI_MESSAGE_TEMPLATE'
    Cursor = 'NBSAPI_MESSAGE_TEMPLATE'
    MaxRowCount = 200
    WOwner = FrWizard
    WOrigem = EhNone
    WKey = '440092;46002'
    DeltaMode = dmAll
    RatioBatchSize = 20
    ModelType = mtCollectionModel
  end
end
