<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>nbs-front-end</groupId>
    <artifactId>crmservice</artifactId>
    <version>********.L35501</version>
    <description>Módulo web para gestão de oficina</description>
    <name>crmservice</name>


    <properties>
        <endorsed.dir>${project.build.directory}/endorsed</endorsed.dir>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>windows-1252</project.build.sourceEncoding>
        <jakartaee>8.0</jakartaee>
    </properties>


    <parent>
        <groupId>br.com.nbs.freedom</groupId>
        <artifactId>freedom-frontend-starter</artifactId>
        <version>********</version>
    </parent>


    <dependencies>
        <dependency>
            <groupId>nbs-shared</groupId>
            <artifactId>cursores-zk</artifactId>
            <version>**********.L35501</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>nbs-shared</groupId>
            <artifactId>nbs-empresa-zk</artifactId>
            <version>**********.L35501</version>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>nbs-shared</groupId>
            <artifactId>encrypt-nbs</artifactId>
            <version>1.0.0.3.62341</version>
            <type>jar</type>
        </dependency>

        <dependency>
            <groupId>nbs-shared</groupId>
            <artifactId>nbs-util-zk</artifactId>
            <version>1.11.0.244.L35501</version>
        </dependency>
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>6.4.1</version>
        </dependency>

        <dependency>
            <groupId>barbecue</groupId>
            <artifactId>barbecue</artifactId>
            <version>1.1</version>
        </dependency>


        <!-- IMPORTANTE: Não remova os comentários  BEGIN-REPLACE-PROFILE e END-REPLACE-PROFILE -->
        <!-- Eles são metadados que definem a parte do POM.XML que é alterada automaticamente no momento -->
        <!-- da compilação -->

        <!--[Profile-Payara]-->

        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <version>${jakartaee}</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-server</artifactId>
            <version>2.30.1</version>
        </dependency>

    </dependencies>

    <packaging>war</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <webXml>src/main/resources/static/WEB-INF/web.xml</webXml>
                    <webResources>
                        <resource>
                            <directory>src/main/resources/META-INF/</directory>
                            <targetPath>META-INF</targetPath>
                            <includes>
                                <include>Version.xml</include>
                            </includes>
                        </resource>
                        <resource>
                            <directory>src/main/resources/static/WEB-INF/</directory>
                            <targetPath>WEB-INF</targetPath>
                            <includes>
                                <include>sun-jaxws.xml</include>
                                <include>zk.xml</include>
                            </includes>
                        </resource>
                        <resource>
                            <directory>target/classes/static/</directory>
                            <targetPath>/</targetPath>
                            <excludes>
                                <exclude>WEB-INF/</exclude>
                            </excludes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.google.code.maven-replacer-plugin</groupId>
                <artifactId>maven-replacer-plugin</artifactId>
                <version>1.3.7</version>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>replace</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includes>
                        <include>target/classes/static/css/freedom.css</include>
                    </includes>
                    <regex>false</regex>
                    <token>/${artifactId}/images/</token>
                    <value>/images/</value>
                </configuration>
            </plugin>

            <!--[END-REPLACE-PROFILE]-->

        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
