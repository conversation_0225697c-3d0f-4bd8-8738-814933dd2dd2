        <!--[Profile-Payara]-->

        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <version>${jakartaee}</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-server</artifactId>
            <version>2.30.1</version>
        </dependency>

    </dependencies>

    <packaging>war</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <webXml>src/main/resources/static/WEB-INF/web.xml</webXml>
                    <webResources>
                        <resource>
                            <directory>src/main/resources/META-INF/</directory>
                            <targetPath>META-INF</targetPath>
                            <includes>
                                <include>Version.xml</include>
                            </includes>
                        </resource>
                        <resource>
                            <directory>src/main/resources/static/WEB-INF/</directory>
                            <targetPath>WEB-INF</targetPath>
                            <includes>
                                <include>sun-jaxws.xml</include>
                                <include>zk.xml</include>
                            </includes>
                        </resource>
                        <resource>
                            <directory>target/classes/static/</directory>
                            <targetPath>/</targetPath>
                            <excludes>
                                <exclude>WEB-INF/</exclude>
                            </excludes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.google.code.maven-replacer-plugin</groupId>
                <artifactId>maven-replacer-plugin</artifactId>
                <version>1.3.7</version>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>replace</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includes>
                        <include>target/classes/static/css/freedom.css</include>
                    </includes>
                    <regex>false</regex>
                    <token>/${artifactId}/images/</token>
                    <value>/images/</value>
                </configuration>
            </plugin>